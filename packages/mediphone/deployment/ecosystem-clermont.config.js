const MEDIFRONT_GIT = '*****************:mediboxteam/medifront.git'
const HOST = 'medibox-clermont.fr'
const DOMAIN = HOST
const USER = 'root'
const PATH = `/${USER}/medifront`
const PUBLIC_PATH = 'elearning'
const DEPLOYMENT_FOLDER  = `/var/www/html/${PUBLIC_PATH}`

module.exports = {
  deploy : {
    production : {
      user : USER,
      host : HOST,
      ref  : 'origin/master',
      repo : MEDIFRONT_GIT,
      path : PATH,
      "env" : {
        "NODE_ENV": "production"
      },
      "ssh_options": ["StrictHostKeyChecking=no"],
      'pre-deploy': `git reset --hard ; mkdir -p ${DEPLOYMENT_FOLDER} `, // prévient les erreurs de pull
      'post-deploy' : `rm -rf ${DEPLOYMENT_FOLDER}/* ; cp -rp ${PATH}/current/dist/* ${DEPLOYMENT_FOLDER} && echo Deployment CLERMONT is DONE.`,
    },
  }
};
