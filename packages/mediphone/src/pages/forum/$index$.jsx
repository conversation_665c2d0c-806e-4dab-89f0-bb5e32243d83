import Commentaires from '@/shared/components/Commentaires/Commentaires.jsx'
import FullMediParticlesBreadCrumb from '@/shared/components/FullMediParticlesBreadCrumb.jsx'
import { QUERY_FORUM_CATEGORY } from '@/shared/graphql/forum.js'
import { EditCreateForumCategoryModal } from '@/shared/pages/forum/components/EditCreateForumCategoryModal.jsx'
import { ForumBreadCrumb } from '@/shared/pages/forum/components/ForumBreadcrumb.jsx'
import { ForumCard } from '@/shared/pages/forum/components/ForumCard.jsx'
import { ForumCategory } from '@/shared/pages/forum/components/ForumCategory.jsx'
import { ForumSubjects } from '@/shared/pages/forum/components/ForumSubjects.jsx'
import { LatestPosts } from '@/shared/pages/forum/components/LatestPosts.jsx'
import { CommentairesType } from '@/shared/services/commentaires.js'
import { ForumType } from '@/shared/services/forum.js'
import { isAdmin } from '@/shared/utils/authority.js'
import { useEffectScrollTop } from '@/shared/utils/hooks/useEffectScrollTop.js'
import { useQuery } from '@apollo/client'
import React, { useState } from 'react'
import { Button, Col, Row } from 'antd'
import { useTranslation } from 'react-i18next';
import { ExoPullToRefresh } from '../../components/ExoPullToRefresh.jsx'

export default function(props) {
  useEffectScrollTop()
  const forumId = props.match.params.forumId || null
  const type = props.match.params.type || ForumType.FORUM
  const typeId = props.match.params.typeId || null

  const isForum = type === ForumType.FORUM
  const matieresActivated = true

  const {t} = useTranslation();
  const { loading, error, data, refetch } = useQuery(QUERY_FORUM_CATEGORY, {
    fetchPolicy: 'no-cache', variables: {
      parentId: forumId,
    },
  })

  const getForumCategories = () => data && data.forumCategories && data.forumCategories.categories
  const getForumParents = () => data && data.forumCategories && data.forumCategories.parents

  const [createForumCategoryVisible, setCreateForumCategoryVisible] = useState(false)
  const closeModalHandler = () => {
    setCreateForumCategoryVisible(false)
    refetch() // Load new modifications
  }

  const openModalHandler = () => {
    setCreateForumCategoryVisible(true)
  }

  const breadCrumbName = () => {
    if (getForumParents()) {
      const [lastForum] = getForumParents().slice(-1)
      return (lastForum && lastForum.name) || 'Forum'
    }
    return ''
  }

  const renderAdminCreateForumCategory = () => (
    <>
      <Button
        style={{ minHeight: '25px', marginBottom: 16 }}
        type="dashed"
        block
        onClick={() => {
          setCreateForumCategoryVisible(true)
        }}
      >
        {t('AddCategory')}
      </Button>
      <EditCreateForumCategoryModal
        closeModalHandler={closeModalHandler}
        isVisible={createForumCategoryVisible}
        modalType="CREATE"
        refetch={refetch}
        loading={loading}
        forumId={forumId}
      />
    </>
  )

  return (
    <ExoPullToRefresh onRefresh={async () => {
      await refetch()
    }}>
      <FullMediParticlesBreadCrumb title={breadCrumbName()}/>
      <React.Fragment>
        <ForumBreadCrumb type={type} typeId={typeId} forumId={forumId} parents={getForumParents()}/>
        &nbsp;
        {isForum && isAdmin() && renderAdminCreateForumCategory()}
        <Row justify="left" gutter={(MOBILE === 1) ? 0 : [1, 1]} type="flex" key="1">
          <Col xl={18} lg={18} md={18} sm={24} xs={24}>
            {isForum && getForumCategories() && getForumCategories().map((forumCategory) => (
              <ForumCategory
                type={ForumType.FORUM}
                key={forumCategory.id}
                forumCategory={forumCategory}
                refetch={refetch}
              >
                {forumCategory?.forums && forumCategory?.forums.map(forum => (
                  <ForumCard forum={forum} key={forum.id}/>
                ))}
              </ForumCategory>
            ))}

            {/* root matieres*/}
            {/*
            {matieresActivated && !forumId && (
              <UEForums forumId={forumId} type={type} typeId={typeId}/>
            )}
            */}

            {forumId && (
              <ForumSubjects>
                <Commentaires
                  isForum
                  id={forumId}
                  type={CommentairesType.FORUM}
                  refetch={refetch}
                />
              </ForumSubjects>
            )}
          </Col>

          <Col xl={6} lg={6} md={6} sm={24} xs={24}>
            <LatestPosts forumId={forumId} type={type} typeId={typeId}/>
          </Col>
        </Row>
      </React.Fragment>
    </ExoPullToRefresh>
  )
}
