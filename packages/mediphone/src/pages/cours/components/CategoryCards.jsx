import { GlobalContext } from '@/shared/layouts/BlankLayout.jsx';
import { CategoryCard } from '@/shared/pages/cours/components/PedagogicalHierarchyElements/CategoryCard.jsx';
import { Button, Row, Spin } from 'antd'
import { isAdmin } from '@/shared/utils/authority'
import { useQuery } from '@apollo/client'
import { QUERY_UE_CATEGORIES_FOR_UE } from '@/shared/graphql/cours.js'
import { ErrorResult } from '@/shared/components/ErrorResult'
import { TuteursReferentCard } from '@/shared/pages/cours/components/TuteurReferent'
import React, { useContext, useState } from 'react';
import { EditCreateCategoryModal } from '@/shared/pages/cours/components/EditCategorieCardModal'
import { PlusCircleTwoTone } from '@ant-design/icons'
import { useTranslation } from 'react-i18next';

export const CategoriesCards = ({ ue, ueId }) => {
  const {t} = useTranslation();
  const { listOrCardNavigation } = useContext(GlobalContext);
  const { loading, error, data, refetch } = useQuery(QUERY_UE_CATEGORIES_FOR_UE,
    {
      fetchPolicy: 'cache-and-network',
      variables: { ueId },
    })

  const tuteursBlock = () => (
    <TuteursReferentCard
      tuteurs={!error && ue && ue.tuteurs}
      color={ue?.color}
      color2={ue?.color2}
      title={t('ReferentTeacher')}
    />
  )

  const [createVisible, setCreateVisible] = useState(false)

  const ueCategoryReady = () => !loading && !error && data && data.ueCategories

  const renderCreateCategory = (
    <div style={{ width: '100%', display: 'flex', justifyContent: 'center' }}>
      <div style={{ width: '100%', margin:12 }}>
        <Button
          style={{ marginTop: 16, minHeight: '374px' }}
          type="dashed"
          block
          onClick={() => {
            setCreateVisible(true)
          }}
        >
          <PlusCircleTwoTone style={{ fontSize: '2.8rem' }}/>
          <br/>
          {t('general.Create')}
        </Button>
        <EditCreateCategoryModal
          isVisible={createVisible}
          modalType="CREATE"
          closeModalHandler={() => {
            setCreateVisible(false)
            refetch()
          }}
          ueId={ueId}
        />
      </div>
    </div>
  )

  const renderCategories = () => data?.ueCategories?.map((category, index) => (
    <React.Fragment key={index}>
      {(category.isVisible || (isAdmin())) && (
        <div style={listOrCardNavigation === 'List' ? { width: '100%' } : {}}> {/* width: '100%', margin: 12 */}
          <CategoryCard
            category={category}
            name={category.name}
            categoryId={category.id}
            image={category.image}
            views="0"
            loading={loading}
            ueId={ueId}
            refetch={refetch}
            size="large"
            color={ue && ue.color}
            color2={ue && ue.color2}
            description={category.description}
            order={category.order}
            isVisible={category.isVisible}
            countAccessiblesCourses={category?.countAccessiblesCourses}
            countAccessiblesExercises={category?.countAccessiblesExercises}
          />
        </div>
      )}
    </React.Fragment>
  ))

  return (
    <Row justify="center" align="top" type="flex" key="1">
      {/* LOADING */}
      {loading && !data && (
        <Spin/>
      )}
      {/* DONE LOADING */}
      {ueCategoryReady() && (
        <div style={{
          display: 'flex',
          alignContent: 'center',
          margin: 12,
          justifyContent: 'center',
          width: '100%',
        }}>
          {!error && ue && ue.tuteurs && ue.tuteurs.length > 0 && (
            <div style={{ width: '100%' }}>
              {tuteursBlock()}
            </div>
          )}
        </div>
      )}

      <div style={
        listOrCardNavigation === 'Card'
          ? {
            display: 'flex',
            alignContent: 'center',
            margin: 'auto',
            gap: '30px',
            flexWrap: 'wrap',
            justifyContent: 'center',
          } : {
            display: 'flex',
            flexWrap: 'wrap',
            alignContent: 'center',
          }}>
        {ueCategoryReady() && renderCategories()}
      </div>

      {/* ERROOOOR :'( */}
      {!loading && error ? (<ErrorResult refetch={refetch} error={error}/>) : ''}

      {/* CREATE CATEGROY */}
      {isAdmin() && renderCreateCategory}
    </Row>
  )
}
