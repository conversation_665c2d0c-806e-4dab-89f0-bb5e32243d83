# Exoteach Web

## Local config
See localDevConfig in `config/config.js`

## App routes
In `config/routes.js`

## Custom configs
See https://v2.umijs.org/config/

## Environment Prepare

Install `node_modules`:
```bash
npm install
```
## Provided Scripts

### Start project (development)

```bash
npm start
```

### Build project
```bash
npm run build
```

### Deploy
- Auto DEPLOY PROD (build project, commit, push, deploy)
```
npm run autoDeployProd
```


#### Autre
- Setup new deployment (first time only) 
```
pm2 deploy <deployment/ecosystem.js> production setup
```

https://pm2.keymetrics.io/docs/usage/deployment/


### Check code style

```bash
npm run lint
```

You can also use script to auto fix some lint error:

```bash
npm run lint:fix
```

### Test code

```bash
npm test
```

### LOCAL CONFIG
```
  dev: {
    baseWebsiteUrl: 'http://localhost/medibox/', // DOIT FINIR PAR '/'
    serverUrl: 'http://localhost:8000/',
    graphqlServerUrl: 'http://localhost:8000/graphql',
    webSocketsGraphqlServerUrl: 'ws://localhost:8000/graphql',
    qcmBase: 'http://localhost/medibox/qcm/', // NON utilisé pour le momemnt
    publicPath: '/'
  },
```