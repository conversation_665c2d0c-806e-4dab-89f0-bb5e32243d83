import Progress from 'antd/es/progress';
import Tag from 'antd/es/tag';
import React from 'react';
import { useTranslation } from 'react-i18next';

export const StatsItemsVusFaitsUE = ({ myProgression }) => {
  const { t } = useTranslation();
  const percentageCoursVus = ((myProgression.coursVus / myProgression.totalCours) * 100).toFixed(2);
  const percentageQcmFaits = ((myProgression.qcmFaits / myProgression.totalQcm) * 100).toFixed(2);
  return (
    <>
      <div style={{ width: '50%', margin: 'auto' }}>
        {percentageCoursVus && (
          <div>
            <Tag color="volcano">{t('CourseSeen')}</Tag>
            <div>
              <Progress
                strokeColor={{
                  '0%': '#108ee9',
                  '100%': '#87d068'
                }}
                percent={percentageCoursVus}
              />
            </div>
          </div>
        )}

        {percentageQcmFaits && (
          <div>
            <Tag color="blue">{t('ExercicesDone')}</Tag>
            <div>
              <Progress
                strokeColor={{
                  '0%': '#108ee9',
                  '100%': '#87d068'
                }}
                percent={percentageQcmFaits}
              />
            </div>
          </div>
        )}

        {/*
          <div>
            <Tag color="volcano">Annales vues</Tag>
            <div>
              <Progress
                strokeColor={{
                  '0%': '#108ee9',
                  '100%': '#87d068',
                }}
                percent={50}
              />
            </div>
          </div>
        */}
      </div>
    </>
  );
};
