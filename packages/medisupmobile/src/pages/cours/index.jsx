import { GlobalContext } from '@/shared/layouts/BlankLayout.jsx';
import { useEffectScrollTop } from '@/shared/utils/hooks/useEffectScrollTop.js';
import React, { useContext, useEffect, useRef } from 'react';
import { Divider, Float<PERSON><PERSON>on, Menu } from 'antd';
import { useTranslation } from 'react-i18next';
import { router } from 'umi';
import { UECards } from '@/pages/cours/components/UECards';
import { useQuery } from '@apollo/client';
import { GET_MY_GROUPS } from '@/shared/graphql/user';
import FullMediParticlesBreadCrumb from '@/shared/components/FullMediParticlesBreadCrumb';
import { CoursesToolBar } from '@/shared/pages/cours/components/CoursesToolBar';
import { QUERY_UE_ID } from '@/shared/graphql/cours';
import { isAdmin } from '@/shared/utils/authority';
import { UE_TYPES } from '@/shared/services/ues';
import { Plus } from 'lucide-react';

export default function(props) {
  useEffectScrollTop();
  const { t } = useTranslation();
  // todo reuse prefetched user data from baselayout
  const { data, loading, error } = useQuery(GET_MY_GROUPS, { fetchPolicy: 'no-cache' });
  const [isFormation, setIsFormation] = React.useState(ue?.type === UE_TYPES.FORMATION || false);

  const handleMenuClick = e => {
    if (e.key === 'permissions') {
      router.push('/admin/permissions');
    } else if (e.key === 'addCours') {
      router.push('/create/cours');
    }
  };

  const adminMenu = (
    <Menu onClick={handleMenuClick}>
      <Menu.Item key="permissions">{t('CoursesPermissions')}</Menu.Item>
      <Menu.Item key="addCours">{t('createCourse')}</Menu.Item>
    </Menu>
  );

  const {
    navigationStyle,
    setNavigationStyle,
    globalBannerText,
    setGlobalBannerText,
    globalBannerSubtitle,
    setGlobalBannerSubtitle,
    coursId,
    setCoursId,
    ueId,
    setUeId,
    categoryId,
    setCategoryId,
    breadCrumbImage,
    setBreadCrumbImage,
    breadCrumbImageType,
    setBreadCrumbImageType,
    listOrCardNavigation,
    setListOrCardNavigation
  } = useContext(GlobalContext);

  useEffect(() => {
    // Reset
    setGlobalBannerText('Tous mes cours');
    setGlobalBannerSubtitle('');
    setCoursId(null);
    setUeId(null);
    setCategoryId(null);
    setBreadCrumbImage(null);
    // setBreadCrumbImageType(undefined)
    setBreadCrumbImageType('fileCours');
  }, []);

  const handleSegmentedControlChange = value => {
    setListOrCardNavigation(value);
  };

  const [isStickyToolbar, setIsStickyToolbar] = React.useState(false);
  const [isToolbarInScreen, setIsToolbarInScreen] = React.useState(false);

  const stickyRef = useRef(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsToolbarInScreen(entry.isIntersecting);
      },
      {
        root: null, // viewport
        threshold: 0.01 // dès qu'un pixel sort de l'écran
      }
    );

    if (stickyRef.current) {
      observer.observe(stickyRef.current);
    }

    return () => {
      if (stickyRef.current) {
        observer.unobserve(stickyRef.current);
      }
    };
  }, []);

  // Check if we are in formation - In formation, we don't show the toolbar
  const { dataUe } = useQuery(QUERY_UE_ID, {
    variables: { id: ueId },
    fetchPolicy: 'cache-and-network',
    skip: !ueId
  });
  const ue = dataUe?.ue;

  // Notify parent component about formation status
  useEffect(() => {
    setIsFormation(isFormation);
  }, [isFormation, setIsFormation]);

  const showCoursesToolBar = !coursId && isAdmin() && !isFormation;

  return (
    <>
      <FullMediParticlesBreadCrumb
        title={globalBannerText}
        subtitle={globalBannerSubtitle}
        image={breadCrumbImage}
        imageType={breadCrumbImageType}
      />
      {/* Bouton voir mon abonnement */}
      {/*
      <Link to="/account/forfait">
        <Button type="ghost" style={{ margin: '10px' }}>
          {t('SeeMySubscription')}
        </Button>
      </Link>
      */}

      <div style={{ width: '100%' }}>
        {showCoursesToolBar && (
          <>
            {(!isToolbarInScreen || isStickyToolbar) && (
              <FloatButton
                shape="circle"
                type="primary"
                style={{ insetInlineEnd: 24, width: 60, height: 60 }}
                description={
                  <Plus
                    style={{
                      fontSize: 42,
                      transition: 'all .3s ease-in-out',
                      transform: `rotate(${isStickyToolbar ? 45 : 0}deg)`
                    }}
                  />
                }
                onClick={() => {
                  setIsStickyToolbar(!isStickyToolbar);
                }}
              />
            )}
            <div
              ref={stickyRef}
              style={{
                position: isStickyToolbar ? 'sticky' : 'static',
                top: isStickyToolbar ? 44 : 'auto',
                backgroundColor: '#f5f5f5',
                opacity: isToolbarInScreen || isStickyToolbar ? 1 : 0,
                transform:
                  isToolbarInScreen || isStickyToolbar ? 'translateY(0)' : 'translateY(-100%)',
                pointerEvents: isToolbarInScreen || isStickyToolbar ? 'auto' : 'none',
                transition: 'all .3s ease-in-out',
                padding: 24,
                borderBottom: '1px solid #d9d9d9',
                zIndex: 1000
              }}
            >
              <CoursesToolBar ueId={ueId} categoryId={categoryId} coursId={coursId} />
            </div>
          </>
        )}

        <UECards />
      </div>
      <Divider />
    </>
  );
}
