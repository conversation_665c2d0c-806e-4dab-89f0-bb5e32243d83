import { GlobalConfig, IS_DEV } from '@/shared/utils/utils.js';
import { useState } from 'react';
import useSessionStorage from '@/shared/hooks/useSessionStorage.js';
import dayjs from 'dayjs';

export const formatAnnee = (annee) => {
  if (!isNaN(annee)) {
    return `${annee}-${parseInt(annee, 10) + 1}`;
  }
  return annee;
};

// Initialise un filter par default (pas mal si on veut vérif la cohérence des données avec l'ancienne data et la nouvelle
const defaultFilter = {
  ueIds: null, // Conditionnellement aux droits de l'user => Doit être déterminé au chargement du componant de sélection des UE,
  userIds: null, // Peut-être vide à l'init
  anneesSelected: null, // Conditionnellement au moment t => Doit être déterminé au chargement du componant pour select all années
  deleted: false, // Par default à false
  titre: null, // Par default, pas de filtration sur le titre
  dateCreationStart: null,
  dateCreationEnd: null,
  dateLastModifStart: null,
  dateLastModifEnd: null,
  typeQcm: null // Par default à null, => Doit être déterminé au chargement du componant
};

export const initQcmSearchFilter = () => {
  /* Fonction qui retourne un react Hook du searchQcmFilter depuis le sessionStorage. Si présent, récupère, si absent, init
   * /!\ Faire attention à la sync (update)
   * /!\ Faire attention à l'update de droit (accès à des UE auquel l'user n'a plus les droits
   * */
  return useSessionStorage('localQcmFilter', defaultFilter);
};

export const resetQcmSearchFilter = () => {
  window.sessionStorage.setItem('localQcmFilter', JSON.stringify(defaultFilter));
  return JSON.parse(window.sessionStorage.getItem('localQcmFilter'));
};

export const typeCoursSelecterExerciceFilter = {
  select_Id_cours: 'cours',
  exercices_without_cours: 'coursless',
  exercice_linked_to_deleted_cours: 'deletedcours'
};

// /!\ => chaque nom doit être unique
export const formatExerciceObjectReference = [
  { name: 'Texte libre', id: 1 },
  { name: 'QCU', id: 2 },
  { name: 'QCM', id: 3 },
  { name: 'Alphanumérique', id: 4 },
  { name: 'Numérique', id: 5 },
  { name: 'Texte à trous', id: 6 },
  { name: 'Schémas Point&Click', id: 7 },
  { name: 'Schémas légendes à remplir', id: 8 },
  { name: 'flashcard', id: 9 }
];

const defaultExerciceFilter = {
  limit: 20,
  offset: 0,

  coursPointer: typeCoursSelecterExerciceFilter.select_Id_cours,
  linkCoursIds: null,

  isPublished: null, // isPublished ? : (True/False/None)

  typeIds: null, // Exercices Types

  formatExercices: formatExerciceObjectReference.map((value) => value.name),

  dateCreationBegin: dayjs().subtract(1, 'year').toDate(), // Date or None
  dateCreationEnd: dayjs().add(1, 'day').toDate(), // Date or None
  dateModifBegin: dayjs().subtract(1, 'year').toDate(), // Date or None
  dateModifEnd: dayjs().add(1, 'day').toDate(), // Date or None

  name: '',
  userIds: null,
  seriesFiltration: false,
  seriesComparisonSign: '==',
  seriesComparisonNumber: 0
};

export const initExerciceFilter = () => {
  /* Fonction qui retourne un react Hook du searchQcmFilter depuis le sessionStorage. Si présent, récupère, si absent, init
   * /!\ Faire attention à la sync (update)
   * /!\ Faire attention à l'update de droit (accès à des UE auquel l'user n'a plus les droits
   * */
  return useSessionStorage('localExerciceFilter', defaultExerciceFilter);
};

export const resetExerciceFilter = () => {
  window.sessionStorage.setItem('localExerciceFilter', JSON.stringify(defaultExerciceFilter));
  return JSON.parse(window.sessionStorage.getItem('localExerciceFilter'));
};

const SMART_MCQ_QUESTIONS_NUMBER_AND_MAX_GRADE = 20;

export const getQuestionsNumberFromQcm = (qcm) => {
  return qcm?.questionPickingStrategy === 'smart'
    ? SMART_MCQ_QUESTIONS_NUMBER_AND_MAX_GRADE
    : qcm?.questions?.length || qcm?.nombreQuestions;
};

export const getMaxPointsFromQcm = (qcm) => {
  if (qcm?.questionPickingStrategy === 'smart') {
    return SMART_MCQ_QUESTIONS_NUMBER_AND_MAX_GRADE;
  }
  if (qcm?.maximumPoints) {
    return qcm?.maximumPoints;
  }
  return getQuestionsNumberFromQcm(qcm);
};

export const getGradeOutOf20 = (grade, realNumberOfQuestions) => {
  return ((grade / realNumberOfQuestions) * 20)?.toFixed(2);
};

/**
 * Marche si number est un nombre ou une chaine de caractère représentant un nombre
 * @param number
 * @returns {number}
 */
export const getCleanFloatNumber = (number) => {
  //return parseFloat(parseFloat(number)?.toFixed(2));
  const parsedNumber = parseFloat(number);
  if (isNaN(parsedNumber)) {
    return 0; // Gère le cas où le nombre n'est pas valide
  }
  const threshold = 1e-10; // Seuil pour traiter les petits nombres comme zéro
  if (Math.abs(parsedNumber) < threshold) {
    return 0;
  }
  return parseFloat(parsedNumber.toFixed(2));
};

export const QuestionOrder = {
  DEFAULT: 'DEFAULT',
  SESSION_DEFINED: 'SESSION_DEFINED',
  BY_CATEGORY: 'BY_CATEGORY',
  BY_GOOD_ANSWERS: 'BY_GOOD_ANSWERS',
  BY_BAD_ANSWERS: 'BY_BAD_ANSWERS'
};

export const CHRONO_BY_QUESTION_OR_GLOBAL = {
  GLOBAL: 'globalTime',
  BY_QUESTION: 'timeByQuestion'
};

export const extractAnneesFromQcms = (qcms) => {
  if (qcms && qcms.length > 0) {
    return [...new Set(qcms.map((qcm) => qcm.annee))];
  }
  return null;
};
/* Map form type (answers and question ids) to model type, returns Array */
export const mapFormToQuestionsModel = (values) => {
  const reponses = [];
  Object.keys(values).forEach((questionId) => {
    reponses.push({
      id_question: parseInt(questionId, 10),
      answers: values[questionId],
      certainty: parseInt(values.certainty, 10)
    });
  });
  return reponses;
};

// Values from form + values from context
export const mapFormToQuestionsModel_V2 = (values, contextValues) => {
  const reponses = [];
  /* adaptater qui va se brancher soit sur values (le formulaire des questions) si values.length > 0 , soit sur contextValue
   *
   * => En effet, dans le cas des flashcard on utilise pas le form mais le contextValues.
   *
   * */
  const source =
    Object.keys(values).length > 0
      ? values
      : contextValues.reduce((acc, q) => {
          acc[q.id_question] = q.id_question;
          return acc;
        }, {});

  Object.keys(source).forEach((questionId) => {
    // Retrouver la question dans le context (userChoicesByQuestion)
    const currentQuestion = contextValues?.find(
      (q) => String(q.id_question) === String(questionId)
    );
    if (IS_DEV) {
      if (!currentQuestion) {
        console.log('values of questionId for missing items', values?.[questionId]);
      }
    }
    // Pour alphanum, la valeur est pas dans currentQuestion, mais dans values, et elle ne doit pas être undefined
    const answerFromForm = values?.[questionId] !== undefined ? values?.[questionId] : undefined;

    reponses.push({
      id_question: parseInt(questionId, 10),
      answers: currentQuestion?.answers || answerFromForm,
      answers_false: currentQuestion?.answers_false || undefined,
      jsonAnswers: currentQuestion?.jsonAnswers || undefined, // Pour les questions type schema, texte à trou, etc.
      certainty:
        (values.certainty != null ? parseInt(values.certainty, 10) : null) ??
        (currentQuestion != null ? parseInt(currentQuestion?.certainty, 10) : null) ??
        null
    });
  });
  return reponses;
};

export const mapContextAnswerToFormValues = (contextValues) => {
  /*
  Structure:
  [{"answers": ["64708", "64710"], "id_question": "13298", "answers_false": []}, {"answers": ["64713", "64715"], "id_question": "13299", "answers_false": []}, {"answers": ["64703", "64705", "64707"], "id_question": "13297", "answers_false": []}]
  */
  const mappedValues = {};
  contextValues.forEach((q) => {
    mappedValues[q.id_question] = q.answers;
  });
  return mappedValues;
};
/* Map questions & answers history model type to form type for display */
export const mapQuestionsModelToForm = (questions) => {
  let mappedValues = {};
  questions.forEach((q) => {
    if (q.answerHistory && q.answerHistory.answersData) {
      if (q.isCheckbox) {
        // Doit ajouter seulement les items cochés (le back retourne seulement les items cochés dans answersData)
        mappedValues = { ...mappedValues, [q.id_question]: q.answerHistory.answersData };
      } else {
        // Radio buttons or free text
        mappedValues = { ...mappedValues, [q.id_question]: q.answerHistory.answersData[0] };
      }
      if (IS_DEV) {
        //console.log({answerHistory: q.answerHistory});
      }
    }
  });
  if (IS_DEV) {
    //console.log({mappedValues});
  }
  return mappedValues;
};

export const groupByKey = (array, key) => {
  return array.reduce((hash, obj) => {
    if (obj[key] === undefined) return hash;
    return Object.assign(hash, { [obj[key]]: (hash[obj[key]] || []).concat(obj) });
  }, []);
};

// eslint-disable-next-line no-undef
export const buildQcmImage = (url_image_q) => `${GlobalConfig.get().baseWebsiteUrl}${url_image_q}`;

export const getLatestYear = (annees) => {
  if (annees) {
    return Math.max(...annees);
  }
  return null;
};

export const filterQcmsByAnnee = (qcms, annee) => qcms.filter((qcm) => qcm.annee === annee);

export const getLastSelectedYear = () => {
  return localStorage.getItem('qcmLSY') || null;
};
export const setLastSelectedYear = (year) => {
  localStorage.setItem('qcmLSY', year);
};
export const setLastSelectedYearForQcm = (year) => {
  localStorage.setItem('qcmLSYsearch', year);
};
export const getLastSelectedYearForQcm = () => {
  return localStorage.getItem('qcmLSYsearch');
};

export const getLastSelectedUE = () => {
  return localStorage.getItem('LSUE') || undefined;
};
export const setLastSelectedUE = (ue) => {
  localStorage.setItem('LSUE', ue);
};

export const getNormalizedRankingForResults = (_scores, gradeAttribute = 'note') => {
  const scores = _scores;
  if (scores && scores[0]) {
    scores[0].classement = 1;
  } else {
    return scores;
  }
  for (let i = 1; i < scores?.length; i++) {
    if (scores[i]?.[gradeAttribute] === scores[i - 1]?.[gradeAttribute]) {
      scores[i].classement = scores[i - 1].classement;
    } else {
      scores[i].classement = i + 1;
    }
  }
  return scores;
};

export const convertSecondsToHoursMinutesSeconds = (totalSeconds) => {
  if (!(totalSeconds > 0)) {
    return { hours: 0, minutes: 0, seconds: 0 };
  }
  let totalSecondsTmp = totalSeconds;
  const hours = Math.floor(totalSecondsTmp / 3600) || 0;
  totalSecondsTmp %= 3600;
  const minutes = Math.floor(totalSecondsTmp / 60) || 0;
  const seconds = totalSecondsTmp % 60 || 0;
  return { hours, minutes, seconds };
};
/**
 * Retourne chaine de caractère formatée pour affichage joli de la durée en secondes
 * @param secondsTotal
 * @returns {string}
 */
export const prettyPrintSeconds = (secondsTotal) => {
  const hours = Math.floor(secondsTotal / 3600);
  const minutes = Math.floor((secondsTotal % 3600) / 60);
  const seconds = secondsTotal % 60;
  let timeString = '';
  if (hours > 0) {
    timeString += `${hours}h`;
  }
  if (minutes > 0 || hours > 0) {
    // Inclure les minutes si les heures sont présentes
    timeString += `${minutes.toString().padStart(2, '0')}min`;
  }
  if (seconds > 0 || minutes > 0 || hours > 0) {
    // Inclure les secondes si les minutes ou les heures sont présentes
    timeString += `${seconds.toFixed(0).toString().padStart(2, '0')}s`;
  }
  return timeString;
};

const GENERATEDQCMKEY = 'genQCMdata';
const NOTE_NORMAL_QCM_KEY = 'normalMcqScoreTemp';
const CHOIX_GENERATED_QCM_DATA_KEY = 'reponsesQcmGenerated';
const NOTE_GENERATED_QCM_KEY = 'noteQcmGenerated';
const MAXPOITNS_GENERATED_QCM_KEY = 'maxPointsQcmGenerated';
const ANSWERS_NORMAL_MCQ_KEY = 'mcqformanswersdata';
const LAST_SELECTED_TYPE_KEY = 'lastSelectedTypes';
export const setGeneratedQCMData = (data) =>
  sessionStorage.setItem(GENERATEDQCMKEY, JSON.stringify(data));
export const getGeneratedQCMData = () => JSON.parse(sessionStorage.getItem(GENERATEDQCMKEY));
export const getChoixGeneratedQCMData = () =>
  JSON.parse(sessionStorage.getItem(CHOIX_GENERATED_QCM_DATA_KEY));
export const setChoixGeneratedQCMData = (data) =>
  sessionStorage.setItem(CHOIX_GENERATED_QCM_DATA_KEY, JSON.stringify(data));
export const getNoteGeneratedQCM = () => sessionStorage.getItem(NOTE_GENERATED_QCM_KEY);
export const getMaxPointsGeneratedQCM = () => sessionStorage.getItem(MAXPOITNS_GENERATED_QCM_KEY);
export const setNoteGeneralQcm = (data) => sessionStorage.setItem(NOTE_NORMAL_QCM_KEY, data);
export const getNoteGeneralQcm = () => sessionStorage.getItem(NOTE_NORMAL_QCM_KEY);

export const setQcmResultsGeneralQcm = (data) =>
  sessionStorage.setItem(ANSWERS_NORMAL_MCQ_KEY, JSON.stringify(data));
export const getQcmResultsGeneralQcm = () =>
  JSON.parse(sessionStorage.getItem(ANSWERS_NORMAL_MCQ_KEY));

export const setLastSelectedQcmTypes = (data) =>
  sessionStorage.setItem(LAST_SELECTED_TYPE_KEY, JSON.stringify(data));
export const getLastSelectedQcmTypes = () =>
  JSON.parse(sessionStorage.getItem(LAST_SELECTED_TYPE_KEY)) || [];
