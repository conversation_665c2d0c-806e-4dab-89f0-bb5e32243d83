import {
  getHrefProtectedFileUrl,
  getProtectedFileUrl,
  getSupportProtectedS3File,
  downloadS3File
} from '@/services/downloadFiles.js';
import { GlobalConfig } from '@/shared/utils/utils.js';

export const FILE_TYPE = {
  FILE: 'FILE', // Videos, autres fichiers utiles
  FICHE: 'FICHE', // Fiches de cours
  COURS: 'COURS', // Fichier de cours (PDF, etc)
  BILLS: 'BILLS',
  PIXIES: 'PIXIES', // Fichier PDF de mathpix, sauf que l'on obfusce. En BDD nomé : 'Mathpixies', ici, Pixies
  S3FILE: 'S3FILE', // Fichier hébergé sur le bucket S3
  TEMP: 'TEMP'
};

export const getDownloadBaseUrl = (fileType) => {
  let baseUrl = GlobalConfig.get().FILES_URL;
  if (fileType === FILE_TYPE.FICHE) {
    baseUrl = GlobalConfig.get().FICHES_URL;
  } else if (fileType === FILE_TYPE.FILE) {
    baseUrl = GlobalConfig.get().FILES_URL;
  } else if (fileType === FILE_TYPE.COURS) {
    baseUrl = GlobalConfig.get().COURS_URL;
  } else if (fileType === FILE_TYPE.BILLS) {
    baseUrl = GlobalConfig.get().BILLS_URL;
  } else if (fileType === FILE_TYPE.PIXIES) {
    baseUrl = GlobalConfig.get().PIXIES_URL;
  } else if (fileType === FILE_TYPE.S3FILE) {
    baseUrl = GlobalConfig.get().S3FILES_URL;
  } else if (fileType === FILE_TYPE.TEMP) {
    baseUrl = GlobalConfig.get().TEMP_URL;
  }
  return baseUrl;
};

/* NOT WORKING ON SAFARI */
export function openInNewTab(url) {
  const win = window.open(url, '_blank');
  win.focus();
}

export const openInNewTabUniversal = (url) => {
  try {
    const windowReference = window.open('about:blank', '_blank');
    if (windowReference) {
      windowReference.location = url;
    }
  } catch (e) {
    window.location.replace(url);
  }
};

// for safari
export function openInNewTab2(appLink) {
  const getUrl = window.location;
  const baseUrl = getUrl.protocol + '//' + getUrl.host + '/' + getUrl.pathname.split('/')[1];
  openInNewTabUniversal(`${baseUrl}#${appLink}`);
}

const getDownloadTokenUrl = `${GlobalConfig.get().FILES_URL}getdownloadtoken`;

export const downloadFile = async (fileType, url) =>
  getHrefProtectedFileUrl(getDownloadBaseUrl(fileType) + encodeURI(url));

export const getProtectedUrl = async (fileType, url) =>
  getProtectedFileUrl(getDownloadBaseUrl(fileType) + encodeURI(url));
export const getProtectedS3Url = async (s3FileName) =>
  getSupportProtectedS3File(
    getDownloadBaseUrl(FILE_TYPE.S3FILE) + 'getUrl/' + encodeURI(s3FileName)
  );
export const downloadProtectedS3File = async (s3FileName) =>
  downloadS3File(getDownloadBaseUrl(FILE_TYPE.S3FILE) + 'download/' + encodeURI(s3FileName));

// OLD METHOD
export const openDownloadedFile = (url) => {
  // https://stackoverflow.com/questions/43283454/open-blob-objecturl-in-chrome
  const dl = document.getElementById('to_download_medibox');
  dl.href = url;
  dl.target = '_self';
  // dl.download = ''
  dl.click();
};

export const printDownloadedFile = (url) => {
  const dl = document.getElementById('to_download_medibox');
  // eslint-disable-next-line no-script-url
  dl.href = `javascript: w=window.open("${url}"); w.print();`;
  dl.click();
};
