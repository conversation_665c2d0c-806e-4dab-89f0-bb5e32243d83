import { useEffect } from 'react';

/**
 * Dynamically sets the favicon for the document.
 * Supports cache-busting and multiple image types.
 *
 * @param {string|null} path - The base path to the favicon (can be .ico, .png, .svg etc.).
 */
const useFavicon = (path) => {
  useEffect(() => {
    if (!path) return;

    // Remove existing favicons
    const existingIcons = document.querySelectorAll("link[rel='icon'], link[rel='shortcut icon']");
    existingIcons.forEach((el) => el.parentNode.removeChild(el));

    // Cache buster
    const version = Date.now();
    const finalUrl = `${path}?v=${version}`;

    // Create new favicon
    const link = document.createElement('link');
    link.rel = 'icon';

    // Detect mime type
    if (path.endsWith('.svg')) {
      link.type = 'image/svg+xml';
    } else if (path.endsWith('.png')) {
      link.type = 'image/png';
    } else if (path.endsWith('.ico')) {
      link.type = 'image/x-icon';
    }

    link.href = finalUrl;
    document.head.appendChild(link);
  }, [path]);
};

export default useFavicon;
