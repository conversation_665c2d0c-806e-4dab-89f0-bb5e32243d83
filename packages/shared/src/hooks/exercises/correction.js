import { GET_CURRENT_SESSION_DETAILS, GET_CURRENT_SESSION_DETAILS_WITH_QUESTIONS } from '@/shared/graphql/qcm.js';
import { useQuery } from '@apollo/client';

export const useQueryCurrentSessionDetails = ({ sessionId, isInSession, qcmId }) => {
  const {
    loading: loadingSession,
    error: errorSession,
    data: dataSession,
    refetch: refetchCurrentSession,
    // Dans une série d'exercices, on récupère juste la session, pas les exercices
    // Dans résultat générateur, on récupère juste la session + les exercices
  } = useQuery(qcmId ? GET_CURRENT_SESSION_DETAILS : GET_CURRENT_SESSION_DETAILS_WITH_QUESTIONS, {
    fetchPolicy: 'cache-and-network',
    variables: { id: sessionId },
    skip: !isInSession,
  });
  const session = dataSession?.sessionQcm;

  return { session, loadingSession, errorSession, refetchCurrentSession };
};
