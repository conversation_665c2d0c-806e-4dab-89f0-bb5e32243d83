import ChallengeItem from '@/shared/components/Challenge/ChallengeItem.jsx';
import ChallengeUnlockableElements
  from '@/shared/pages/admin/challenges/modal/components/ChallengeUnlockableElements.jsx';
import { tr } from '@/shared/services/translate.js';
import { DATE_FORMATS } from '@/shared/utils/utils.js';
import { TrophyTwoTone } from '@ant-design/icons';
import { Card } from 'antd';
import dayjs from 'dayjs';
import React from 'react';
import { useTranslation } from 'react-i18next';

{/* Challenge detail with status and rewards */}

export default function({ challenge, challengeUserProgress }) {
  const { t } = useTranslation();
  const hasNoProgress = !challengeUserProgress;
  const hasNotFinished = challengeUserProgress?.isFinished === false;
  const hasFinished = challengeUserProgress?.isFinished === true;

  const challengeRewards = challenge?.rewards;
  return (
    <React.Fragment>
      <div style={{ textAlign: 'center', margin: 'auto', maxWidth: '400px' }}>
        {challenge && (
          <Card>
            <ChallengeItem challenge={challenge}/>

            <div>
              {hasFinished && (
                <div>
                  <div style={{ fontSize: '28px' }}>
                    <TrophyTwoTone twoToneColor="#F09B05"/>
                  </div>
                  <h3> {t('YouHaveSuccessfullyCompletedThisChallenge')}</h3>
                  <div>
                    {dayjs(challengeUserProgress?.updatedAt).format(DATE_FORMATS.FULL_FR)}
                  </div>
                  <br/>
                </div>
              )}
              {hasNoProgress && (
                <div>
                  <h3>
                    {t('NotStarted')}
                  </h3>
                </div>
              )}

              {hasNotFinished && (
                <div>
                  <h3>
                    {t('Unfinished')}
                  </h3>
                </div>
              )}
            </div>

            {hasFinished && (
              <>
                <p>{challengeRewards?.[tr('messageToShowWhenChallengeDone')]}</p>
                <br/>
              </>
            )}

            <h2>{t('tab.Rewards')}</h2>

            <ChallengeUnlockableElements
              challenge={challenge}
              editable={false}
              unlocked={hasFinished}
            />

          </Card>
        )}
      </div>

    </React.Fragment>
  );
}