import {
  MUTATION_CREATE_TITLE,
  MUTATION_DELETE_TITLE,
  MUTATION_UPDATE_TITLE,
} from '@/shared/graphql/formations.js'
import { showGqlErrorsInMessagePopupFromException } from '@/shared/utils/utils.js'
import {
  DeleteOutlined,
} from '@ant-design/icons'
import { Button, Form, Input, message, Modal, Popconfirm, Space, Tooltip } from 'antd'
import { useMutation } from '@apollo/client'
import React from 'react'
import { SmallErrorsAlert } from '@/shared/components/ErrorResult'
import { useTranslation } from 'react-i18next';

export const ModalType = {
  CREATE: 'CREATE',
  UPDATE: 'UPDATE',
}

const getMutationFromModalType = (modalType) => {
  switch (modalType) {
    case ModalType.CREATE:
      return MUTATION_CREATE_TITLE
    case ModalType.UPDATE:
      return MUTATION_UPDATE_TITLE
    default:
      return MUTATION_CREATE_TITLE
  }
}

export const CreateEditTitleModal = (
  {
    closeModalHandler,
    modalType,
    isModalVisible,
    formationStep,
    refetch,
    title,
  }) => {
  const {t} = useTranslation();
  const [form] = Form.useForm()
  const [TitleMutation, { loading, data, error }] = useMutation(getMutationFromModalType(modalType))
  const [DeleteMutation] = useMutation(MUTATION_DELETE_TITLE)

  const handleFinish = async data => {
    try {
      const newTitle = { ...data }
      if (newTitle?.size) {
        newTitle.size = parseInt(newTitle.size, 10)
      }
      if (newTitle?.offset) {
        newTitle.offset = parseInt(newTitle.offset, 10)
      }
      if (newTitle?.level) {
        newTitle.level = parseInt(newTitle.level, 10)
      }
      if (newTitle?.fontWeight) {
        newTitle.fontWeight = parseInt(newTitle.fontWeight, 10)
      }

      if (modalType === ModalType.UPDATE) {
        await TitleMutation({ variables: { id: title?.id, input: newTitle } })
        message.success(t('Updated'));
      } else { // Create
        await TitleMutation({ variables: { input: newTitle } })
        message.success(t('Created'));
      }
      await closeModalHandler()
    } catch (e) {
      console.error(e)
      showGqlErrorsInMessagePopupFromException(e)
    }
  }


  async function deleteTitre() {
    try {
      await DeleteMutation({
        variables: {
          id: title?.id,
        },
      })
      await closeModalHandler()
    } catch (e) {
      console.error(e)
    }
  }

  const buttonProps = {
    size: 'large',
    // fontSize: '20px',
    style: {
      height: '120px',
      width: '120px',
    },
  }

  return (
    (<Modal
      title={modalType === ModalType.UPDATE ? `${t('Edit')}` : 'Créer un nouveau titre'}
      open={isModalVisible}
      onCancel={() => {
        form.resetFields()
        closeModalHandler()
      }}
      footer={null}
      closable
      confirmLoading={false}
      bodyStyle={{ paddingTop: 0 }}
      width={1000}
    >
      {/* Show small error(s) if needed */}
      <SmallErrorsAlert error={error} loading={loading}/>
      <>
        <Form
          layout="vertical"
          onFinish={handleFinish}
          form={form}
          initialValues={
            modalType === ModalType.UPDATE ?
              title : {}
          }
        >
          <>
            <Form.Item
              name="name"
              label={t('TitleName')}
            >
              <Input type="text" placeholder={t('Title')}/>
            </Form.Item>
          </>

          <Form.Item
            name="level"
            label={t('TitleLevel')}
            help={t('TitleLevelExplanation')}
          >
            <Input type="number" placeholder="2"/>
          </Form.Item>

          <Form.Item
            name="size"
            label={t('SizeInPixels')}
          >
            <Input type="number" placeholder="16"/>
          </Form.Item>

          <Form.Item
            name="fontWeight"
            label={t('FontWeight')}
            help="Valeurs recommandées : entre 400 (fin) et 900 (épais)"
          >
            <Input type="number" placeholder="600"/>
          </Form.Item>

          <Form.Item
            name="offset"
            label={t('PixelDecalage')}
          >
            <Input type="number" placeholder="6"/>
          </Form.Item>

          <Form.Item
            name="color1"
            label={t('ColorOne')}
          >
            <input type="color"/>
          </Form.Item>
          <Form.Item
            name="color2"
            label={t('ColorTwo')}
          >
            <input type="color"/>
          </Form.Item>

          <Form.Item
            name="backgroundColor"
            label="Couleur de fond"
          >
            <input type="color"/>
          </Form.Item>

          <Form.Item>
            {modalType === ModalType.UPDATE && (
              <Space>
                <Button htmlType="submit" type="primary" loading={loading}>
                  {t('Update')}
                </Button>
                <Popconfirm
                  title={t('SureToDelete')}
                  onConfirm={deleteTitre}
                  okText={t('general.yes')}
                  cancelText={t('general.no')}
                >
                  <Tooltip title={t('Delete')}>
                    <Button shape="circle" type="danger" icon={<DeleteOutlined/>}/>
                  </Tooltip>
                </Popconfirm>
              </Space>
            )}
            {modalType === ModalType.CREATE && (
              <Button htmlType="submit" type="primary" loading={loading}>
                {t('general.add')}
              </Button>
            )}
          </Form.Item>
        </Form>
      </>
    </Modal>)
  );
}
