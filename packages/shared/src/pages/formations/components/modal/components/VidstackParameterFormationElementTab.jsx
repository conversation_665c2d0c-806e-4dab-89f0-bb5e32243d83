import React from 'react';
import VidstackParameters from '@/shared/components/Vidstack/VidstackParameters';
import VidstackFormationElementDisplay from '@/shared/pages/formations/components/modal/components/VidstackFormationElementDisplay';

const VidstackParameterFormationElementTab = () => {
  return (
    <div style={{ display: 'flex', flexWrap: 'wrap', gap: '20px' }}>
      <VidstackParameters />
      <div
        style={{
          flexGrow: 1,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center'
        }}
      >
        <VidstackFormationElementDisplay style={{ width: '100%' }} />
      </div>
    </div>
  );
};

export default VidstackParameterFormationElementTab;
