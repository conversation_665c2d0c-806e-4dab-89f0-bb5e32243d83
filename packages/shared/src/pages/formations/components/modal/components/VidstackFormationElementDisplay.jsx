import VidstackManager from '@/shared/components/Vidstack/VidstackManager';
import React, { useEffect, useState } from 'react';
import { useVidstackContext } from '@/shared/components/Vidstack/VidstackContextProvider';
import RenderQuillHtml from '@/shared/components/ExoQuill/RenderQuillHtml';
import { Button, Popover, Typography } from 'antd';
import { useTranslation } from 'react-i18next';
import VidstackEditThumbnail from '@/shared/components/Vidstack/VidstackEditThumbnail';
import { ImageUp } from 'lucide-react';

const VidstackFormationElementDisplay = ({
  allowEditThumbnail = false,
  hideTitle = false,
  style
}) => {
  const { t } = useTranslation();
  const { title, vidstackQuillDescription } = useVidstackContext();

  const [quillKey, setQuillKey] = useState(0);
  const [showThumbnailEditor, setShowThumbnailEditor] = useState(false);

  useEffect(() => {
    if (vidstackQuillDescription && typeof vidstackQuillDescription === 'string') {
      setQuillKey((prev) => prev + 1);
    }
  }, [vidstackQuillDescription]);

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: 8, ...style }}>
      <Typography.Title
        level={3}
        style={{
          margin: 0,
          marginTop: 8,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: 12
        }}
      >
        {!hideTitle && t('VideoPreview')}

        {allowEditThumbnail && (
          <Popover content={showThumbnailEditor ? t('HideEditThumbnail') : t('EditThumbnail')}>
            <Button
              type="text"
              color="default"
              onClick={() => setShowThumbnailEditor((prev) => !prev)}
            >
              <ImageUp style={{ fontSize: 22 }} />
            </Button>
          </Popover>
        )}
      </Typography.Title>

      <div
        style={{
          flexGrow: 1,
          borderRadius: 12,
          border: '1px solid lightgrey',
          backgroundColor: '#f5f5f5A6',
          padding: 24
        }}
      >
        <VidstackManager>
          <div style={{ display: 'flex', flexDirection: 'column', gap: 8 }}>
            <Typography.Title level={4} style={{ margin: 0, marginTop: 8 }}>
              {title}
            </Typography.Title>
            <RenderQuillHtml key={quillKey}>{vidstackQuillDescription}</RenderQuillHtml>
          </div>

          {showThumbnailEditor && (
            <div style={{ marginTop: 8 }}>
              <VidstackEditThumbnail />
            </div>
          )}
        </VidstackManager>
      </div>
    </div>
  );
};

export default VidstackFormationElementDisplay;
