import { QUERY_REVISIONS_BETWEEN } from '@/shared/graphql/edt.js';
import useLocalStorage from '@/shared/hooks/useLocalStorage.js';
import { getMobiScrollLanguage } from '@/shared/pages/planning/index.jsx';
import styles from '@/shared/pages/planning/style.less';
import { useEffectScrollTop } from '@/shared/utils/hooks/useEffectScrollTop.js';
import { isMobile, TimeZoneManager } from '@/shared/utils/utils.js';
import { useLazyQuery } from '@apollo/client';
import { Badge, Button as AntdButton, Select, Tag } from 'antd';
import moment from 'moment-timezone';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import { router } from 'umi';
import Link from 'umi/link.js';
import {
  Eventcalendar,
  CalendarNav,
  Button,
  CalendarToday,
  Popup,
  momentTimezone,
} from '@mobiscroll/react';
import '@mobiscroll/react/dist/css/mobiscroll.min.css';
import { useTranslation } from 'react-i18next';

momentTimezone.moment = moment;


const calculateBeginning = (date) => dayjs(date).subtract(30, 'days');
const calculateEnd = (date) => dayjs(date).add(30, 'days');

export const PlanningRevisionsCalendarView = (props) => {
  const {t, i18n} = useTranslation();
  const {language} = i18n;

  useEffectScrollTop();
  const [begin, setBegin] = useState(calculateBeginning(dayjs()));
  const [end, setEnd] = useState(calculateEnd(dayjs()));

  const [showTimeZoneSelect, setShowTimeZoneSelect] = useState(false);
  const [currentTimeZone, setCurrentTimezone] = useLocalStorage(TimeZoneManager.getLocalStorageTimeZoneKey(), TimeZoneManager.getDefaultTimeZone());

  const CALENDAR_VIEW_SCHEDULE_WEEK_GROUPS = {
    schedule: {
      type: 'week',
      startTime: '06:00',
      popover: true,
    },
  };
  const [view, setView] = useState('calendar'); //  calendar, scheduleByGroups, schedule
  const [currentDate, setCurrentDate] = useState(new Date());

  const [groupsView, setGroupsView] = useState(false);
  const [calView, setCalView] = useState({
    calendar: {
      labels: 'all', // Show all events
      popover: true, // Activate popover
    },
  });

  const [queryPlanning, { loading, error, data, refetch }] =
    useLazyQuery(QUERY_REVISIONS_BETWEEN, {
      variables: {
        begin: begin.toDate(),
        end: end.toDate(),
      },
      skip: (!begin && !end),
      fetchPolicy: 'no-cache',
    });
  const [hasStarted, setStarted] = useState(false);
  useEffect(() => {
    if (!hasStarted) {
      queryPlanning();
      setStarted(true);
    }
  }, []);

  const onPanelChange = (value, mode) => {
    if (mode === 'month') {
      setBegin(calculateBeginning(value));
      setEnd(calculateEnd(value));
      queryPlanning();
    }
  };

  /* Load fresh data */
  const onPageLoading = (event, inst) => {
    const firstDay = event?.firstDay;
    const lastDay = event?.lastDay;
    setBegin(dayjs(firstDay));
    setEnd(dayjs(lastDay));
    queryPlanning();
  };

  const changeView = React.useCallback((event) => {
    let calendarView;
    switch (event.target.value) {
      case 'calendar':
        calendarView = {
          calendar: {
            labels: 'all', // Show all events
            popover: true, // Activate popover
          },
        };
        setGroupsView(false);
        break;
      default:
        break;
    }
    setView(event.target.value);
    setCalView(calendarView);
  }, [setView, setCalView, setGroupsView]);

  const onSelectedDateChange = React.useCallback((event) => {
    const value = event?.date;
    setCurrentDate(value);
  }, [setCurrentDate]);

  const getFirstDayOfWeek = React.useCallback((d, prev) => {
    const day = d.getDay();
    const diff = d.getDate() - day + (prev ? -7 : 7);
    return new Date(d.setDate(diff));
  }, []);

  const navigatePage = React.useCallback((prev) => {
    if (view == 'calendar') {
      // Same for mobile and desktop
      const prevNextPage = new Date(currentDate.getFullYear(), currentDate.getMonth() + (prev ? -1 : 1), 1);
      setCurrentDate(prevNextPage);
    } else {
      let nextDate;
      if (isMobile) {
        if (prev) {
          nextDate = dayjs(currentDate).subtract(1, 'week').toDate();
        } else {
          nextDate = dayjs(currentDate).add(1, 'week').toDate();
        }
      } else {
        nextDate = getFirstDayOfWeek(currentDate, prev);
      }
      setCurrentDate(nextDate);
    }
  }, [view, currentDate, setCurrentDate, getFirstDayOfWeek]);

  const customWithNavButtons = () => {
    return (
      <React.Fragment>
        <CalendarNav className="md-custom-header-nav"/>
        <div className="md-custom-header-controls">
          <Button
            onClick={() => navigatePage(true)}
            icon="material-arrow-back"
            variant="flat"
            className="md-custom-header-button"
          />
          <CalendarToday className="md-custom-header-today"/>
          <Button
            onClick={() => navigatePage(false)}
            icon="material-arrow-forward"
            variant="flat"
            className="md-custom-header-button"
          />
        </div>
      </React.Fragment>
    );
  };

  const [mobiscrollDataPlanning, setMobiscrollDataPlanning] = React.useState([]);


  const getMonPlanning = () => data && data.monPlanningRevision;

  const dateCellRender = (value) => {
    const listData = getMonPlanning() && getMonPlanning().filter(item => dayjs(value).isSame(item.date, 'day'));
    return (
      <ul className={styles.events}>
        {listData && listData[0] && listData[0].events && listData[0].events.map(item => (
          <li key={item?.id}>
            <Badge
              status="success"
              text={(
                <Link to={`/cours/${item.cours.id}`}>
                  {item?.cours?.ueCategory?.ue?.name} : {item?.cours?.name} ({item?.occurence} / {item?.totalOccurences})
                </Link>
              )}
            >
            </Badge>
          </li>
        ))}
      </ul>
    );
  };


  /* Load mobiscroll planning from data */
  useEffect(() => {
    const datesToAdd = [];
    const planningData = getMonPlanning();
    let events = [];
    if (planningData) {
      events = planningData?.map((item) => item?.events);
    }
    events = events.flat();
    let dataPlanning = events?.map(c => {
      let title = '';
      let color;
      if (c?.cours) {
        title = `${c?.cours?.ueCategory?.ue?.name} : ${c?.cours?.name || ''} (${c?.occurence} / ${c?.totalOccurences})`;
      }
      let endDate = null;
      if (c.dateEnd) {
        endDate = dayjs(c.dateEnd).set('second', 0).set('ms', 0).toDate();
      } else {
        endDate = dayjs(c.date).add(1, 'hour').set('second', 0).set('ms', 0).toDate()
      }
      return {
        ...c,
        start: dayjs(c.date).set('second', 0).set('ms', 0).toDate(),
        end: endDate,
        title,
        // resource: c.groupes?.map(g => g?.id),
        tooltip: `${title}`,
        color,
        premiereRevision: c?.premiereRevision,
        derniereRevision: c?.derniereRevision,
      };
    });
    if (!dataPlanning) {
      dataPlanning = [];
    }
    setMobiscrollDataPlanning([...dataPlanning, ...datesToAdd]);
  }, [data]);

  const getLink = (item) => {
    if (item?.cours) {
      return `/cours/${item.cours.id}`;
    }
    if (item?.qcm) {
      const { qcm } = item;
      if (qcm?.annale) {
        return `/annales/${qcm.id_qcm}`;
      }
      return `/qcm/${qcm?.id_qcm}`;
    }
    if (item?.exam_session) {
      return `/exam/${item?.exam_session?.examId}`;
    }
    if (item?.examQuestionSerie) {
      return `/exam/${item?.examQuestionSerie?.examId}`;
    }
    if (item?.event) {
      return `/event/${item?.event?.id}`;
    }
    return '';
  };
  /* Tooltips */
  const timerRef = React.useRef(null);
  const [isTooltipOpen, setTooltipOpen] = React.useState(false);
  const [anchor, setAnchor] = React.useState(null);
  const [currentEvent, setCurrentEvent] = React.useState(null);
  const [displayTimeZone, setDisplayTimeZone] = React.useState('local');
  const [info, setInfo] = React.useState(''); //todo clean
  const [time, setTime] = React.useState('');//todo clean

  const onEventHoverIn = React.useCallback((args) => {
    const event = args.event;
    const time = `${dayjs(event.start).format('HH:mm')} - ${dayjs(event.end).format('HH:mm')} `;
    setCurrentEvent(event);
    setInfo(event.title);
    setTime(time);
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }
    setAnchor(args.domEvent.target);
    setTooltipOpen(true);

  }, []);

  const onEventHoverOut = React.useCallback(() => {
    timerRef.current = setTimeout(() => {
      setTooltipOpen(false);
    }, 200);
  }, []);
  const onMouseEnter = React.useCallback(() => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }
  }, []);
  const onMouseLeave = React.useCallback(() => {
    timerRef.current = setTimeout(() => {
      setTooltipOpen(false);
    }, 200);
  }, []);

  const onEventClickGoToEvent = (event, inst) => {
    const item = event?.event;
    router.push(getLink(item));
  };

  return (
    <>
      <div style={{ textAlign: 'center' }}>{t('general.Timezone')}
        {showTimeZoneSelect ? (
          <>
            <Select
              style={{ minWidth: '300px', marginBottom: '10px' }}
              showSearch
              //size="small"
              value={currentTimeZone}
              onSelect={(value, item) => {
                setCurrentTimezone(value);
                moment.tz.setDefault(value);
                setShowTimeZoneSelect(false);
                window.location.reload();
              }}
            >
              {moment.tz.names().map(tz => (
                <Select.Option value={tz} key={tz} toSearch={tz}>
                  {tz}
                </Select.Option>
              ))}
            </Select>
            <AntdButton type={'link'} onClick={() => setShowTimeZoneSelect(false)}>{t('Cancel')}</AntdButton>
          </>
        ) : (
          <AntdButton type={'link'} onClick={() => setShowTimeZoneSelect(true)}>{currentTimeZone}</AntdButton>
        )}
      </div>


      <div style={isMobile ? { height: '100vh' } : {}}>
        <Eventcalendar
          locale={getMobiScrollLanguage(language)}
          dataTimezone="utc"
          displayTimezone={currentTimeZone}
          timezonePlugin={momentTimezone}

          renderHeader={customWithNavButtons}

          theme="ios"
          themeVariant="light"
          data={mobiscrollDataPlanning}

          onSelectedDateChange={onSelectedDateChange}
          selectedDate={currentDate}

          view={calView}

          onEventClick={onEventClickGoToEvent}
          cssClass="md-custom-header"
          onPageLoading={onPageLoading}

          showEventTooltip

          onEventHoverIn={onEventHoverIn}
          onEventHoverOut={onEventHoverOut}
        />
      </div>


      <Popup
        display="anchored"
        isOpen={isTooltipOpen}
        anchor={anchor}
        touchUi={false}
        showOverlay={false}
        contentPadding={false}
        closeOnOverlayClick={false}
        width={350}
        cssClass="md-tooltip"
      >
        <div onMouseEnter={onMouseEnter} onMouseLeave={onMouseLeave} style={{ padding: '10px' }}>
          {info}
          <br/>
          {currentEvent?.cours && (
            <>
              <div>
                Première révision : {dayjs(currentEvent?.premiereRevision).format('DD/MM/YYYY')}
              </div>
              <div>
                Dernière révision : {dayjs(currentEvent?.derniereRevision).format('DD/MM/YYYY')}
              </div>
            </>
          )}

          {currentEvent?.event?.customFields && currentEvent?.event?.customFields?.map((field, key) => (
            <div style={{ marginBottom: '5px' }} key={key}>
              <div>
                <Tag color="#15576A"> {field?.label} <b>{field?.value}</b></Tag>
              </div>
            </div>
          ))}

          {currentEvent?.exam_session?.examQuestionSeries && (
            <br/>
          )}
          {currentEvent?.exam_session?.examQuestionSeries?.map(questionSerie => (
            <div key={questionSerie?.id}>
              - {questionSerie?.qcm?.titre}
            </div>
          ))}

          {currentEvent?.event?.customFields && currentEvent?.event?.customFields?.map((field, key) => (
            <div style={{ marginBottom: '5px' }} key={key}>
              <div>
                <Tag color="#15576A"> {field?.label} <b>{field?.value}</b></Tag>
              </div>
            </div>
          ))}

          <br/>
          {currentEvent && (
            <div>
              {currentEvent?.groupes?.map(g => (
                <Tag color="#15576A" key={g.id}>{g.name}</Tag>
              ))}
            </div>
          )}
          <br/>

          <Link to={getLink(currentEvent)}>
            <Button>
              {t('general.See')}
            </Button>
          </Link>
        </div>
      </Popup>


    </>
  );
};