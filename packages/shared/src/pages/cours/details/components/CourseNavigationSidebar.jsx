import defaultSettings from '@/shared/config/defaultSettings.js';
import {
  QUERY_COURSES_IN_UE,
  QUERY_UE_BY_ID_WITH_CHILDREN,
  QUERY_UE_CATEGORIES_FOR_UE,
  QUERY_UE_CATEGORY_ID_WITH_CHILDREN_AND_COURSES
} from '@/shared/graphql/cours.js';
import useLocalStorage from '@/shared/hooks/useLocalStorage.js';
import useMediaQuery from '@/shared/hooks/useMediaQuery.jsx';
import { GlobalContext } from '@/shared/layouts/BlankLayout.jsx';
import {
  renderUEDescriptionWithFallback,
  renderUENameWithFallback,
  tr
} from '@/shared/services/translate.js';
import {
  getLinearGradientBackgroundValue,
  getUrlProtectedRessource
} from '@/shared/utils/utils.js';
import { useQuery } from '@apollo/client';
import { Collapse, Steps } from 'antd';
import React, { useContext, useEffect, useState } from 'react';
import { router } from 'umi';
import { isAdmin } from '@/shared/utils/authority';

const renderIcon = (image) => {
  return (
    <img
      src={getUrlProtectedRessource(FILES_URL + image)}
      style={{ maxWidth: '22px', marginRight: '5px' }}
      alt=""
    />
  );
};

const CoursSteps = ({ cours, setCoursId, currentCoursId }) => {
  const [currentStepKey, setCurrentStepKey] = useState(undefined);

  useEffect(() => {
    // find key from id
    const index = cours?.findIndex((step) => step.id === currentCoursId);
    setCurrentStepKey(index);
  }, [currentCoursId]);

  return (
    <Steps
      current={currentStepKey}
      // type={'navigation'}
      size="small"
      onChange={(step) => {
        setCoursId(step);
      }}
      direction="vertical"
    >
      {cours
        ?.filter((c) => c.isVisible || isAdmin())
        ?.map((cours) => (
          <Steps.Step
            stepIndex={cours?.id}
            onClick={() => {
              setCoursId(cours?.id);
              router.push(`/cours/${cours.id}`);
              // setCurrentStepId(step?.id)
              // setCurrentStep(step)
            }}
            key={cours.id}
            title={
              cours?.targetCours?.[tr('name')] ||
              cours?.targetCours?.name ||
              cours?.[tr('name')] ||
              cours?.name
            }
            description={
              <>
                {cours?.targetCours?.[tr('text')] ||
                  cours?.targetCours?.text ||
                  cours?.[tr('text')] ||
                  cours?.text}
                {/*
                {canEdit && (
                  <Button
                    icon={<EditOutlined/>}
                    size={'small'}
                    onClick={() => {
                      //setStepToEdit(step)
                      //setEditVisible(true)
                    }}
                  />
                )}
              */}
              </>
            }
          />
        ))}
    </Steps>
  );
};

const UeMenuTitle = ({ ue }) => {
  const [isHover, setHover] = useState(false);

  const getUnderlineStyle = (color1, color2) => ({
    width: '65%',
    backgroundColor: '#1ebbf0',
    background: getLinearGradientBackgroundValue(color1, color2), // 'linear-gradient(left, #1ebbf0 30%, #39dfaa 100%)',
    height: '3px',
    position: 'absolute',
    display: 'block',
    boxSizing: 'border-box',
    left: '29%',
    top: '80%',
    right: '0%',
    opacity: '0.9',
    transition: 'all .3s cubic-bezier(.175,.885,.32,1.275)'
  });

  const getColoredUEStyle = (color1, color2) => ({
    padding: '6px',
    paddingRight: 0
  });

  return (
    <span onMouseOver={(e) => setHover(true)} onMouseOut={(e) => setHover(false)}>
      <span style={getColoredUEStyle(ue.color || defaultSettings.colorCard)}>
        <b>{renderUENameWithFallback(ue)}</b>
        {renderUEDescriptionWithFallback(ue) && ` : ${renderUEDescriptionWithFallback(ue)}`}
      </span>
      <i />
    </span>
  );
};

const CategoryContent = ({ category }) => {
  const {
    ueId: currentUeId,
    categoryId: currentCategoryId,
    coursId: currentCoursId,
    setCoursId,
    setCategoryId
  } = useContext(GlobalContext);

  // Query actual category, with courses, and children
  const {
    data: dataCurrentCategory,
    loading: loadingCurrentCategory,
    refetch: refetchCategoryWithChildren
  } = useQuery(QUERY_UE_CATEGORY_ID_WITH_CHILDREN_AND_COURSES, {
    variables: { id: category?.id },
    fetchPolicy: 'cache-and-network',
    skip: !category?.id
  });
  const currentCategory = dataCurrentCategory?.ueCategory;
  const childrenCategories = currentCategory?.children;
  const coursInCategory = currentCategory?.cours;
  const hasChildren = childrenCategories?.length > 0;

  return (
    <>
      {hasChildren && (
        <Collapse
          activeKey={currentCategoryId}
          style={{ backgroundColor: 'white' }}
          bordered={false}
          onChange={(key) => setCategoryId(key)}
        >
          {childrenCategories
            ?.filter((c) => c.isVisible || isAdmin())
            ?.map((category) => (
              <Collapse.Panel
                style={{ backgroundColor: 'white' }}
                header={
                  <>
                    {category.image && renderIcon(category.image)}
                    {category[tr('name')] || category?.name}
                  </>
                }
                key={category.id}
              >
                <CategoryContent category={category} />
              </Collapse.Panel>
            ))}
        </Collapse>
      )}

      {/* Cours */}
      <div style={hasChildren ? { marginTop: '24px' } : {}}>
        <CoursSteps
          cours={coursInCategory}
          setCoursId={setCoursId}
          currentCoursId={currentCoursId}
        />
      </div>
    </>
  );
};

const UEContent = ({ ue }) => {
  const ueId = ue?.id;

  const {
    categoryId: currentCategoryId,
    coursId: currentCoursId,
    setCoursId,
    setCategoryId
  } = useContext(GlobalContext);

  const { data: dataCurrentUE } = useQuery(QUERY_UE_BY_ID_WITH_CHILDREN, {
    variables: { id: ueId },
    fetchPolicy: 'cache-and-network',
    skip: !ueId
  });

  const {
    loading: loadingUeCategories,
    error: errorUECategories,
    data: dataUECategories,
    refetch: refetchUECategories
  } = useQuery(QUERY_UE_CATEGORIES_FOR_UE, {
    fetchPolicy: 'cache-and-network',
    variables: { ueId },
    skip: !ueId
  });
  //Cours dans UE
  const {
    loading: loadingCourses,
    error: errorCourses,
    data: dataCourses,
    refetch: refetchCourses
  } = useQuery(QUERY_COURSES_IN_UE, {
    fetchPolicy: 'cache-and-network',
    variables: { ueId },
    skip: !ueId
  });

  const refetchAll = () => {
    refetchUECategories();
    refetchCourses();
  };

  const isLoadingSomething =
    (loadingCourses || loadingUeCategories) && (!dataUECategories || !dataCourses);

  // Catégories de UE parent
  const categories = dataUECategories?.ueCategories;
  // UEs enfants (si dossier)
  const childrenUEs = dataCurrentUE?.ue?.children;
  // Cours
  const cours = dataCourses?.coursInUE;

  return (
    <Collapse
      activeKey={currentCategoryId}
      style={{ backgroundColor: 'white' }}
      bordered={false}
      key={ue?.id}
      onChange={(key) => setCategoryId(key)}
    >
      {childrenUEs?.map((ue, key) => (
        <Collapse.Panel
          style={{ backgroundColor: 'white' }}
          header={
            <>
              {renderIcon(ue.image)}
              <UeMenuTitle ue={ue} />
            </>
          }
          key={ue.id}
        >
          <UEContent ue={ue} key={key} />
        </Collapse.Panel>
      ))}

      {/* Categories list */}
      {categories
        ?.filter((c) => c.isVisible || isAdmin())
        ?.map((category) => (
          <Collapse.Panel
            style={{ backgroundColor: 'white' }}
            header={
              <>
                {category.image && renderIcon(category.image)}
                {category[tr('name')] || category?.name}
              </>
            }
            key={category.id}
          >
            <CategoryContent category={category} />
          </Collapse.Panel>
        ))}

      {/* Cours dans la matière */}
      {cours?.length > 0 && (
        <div style={{ marginTop: '24px' }}>
          <CoursSteps cours={cours} setCoursId={setCoursId} currentCoursId={currentCoursId} />
        </div>
      )}
    </Collapse>
  );
};

export const CourseNavigationSidebar = () => {
  const {
    ueId: currentUeId,
    categoryId: currentCategoryId,
    coursId: currentCoursId,
    setNextCours,
    appearance
  } = useContext(GlobalContext);

  const [sidebarCollapsed, setSidebarCollapsed] = useLocalStorage('exo-sidebar-collapsed', false);

  // Query current UE id with children
  const { loading, error, data, refetch } = useQuery(QUERY_UE_BY_ID_WITH_CHILDREN, {
    variables: { id: currentUeId },
    fetchPolicy: 'cache-and-network'
  });

  const currentUe = data?.ue;

  //TODO refactor and delete
  useEffect(() => {
    /* Determinate next item ID from current */
    setNextCours(undefined);
    if (!currentUe) {
      return;
    }
    let currentCateg = currentUe?.ueCategories?.find((ca) => ca.id === currentCategoryId);
    if (!currentCateg) {
      return;
    }
    let currentCours = currentCateg?.cours?.find((co) => co.id === currentCoursId);
    if (!currentCours) {
      return;
    }
    const coursIndex = currentCateg?.cours?.indexOf(currentCours);
    let nextCours = currentCateg?.cours[coursIndex + 1];
    if (nextCours) {
      setNextCours(nextCours);
    } else {
      // go to next categ
      const categIndex = currentUe.ueCategories.indexOf(currentCateg);
      let nextCateg = currentUe.ueCategories[categIndex + 1];
      if (nextCateg) {
        let nextCours = nextCateg.cours[0];
        if (nextCours) {
          setNextCours(nextCours);
        } else {
          setNextCours(undefined);
        }
      }
    }
  }, [currentUeId, currentCoursId, currentCategoryId]);

  const hasCustomBackgroundPlatform = !!appearance?.backgroundImagePlatform;

  const showSider = !!currentUe;

  const ue = currentUe;

  const breakpointLg = useMediaQuery('(min-width: 992px)');

  return (
    <>
      {showSider && (
        /*<Layout.Sider
        width={266}
      //breakpoint="lg"
      collapsedWidth="0"
      collapsible={breakpointLg ? true : false}
      onCollapse={(collapsed) => {
        console.log({ collapsed });
        if (collapsed !== sidebarCollapsed) {
          setSidebarCollapsed(collapsed);
        }
      }}
      collapsed={breakpointLg ? sidebarCollapsed : true}
      theme="light"
      style={{ zIndex: 1 }}
      //zeroWidthTriggerStyle={{position: 'fixed'}}
      >*/
        <Collapse
          style={{ backgroundColor: 'white' }}
          bordered={false}
          activeKey={currentUeId}
          onChange={(key) => {
            //setUeId(key); // cause re-render and crash sidebar
          }}
        >
          {/* current ue */}
          <Collapse.Panel
            style={{ backgroundColor: 'white' }}
            header={
              <>
                {renderIcon(ue.image)}
                <UeMenuTitle ue={ue} />
              </>
            }
            key={ue.id}
          >
            <UEContent ue={ue} />
          </Collapse.Panel>
        </Collapse>
        //</Layout.Sider>
      )}
    </>
  );
};
