import ElementValidationCriterias from '@/shared/pages/cours/formation/Admin/Modules/ElementValidationCriterias';
import { EditFormationContext } from '@/shared/pages/cours/formation/context/EditFormationContext';
import { CreateEditFormationElementModal } from '@/shared/pages/formations/components/modal/CreateEditFormationElementModal';
import React, { useContext } from 'react';
import { useTranslation } from 'react-i18next';
import { Divider, Typography } from 'antd';

export default function EditStepInCours(props) {
  const { t } = useTranslation();

  const {
    loadingMutationFormationBloc,
    selectedCoursMenuItem,
    setSelectedCoursMenuItem,
    refetchBlocks,
    courseBlocks,
    updateFormationBlock,
    module
  } = useContext(EditFormationContext);

  //const courseBlock = courseBlocks?.find((item) => item.id === selectedCoursMenuItem);
  // same but memoized :
  const courseBlock = React.useMemo(() => {
    return courseBlocks?.find((item) => item.id === selectedCoursMenuItem);
  }, [courseBlocks, selectedCoursMenuItem]);

  const courseElements = courseBlock?.elements;

  const handleCloseEdition = async (elementId, action) => {
    /*
    if (action === 'delete') {
      // delete associated module?
      await deleteModule({
        variables: {
          id: module.id
        }
      });
      setSelectedItem(null);
      await refetchModules();
    }
    */
    await refetchBlocks();
  };

  const handleCloseCreation = async (elementId, action) => {
    await refetchBlocks();
  };

  const hasNoElement = courseElements?.length === 0;
  const hasElement = courseElements?.length > 0;

  const showCoursStepValidation = module?.validationSettings?.minimumTime === false;

  return (
    <div style={{ marginLeft: 24 }}>
      {/* Edition module type élément */}
      {courseBlock && (
        <>
          {/* Edit block Title with typography editable */}
          <Typography.Title
            editable={{
              onChange: async (value) => {
                await updateFormationBlock({
                  variables: {
                    id: courseBlock.id,
                    input: {
                      name: value
                    }
                  }
                });
                await refetchBlocks();
              }
            }}
            style={{ marginBottom: 16 }}
          >
            {courseBlock.name}
          </Typography.Title>

          <br />
          <br />

          {hasNoElement && (
            <>
              <CreateEditFormationElementModal
                isModalVisible={true}
                modalType="CREATE"
                block={courseBlock}
                //position={position}
                closeModalHandler={handleCloseCreation}
              />
            </>
          )}

          {hasElement && (
            <>
              <CreateEditFormationElementModal
                isModalVisible={true}
                block={courseBlock}
                modalType={'UPDATE'}
                element={courseElements?.[0]}
                showElementChoice={false}
                //preselectedElementType={ELEMENTS_TYPE.DO_EXERCISE}
                closeModalHandler={handleCloseEdition}
                showCancelButton={false}
              />

              <Divider />
              <Typography.Title level={3} style={{ marginBottom: 16 }}>
                {t('Formation.ValidationCriterias')}
              </Typography.Title>
              {showCoursStepValidation ? (
                <ElementValidationCriterias element={courseElements?.[0]} />
              ) : (
                <>
                  <Typography.Text type="secondary">
                    {t('Formation.ValidationCriteriaDefinedByModule')}
                  </Typography.Text>
                </>
              )}
            </>
          )}
        </>
      )}
    </div>
  );
}
