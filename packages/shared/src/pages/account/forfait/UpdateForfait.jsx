import { ErrorResult } from '@/shared/components/ErrorResult.jsx';
import RenderQuillHtml from '@/shared/components/ExoQuill/RenderQuillHtml';
import { SpinnerCentered } from '@/shared/components/PageLoading/SpinnerCentered.jsx';
import {
  MUTATION_UPDATE_FORFAIT_USER_TMP,
  QUERY_FORFAITS_CUSTOM_LINK,
  QUERY_MES_FORFAITS_DISPONIBLES
} from '@/shared/graphql/forfaits.js';
import { GET_CONFIG } from '@/shared/graphql/home.js';
import { GlobalContext } from '@/shared/layouts/BlankLayout.jsx';
import CustomFormItems from '@/shared/pages/account/forfait/components/CustomFormItems.jsx';
import ForfaitGroupsList from '@/shared/pages/account/forfait/components/ForfaitGroupsList';
import { ForfaitImage } from '@/shared/pages/account/forfait/components/ForfaitImage.jsx';
import { MultipleForfaits } from '@/shared/pages/account/forfait/components/MultiplesForfaits.jsx';
import { RadioForfait } from '@/shared/pages/account/forfait/components/RadioForfaits.jsx';
import { ForfaitTypes } from '@/shared/pages/admin/forfaits/components/CreateEditForfaitModal.jsx';
import { RegisterFormItems } from '@/shared/pages/user/register/components/RegisterFormItems.jsx';
import {
  CONFIG_KEYS,
  DEFAULT_CONFIG_VARIABLES,
  getCurrentDomain
} from '@/shared/services/config.js';
import { mapFormFieldsToCustomFields } from '@/shared/services/formations.js';
import { getValueFromKeyConfigData } from '@/shared/services/home.js';
import { isAdmin, isTuteur } from '@/shared/utils/authority';
import {
  cardHeadStyle,
  displayDirectHtml,
  GlobalConfig,
  IS_DEV,
  isMobile,
  showGqlErrorsInMessagePopupFromException
} from '@/shared/utils/utils.js';
import { useMutation, useQuery } from '@apollo/client';
import {
  Card,
  Checkbox,
  Form,
  Button,
  Statistic,
  message,
  Input,
  Steps,
  Col,
  Row,
  Divider,
  Tag
} from 'antd';
import React, { useContext, useEffect, useState } from 'react';
import router from 'umi/router';
import { useTranslation } from 'react-i18next';
import i18n from '@/shared/i18n.js';

export const renderPrice = (forfait, hide = false, showOnlyCreditValue = false, t) => {
  if (forfait && forfait.creditCost && forfait.creditCost > 0 && showOnlyCreditValue) {
    return (
      <React.Fragment>
        {forfait.creditCost} {i18n.t('credit')}
        {forfait.creditCost > 1 ? 's' : ''}
      </React.Fragment>
    );
  }
  if (forfait && forfait?.paymentSettings?.hidePrice) {
    return '';
  }
  if (forfait && !showOnlyCreditValue) {
    return (
      <>
        {forfait.price}€
        {forfait.creditCost > 0 && (
          <>
            {' '}
            {i18n.t('general.or')} {forfait.creditCost} {i18n.t('credit')}
            {forfait.creditCost > 1 ? 's' : ''}
          </>
        )}
      </>
    );
  }
  return '';
};

export const getMonthlyPrice = (forfait) => {
  const monthlyPrice = (
    parseFloat(forfait.price) / forfait?.paymentSettings?.numberOfInstallments
  )?.toFixed(2);
  return monthlyPrice;
};
export const getInstallmentText = (forfait) => {
  return (
    <>
      Régler en plusieurs fois ({forfait?.paymentSettings?.numberOfInstallments} x{' '}
      {getMonthlyPrice(forfait)}€)
    </>
  );
};

export const renderCreditGiven = (forfait) => {
  if (forfait && forfait.creditGiven && forfait.creditGiven > 0) {
    return (
      <React.Fragment>
        <Tag style={{ marginBottom: '10px' }} color={'geekblue'}>
          {i18n.t('itGives')} {forfait.creditGiven} {i18n.t('credit')}
          {forfait.creditGiven > 1 ? 's' : ''} {i18n.t('toUseLater')}
        </Tag>
      </React.Fragment>
    );
  }
  return '';
};

export const DEFAULT_REGISTER_FIELDS = {
  name: { enable: false, mandatory: false },
  firstName: { enable: false, mandatory: false },
  addressline1: { enable: false, mandatory: false },
  postcode: { enable: false, mandatory: false },
  city: { enable: false, mandatory: false },
  phone: { enable: false, mandatory: false }
};

/**
 * UpdateForfait - Inscription/Réinscription à des Offres
 * Utilisé pour page /user/register, liens personnalisés d'inscriptions, et mettre à jour son abonnement.
 *
 * @param mesGroupes
 * @param withRegisterForm
 * @param withCustomLink
 * @param customLink
 * @param mesCredits
 * @param showChoiceSummary // Affiche le récapitulatif des choix
 * @param showOnlyCreditValue
 * @returns {JSX.Element}
 * @constructor
 */
export const UpdateForfait = ({
  mesGroupes = null,
  withRegisterForm = false, // Si true, active les champs obligatoire de base pour l'inscription (identifiant, mdp, email)
  withCustomLink = false,
  customLink,
  mesCredits = 0,
  showChoiceSummary = true,
  showOnlyCreditValue = false
}) => {
  const { t } = useTranslation();

  const getQuery = () =>
    withCustomLink ? QUERY_FORFAITS_CUSTOM_LINK : QUERY_MES_FORFAITS_DISPONIBLES;
  const getQueryOptions = () =>
    withCustomLink
      ? { fetchPolicy: 'no-cache', variables: { link: customLink } }
      : { fetchPolicy: 'no-cache', variables: { domain: getCurrentDomain() } };
  const { data, error, loading, refetch } = useQuery(getQuery(), getQueryOptions());
  const [Mutation, { loading: loadingMut }] = useMutation(MUTATION_UPDATE_FORFAIT_USER_TMP);

  const { data: dataConfig } = useQuery(GET_CONFIG, {
    fetchPolicy: 'no-cache',
    ...DEFAULT_CONFIG_VARIABLES
  });
  const config = dataConfig?.config;
  const companyInfosRaw = getValueFromKeyConfigData(config, CONFIG_KEYS.COMPANY_INFORMATION);
  const companyInfos = companyInfosRaw && JSON.parse(companyInfosRaw);
  const cgv = companyInfos?.linkToConditionsOfSales;
  const cgu = 'https://www.exoteach.com/cgu/';

  const [acceptedCGV, setAcceptedCGV] = useState(false);
  const [acceptedCGU, setAcceptedCGU] = useState(false);
  const [hasReachedLastStep, setHasReachedLastStep] = useState(false);

  // Steps
  const [current, setCurrent] = useState(0);

  const next = () => {
    setCurrent(current + 1);
  };
  const prev = () => {
    setCurrent(current - 1);
  };

  const getForfaitsDispo = () => {
    if (data) {
      if (showOnlyCreditValue) {
        return data?.mesForfaitsDisponibles?.filter((f) => f?.creditCost > 0);
      }
      return withCustomLink ? data.forfaitsFromCustomLink : data.mesForfaitsDisponibles;
    }
    return null;
  };

  const mesForfaitsDisponibles = getForfaitsDispo();
  const [form] = Form.useForm();

  const [forfaitsSelected, setForfaitsSelected] = useState([]);
  const [paymentMethod, setPaymentMethod] = useState('once');
  const [promoCodes, setPromoCode] = useState([]);
  const [prixTotal, setPrixTotal] = useState(0);
  const [creditsAvailable, setCreditsAvailable] = useState(0);

  // Custom fields file upload form input
  const [customFiles, setCustomFiles] = useState([
    /*
      {
        elementId: formItemName,
        file: file
      },
    */
  ]);

  const renderPromoCode = (forfait) => {
    const hasPromoCode = forfait?.hasPromoCode;
    return (
      <>
        {hasPromoCode && (
          <Form.Item label={t('PromoCode')}>
            <Input
              size="small"
              placeholder={t('PromoCode')}
              onChange={(e) => {
                const promoCodeWithoutActual =
                  promoCodes?.filter((p) => p.forfaitId !== forfait?.id) || [];
                const currentPromoCode = {
                  forfaitId: forfait?.id,
                  code: e.target.value
                };
                setPromoCode([...promoCodeWithoutActual, currentPromoCode]);
              }}
            />
          </Form.Item>
        )}
      </>
    );
  };

  useEffect(() => {
    setCreditsAvailable(parseInt(mesCredits));
  }, [mesCredits]);

  const getParentsForfaits = () => {
    if (!mesForfaitsDisponibles) {
      return [];
    }

    // Étape 1 : Collecter tous les IDs des forfaits disponibles
    const existingForfaitIds = new Set(mesForfaitsDisponibles?.map((f) => f.id));
    // Étape 2 : Filtrer les forfaits parents legacy (parentId === null)
    const parentForfaits = mesForfaitsDisponibles.filter((f) => f.parentId === null); // Enlever les forfaits avec parents de mesForfaitsDisponibles

    // Identifier les forfaits orphelins et les marquer comme 'UNIQUE'
    const orphanedForfaits = mesForfaitsDisponibles
      .filter((f) => f.parentId !== null && !existingForfaitIds.has(f.parentId))
      .map((f) => ({
        ...f,
        type: 'UNIQUE' // Marquer le forfait comme unique
      }));

    // Étape 5 : Composer la liste finale
    return [...parentForfaits, ...orphanedForfaits];

    /*
    if (!mesForfaitsDisponibles) {
      return [];
    }
    return mesForfaitsDisponibles; // Sont tous parents normalement ?
     */
    //return mesForfaitsDisponibles;
  };

  const handleFinish = async (formInput) => {
    // Next page Monetico payment or free update confirmation
    try {
      const formItems = JSON.parse(JSON.stringify(formInput));
      if (IS_DEV) {
        console.log(formItems?.customFields);
      }
      if (!isMobile && forfaitsSelected?.length === 0) {
        // Web-app not selecting forfait
        message.error(t('PleaseChooseAtLeastOnePackageToRegister'));
        return false;
      }
      const customFields = mapFormFieldsToCustomFields(formItems.customFields, customFiles);
      delete formItems.customFields;

      const result = await Mutation({
        variables: {
          forfaitsIds: forfaitsSelected.map((f) => f.id),
          userInput: withRegisterForm ? formItems : {},
          isMobile,
          promoCodes,
          customFields,
          paymentMethod
        }
      });
      // id temporary user (either from legacy monetico system or stripe)
      const dataResult = result.data.createTempUserForfaitSelection;
      const id = dataResult?.id;
      const defaultPaymentSystem = dataResult?.paymentSystem;

      if (isMobile || defaultPaymentSystem === 'stripe') {
        // Exoteach free or paid redirect
        router.push(`/user/register/confirm/${id}`);
      } else if (defaultPaymentSystem === 'monetico') {
        // Monetico legacy Payment redirect, REDIRECT TO PHP
        window.location = `${GlobalConfig.get().baseWebsiteUrl}${
          CONFIGS.legacyPhpHomePath
        }?page=inscription-confirm-exoteach&tmpId=${id}`;
      } else {
        alert(t('PaymentMethodUndefinedPleaseContactAdmin'));
      }
      // router.push(`/user/register/confirm/${id}`)
    } catch (e) {
      showGqlErrorsInMessagePopupFromException(e);
      console.error(e);
    }
  };

  const forfaitsCochables =
    mesForfaitsDisponibles && mesForfaitsDisponibles.filter((f) => f.type !== ForfaitTypes.Radio);

  /* for display only */
  const handleCreditConsume = (forfaitCreditCost) => {
    if (forfaitCreditCost !== 0 && forfaitCreditCost <= creditsAvailable) {
      /* Removing credits */
      setCreditsAvailable(creditsAvailable - forfaitCreditCost);
    }
  };
  const handleCreditUnconsume = (forfaitCreditCost) => {
    if (forfaitCreditCost !== 0) {
      /* adding credits */
      setCreditsAvailable(creditsAvailable + forfaitCreditCost);
    }
  };

  // Check/uncheck
  const onRadioForfaitCheck = (newForfait, olderPossibleForfaitCollection) => {
    const forfaitCreditCost = newForfait && parseInt(newForfait.creditCost);
    const fff = forfaitsSelected.filter(
      (f) => !olderPossibleForfaitCollection.some((of) => f.id === of.id)
    );
    if (newForfait) {
      /* Users check forfait */
      handleCreditConsume(forfaitCreditCost);
      setForfaitsSelected([...fff, newForfait]);

      // let forfaitToDisable = forfaitsSelected.find(f => f.id === newForfait.requiredForfaitId)
      // forfaitToDisable.isDisabled = true
      // setForfaitsSelected([...forfaitsSelected, ...forfaitToDisable])
    } else {
      /* Users unselect all forfaits child */
      olderPossibleForfaitCollection.forEach((f) => handleCreditUnconsume(parseInt(f.creditCost)));
      setForfaitsSelected([...fff]);
    }
  };
  const onCheckForfait = (e, forfait) => {
    const otherForfaits = forfaitsSelected.filter((f) => f.id !== forfait.id);
    const forfaitCreditCost = forfait && parseInt(forfait.creditCost);
    if (e.target.checked) {
      /* Users check forfait */
      // handleCreditConsume(forfaitCreditCost)
      setForfaitsSelected([...otherForfaits, forfait]); // Ajoute
    } else {
      /* Users un-check forfait */
      // handleCreditUnconsume(forfaitCreditCost)
      const forfaitsDependantsDeCeluiDecoched = forfaitsSelected.filter(
        (f) => f.requiredForfaitId === forfait.id
      );

      const otherForfaitsSansCeuxDependants = otherForfaits.filter(
        (f) => !forfaitsDependantsDeCeluiDecoched.map((d) => d.id).includes(f.id)
      );
      if (otherForfaitsSansCeuxDependants) {
        setForfaitsSelected([...otherForfaitsSansCeuxDependants]); // Enlève
      } else {
        setForfaitsSelected([...otherForfaits]); // Enlève
      }
      // Uncheck childs
    }
  };

  const [hasInit, setHasInit] = useState(false);

  /* Check default forfaits */
  useEffect(() => {
    if (forfaitsCochables && forfaitsCochables.length > 0 && !hasInit) {
      const fASelectionner = forfaitsCochables.filter((f) => f.defaultChecked === true);
      if (
        fASelectionner &&
        !forfaitsSelected.find((f) => fASelectionner.some((fas) => f.id === fas.id))
      ) {
        setForfaitsSelected([...forfaitsSelected, ...fASelectionner]);
        setHasInit(true);
      }
    }
  }, [forfaitsCochables]);

  /* Calcul total price on forfaits selected change */
  useEffect(() => {
    let creditsAvailableTmp = mesCredits;
    if (forfaitsSelected && forfaitsSelected.length > 0) {
      setPrixTotal(
        parseFloat(
          forfaitsSelected
            .map((f) => {
              const forfaitCreditCost = parseInt(f.creditCost);
              if (forfaitCreditCost !== 0) {
                // If we have enough credits
                if (creditsAvailableTmp >= forfaitCreditCost) {
                  // handleCreditConsume(forfaitCreditCost)
                  creditsAvailableTmp -= forfaitCreditCost;
                  return 0.0; // this one is free lel
                }
              }
              return parseFloat(f.price);
            })
            .reduce((acc, price) => acc + price)
        )
      );
    }
    setCreditsAvailable(creditsAvailableTmp);
    if (forfaitsSelected && forfaitsSelected.length === 0) {
      setPrixTotal(0);
    }
  }, [forfaitsSelected]);

  const isDisabled = (forfait) => {
    const shouldDisable =
      showOnlyCreditValue &&
      creditsAvailable === 0 &&
      !forfaitsSelected?.map((f) => f.id)?.includes(forfait.id);
    if (forfait.isLocked || shouldDisable) {
      return [true, null];
    }
    const hasRequiredForfait = forfait.requiredForfaitId !== null;
    if (hasRequiredForfait) {
      // todo add champ de retour dans l'API pour savoir
      const forfaitRequisDispo = mesForfaitsDisponibles.find(
        (f) => f.id === forfait.requiredForfaitId
      );
      const forfaitRequisSelected = forfaitsSelected.find(
        (f) => f.id === forfait.requiredForfaitId
      );
      // dans la liste et sélectionné
      if (forfaitRequisDispo && forfaitRequisSelected) {
        return [false, forfaitRequisDispo]; // forfait requis ET sélectionné
      }
      if (forfaitRequisDispo && !forfaitRequisSelected) {
        return [true, forfaitRequisDispo]; // forfait requis dispo ET PAS sélectionné
      }
      if (!forfaitRequisDispo) {
        // todo add champ de retour dans l'API pour savoir ou assumer que c'est ok
        return [false, forfaitRequisDispo];
      }
    }
    return [false, null];
  };

  const handleChooseInstallPaymentForForfait = (f) => {
    setForfaitsSelected([f]);
    setPaymentMethod('installments');
    next();
  };

  const renderForfaitUnique = (forfait) => {
    const [disabled, forfaitRequis] = isDisabled(forfait);
    const isAlreadySelected = !!forfaitsSelected?.find((f) => f?.id === forfait?.id);
    return (
      <Card style={{ width: '100%' }}>
        <div style={{ textAlign: 'center' }}>
          <ForfaitImage image={forfait.image} style={{ maxWidth: '70px', width: '60%' }} />
          {/* NAME */}
          <div style={{ fontSize: 24, fontWeight: 500 }}>
            {forfait.name} <ForfaitGroupsList forfait={forfait} />{' '}
          </div>
          {/* PRICE */}
          <div style={{ fontSize: 20, color: '#1987db', fontWeight: 500 }}>
            {renderPrice(forfait, withRegisterForm, showOnlyCreditValue, t)}
          </div>
        </div>

        {disabled && forfaitRequis && forfaitRequis.name && (
          <>
            {forfaitRequis.name} {t('general.required')}
          </>
        )}

        <div style={{ margin: 10 }}>
          <RenderQuillHtml>{forfait.description}</RenderQuillHtml>
          <span style={{ color: '#636363' }}>{renderCreditGiven(forfait)}</span>
        </div>

        {renderPromoCode(forfait)}

        {/* BUTTONS */}
        <div
          style={{
            textAlign: 'center',
            display: 'flex',
            flexWrap: 'wrap',
            flexDirection: 'row',
            gap: '6px',
            justifyContent: 'center'
          }}
        >
          <Button
            type={'primary'}
            danger={isAlreadySelected}
            size="large"
            disabled={disabled}
            onClick={() => {
              const e = { target: { checked: !isAlreadySelected } };
              onCheckForfait(e, forfait);
            }}
          >
            {isAlreadySelected ? 'Supprimer cette offre' : 'Ajouter cette offre'}
          </Button>
          {forfait?.paymentSettings?.enableInstallments && (
            <Button
              type="primary"
              size="large"
              onClick={() => handleChooseInstallPaymentForForfait(forfait)}
            >
              {getInstallmentText(forfait)}
            </Button>
          )}
        </div>

        {/*
        <Form.Item>
          <Checkbox
            disabled={disabled}
            value={forfait.id}
            defaultChecked={forfait.defaultChecked}
            onChange={e => onCheckForfait(e, forfait)}
          >
          </Checkbox>
        </Form.Item>
        */}
        <br />
      </Card>
    );
  };
  if (error && !loading) {
    return <ErrorResult refetch={refetch} error={error} />;
  }

  const renderRadioForfait = (forfait) => (
    <Card style={{ width: '100%' }}>
      <RadioForfait
        mesGroupes={mesGroupes}
        forfaitParent={forfait}
        forfaitsSelected={forfaitsSelected}
        mesForfaitsDisponibles={mesForfaitsDisponibles}
        onSelection={onRadioForfaitCheck}
        isDisabled={isDisabled(forfait)}
        withRegisterForm={withRegisterForm}
        handleChooseInstallPaymentForForfait={handleChooseInstallPaymentForForfait}
      />
    </Card>
  );

  const renderForfaitMultiple = (forfait) => (
    <Card style={{ width: '100%' }}>
      <MultipleForfaits
        mesGroupes={mesGroupes}
        forfaitParent={forfait}
        mesForfaitsDisponibles={mesForfaitsDisponibles}
        onSelection={onCheckForfait}
        isDisabled={isDisabled(forfait)}
        withRegisterForm={withRegisterForm}
        forfaitsSelected={forfaitsSelected}
        handleChooseInstallPaymentForForfait={handleChooseInstallPaymentForForfait}
      >
        {renderPromoCode(forfait)}
      </MultipleForfaits>
    </Card>
  );

  const renderCreditsAvailable = () => {
    if (mesCredits && mesCredits > 0)
      return (
        <div style={{ marginBottom: '15px' }}>
          {t('YouStillHave')}
          <b>
            <Tag color={'geekblue'}>
              {' '}
              {creditsAvailable} {t('credit')}
              {creditsAvailable > 1 ? 's' : ''}
            </Tag>
          </b>{' '}
          {t('toUse')}.
          <br />
        </div>
      );
    return '';
  };

  const renderChoiceSummary = () => {
    const isFirstStep = current === 0;
    const cardProps = isFirstStep
      ? {
          size: 'small'
        }
      : {
          title: t('YourChoice'),
          headStyle: cardHeadStyle,
          width: '100%'
        };
    return (
      <Card {...cardProps}>
        <div
          style={{
            display: 'flex',
            flexDirection: 'row',
            gap: '24px',
            justifyItems: 'space-between',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}
        >
          <div>
            {forfaitsSelected.map((forfait) => (
              <div key={forfait.id} style={{ display: 'flex', alignItems: 'center' }}>
                <ForfaitImage
                  image={forfait.image}
                  style={{ width: '25px', height: 'auto', marginRight: '8px' }}
                  alt={forfait.name}
                />
                {forfait.name}
              </div>
            ))}
            <br />
            {showOnlyCreditValue ? (
              <Statistic
                title={t('general.total')}
                value={mesCredits - creditsAvailable}
                precision={2}
                suffix="credits"
              />
            ) : (
              <Statistic title={t('general.total')} value={prixTotal} precision={2} suffix="€" />
            )}
          </div>
          {current === 0 && (
            <div>
              <Button
                disabled={forfaitsSelected?.length === 0}
                type="primary"
                onClick={() => next()}
              >
                {t('Next')}
              </Button>
            </div>
          )}
        </div>
      </Card>
    );
  };

  // Étape 1: Initialiser l'objet final avec les valeurs par défaut (enable: false, mandatory: false)
  const finalRegisterFields = DEFAULT_REGISTER_FIELDS;
  // Forfaits enabled fields from forfaitsChecked

  // TODO useMemo
  const selectedForfaitsRegisterFields = forfaitsSelected?.map(
    (f) => f?.paymentSettings?.registerFields
  );
  // Étape 2: Parcourir chaque objet registerFields dans selectedForfaitsRegisterFields
  selectedForfaitsRegisterFields?.forEach((registerFields) => {
    if (registerFields !== null && typeof registerFields === 'object') {
      // Étape 3: Mettre à jour les valeurs finales des attributs enable et mandatory pour chaque sous-objet
      Object.keys(registerFields)?.forEach((key) => {
        const field = registerFields?.[key];
        if (field.enable === true) {
          finalRegisterFields[key].enable = true;
        }
        if (field.mandatory === true) {
          finalRegisterFields[key].mandatory = true;
        }
      });
    }
  });

  const choiceSummary = (
    <>
      {showChoiceSummary && (
        <>
          <br />
          {renderChoiceSummary()}
        </>
      )}
    </>
  );

  const forfaitSelection = (
    <>
      <div style={{ display: 'flex', flexWrap: 'wrap', gap: '20px', justifyContent: 'center' }}>
        {getParentsForfaits() &&
          getParentsForfaits().map((forfait) => (
            <React.Fragment key={forfait.id}>
              {forfait.type === ForfaitTypes.Unique && (
                <div
                  style={{
                    flexBasis: 'calc(33% - 20px)',
                    display: 'flex',
                    minWidth: '250px',
                    flexGrow: 1
                  }}
                >
                  {renderForfaitUnique(forfait)}
                </div>
              )}
              {forfait.type === ForfaitTypes.Radio && (
                <div
                  style={{
                    flexBasis: 'calc(66% - 20px)',
                    display: 'flex',
                    minWidth: '250px',
                    flexGrow: 1
                  }}
                >
                  {renderRadioForfait(forfait)}
                </div>
              )}
              {forfait.type === ForfaitTypes.Multiple && (
                <div
                  style={{
                    flexBasis: 'calc(66% - 20px)',
                    display: 'flex',
                    minWidth: '250px',
                    flexGrow: 1
                  }}
                >
                  {renderForfaitMultiple(forfait)}
                </div>
              )}
            </React.Fragment>
          ))}
      </div>
    </>
  );

  const acceptConditions = (
    <>
      {/*
        TermsAndConditions
        TermsAndSellConditions
      */}
      <Form.Item>
        <Checkbox checked={acceptedCGV} onChange={() => setAcceptedCGV((v) => !v)}>
          {t('IAcceptThe')}{' '}
          <a href={cgv} target="_blank" rel="noopener noreferrer">
            {t('TermsAndSellConditions')}
          </a>
        </Checkbox>
      </Form.Item>
      <Form.Item>
        <Checkbox checked={acceptedCGU} onChange={() => setAcceptedCGU((v) => !v)}>
          {t('IAcceptThe')}{' '}
          <a href={cgu} target="_blank" rel="noopener noreferrer">
            {t('TermsAndConditions')}
          </a>
        </Checkbox>
      </Form.Item>
    </>
  );
  const hasAcceptedConditions = acceptedCGU && acceptedCGV;

  const buttonSendRegisterFields = (
    <Button
      disabled={!hasAcceptedConditions}
      size="large"
      type="primary"
      htmlType="submit"
      loading={loadingMut}
    >
      {t('general.confirm')}
    </Button>
  );

  const steps = [
    {
      title: "Choix de l'offre",
      content: (
        <>
          {forfaitSelection}
          <div style={{ position: 'sticky', bottom: '0' }}>{choiceSummary}</div>
        </>
      )
    },
    {
      title: 'Informations',
      content: (
        <>
          <Row justify="center" type="flex" key="1">
            <Col xl={20} lg={20} md={20} sm={24} xs={24}>
              {/* Tous les champs inscription sont des éléments */}
              <CustomFormItems
                customFiles={customFiles}
                setCustomFiles={setCustomFiles}
                forfaitsSelected={forfaitsSelected}
                setHasReachedLastStep={setHasReachedLastStep}
                promoCodes={promoCodes}
                withRegisterForm={withRegisterForm}
              />

              <Divider />
              <br />
              {/* Est sur la dernière step à l'interieur de customFormItems */}
              {hasReachedLastStep && <>{acceptConditions}</>}

              <Button
                style={{ marginRight: '8px' }}
                onClick={() => {
                  setForfaitsSelected([]);
                  setPromoCode([]);
                  setPrixTotal(0);
                  setCreditsAvailable(0);
                  setPaymentMethod('once');
                  prev();
                }}
              >
                {t('general.back')}
              </Button>

              {/* Est sur la dernière step à l'interieur de customFormItems */}
              {hasReachedLastStep && <>{buttonSendRegisterFields}</>}
            </Col>
          </Row>
        </>
      )
    },
    {
      // Juste pour faire joli, en vrai ça devient une autre page de paiement
      title: 'Validation',
      content: <></>
    }
  ];

  const items = steps.map((item) => ({ key: item.title, title: item.title }));
  const contentStyle = {
    marginTop: 16
  };

  return (
    <div style={{ marginLeft: '24px', marginRight: '24px' }}>
      {loading && !error && <SpinnerCentered />}
      {renderCreditsAvailable()}
      <Form
        layout="vertical"
        initialValues={{}}
        onFinish={handleFinish}
        form={form}
        size="large"
        onFieldsChange={(_, allFields) => {
          // IS_DEV && console.log({ allFields });
        }}
      >
        <div style={{ marginLeft: '24px', marginRight: '24px' }}>
          <Steps current={current} items={items} />
        </div>
        <div style={contentStyle}>{steps[current].content}</div>
      </Form>
    </div>
  );
};
