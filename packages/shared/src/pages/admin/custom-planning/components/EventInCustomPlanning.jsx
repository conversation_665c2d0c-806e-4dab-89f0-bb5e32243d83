import { ExoUserLight } from '@/shared/components/User/ExoUserLight.jsx';
import { QUERY_EVENT_BY_ID, QUERY_EVENTS_BY_IDS } from '@/shared/graphql/events.js';
import { BuildingName, RoomName } from '@/shared/pages/event/event.jsx';
import { CalendarOutlined, DeleteOutlined } from '@ant-design/icons';
import { useQuery } from '@apollo/client';
import { Button, Popover, Space, Table, Tag, Timeline } from 'antd';
import dayjs from 'dayjs';
import React from 'react';
import { useTranslation } from 'react-i18next';

export const CustomEventsTable = ({ eventIds, onRemove }) => {
  // Récupérez les données de tous les événements en une seule requête
  const { t } = useTranslation();
  const { loading, error, data } = useQuery(QUERY_EVENTS_BY_IDS, {
    variables: {
      ids: eventIds || [], // Remplacez cette ligne par la logique appropriée pour récupérer les IDs des événements
    },
    fetchPolicy: 'cache-and-network',
  });

  // Transformez les données en une source de données pour la table
  const eventData = data?.events || []; // Assurez-vous d'adapter ceci en fonction de la structure de vos données

  // Définissez les colonnes de la table
  const columns = [
    {
      title: t('Name'),
      dataIndex: 'name',
      key: 'name',
      render: (name, _) => <div style={{ fontWeight: 'bold' }}>{name}</div>,
    },
    {
      title: 'Type(s)',
      dataIndex: 'types',
      key: 'types',
      render: types =>
        types.map(t => (
          <Tag key={t.key} color="#108ee9">
            {t.name}
          </Tag>
        )),
    },
    {
      title: 'Dates',
      dataIndex: 'datesDiffusion',
      key: 'datesDiffusion',
      render: (datesDiffusion, exam) => (
        <>
          <Timeline
            style={{ marginBottom: '-20px' }}
            items={datesDiffusion?.map((dateDiff, key) => ({
              dot: <CalendarOutlined />,
              children: (
                <>
                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'row',
                      gap: '10px',
                      alignItems: 'center',
                      marginBottom: '5px',
                    }}
                  >
                    <div style={{ fontSize: '16px', fontWeight: '700' }}>
                      {dayjs(dateDiff?.date).format('Do MMMM YYYY')}{' '}
                      {dayjs(dateDiff?.date).isSame(dayjs(dateDiff?.dateEnd), 'day') ? (
                        <>
                          {dayjs(dateDiff?.date).format('HH:mm')}
                          {' à '}
                          {dayjs(dateDiff?.dateEnd).format('HH:mm')}
                        </>
                      ) : (
                        <>
                          {dayjs(dateDiff?.date).format('HH:mm')}
                          {' - '}
                          {dayjs(dateDiff?.dateEnd).format('Do MMMM YYYY')}{' '}
                          {dayjs(dateDiff?.dateEnd).format('HH:mm')}
                        </>
                      )}
                    </div>

                    <div>
                      <Tag color={'geekblue'}>{dateDiff?.participantsIds?.length} participants</Tag>
                    </div>

                    <Popover
                      content={
                        <div>
                          {dateDiff?.organizersIds?.map((organizer, k) => (
                            <div key={k}>
                              <ExoUserLight id={organizer} />
                            </div>
                          ))}
                        </div>
                      }
                      trigger="hover"
                    >
                      <Tag>
                        {dateDiff?.organizersIds?.length} {t('Organizers')}
                      </Tag>
                    </Popover>
                  </div>

                  {dateDiff?.buildingId && (
                    <div
                      style={{
                        display: 'flex',
                        flexDirection: 'row',
                        gap: '5px',
                        flexWrap: 'wrap',
                        minWidth: '300px',
                      }}
                    >
                      <div style={{ width: '100%' }}>
                        <div
                          style={{
                            display: 'flex',
                            flexDirection: 'row',
                            gap: '15px',
                            alignItems: 'center',
                          }}
                        >
                          {/* Replace these placeholders with your Building and Room components */}
                          🏛️
                          <BuildingName id={dateDiff.buildingId} />
                          <br />
                          🪑
                          <RoomName id={dateDiff?.roomId} />
                        </div>
                      </div>
                    </div>
                  )}
                </>
              ),
              key: dateDiff?.id,
            }))}
          />
        </>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (text, record) => (
        <Button
          onClick={() => {
            onRemove(record.id);
          }}

        >
          Supprimer de la liste
        </Button>
      ),
    },
  ];

  return <Table dataSource={eventData} columns={columns} loading={loading} pagination={false} />;
};

export const EventInCustomPlanning = ({ id, onRemove }) => {
  // Query event by id with QUERY_EVENT_BY_ID
  const { loading, error, data } = useQuery(QUERY_EVENT_BY_ID, {
    variables: { id },
    fetchPolicy: 'cache-and-network',
  });
  const event = data?.event;

  return (
    <Space direction={'horizontal'}>
      <div>
        <b>{event?.name}</b>
      </div>
      <div>
        {event?.types.map(t => (
          <Tag key={t.key} color="#108ee9">
            {t.name}
          </Tag>
        ))}
      </div>
      <div>
        <Button
          onClick={() => {
            onRemove(id);
          }}
        >
          <DeleteOutlined />
        </Button>
      </div>
    </Space>
  );
};
