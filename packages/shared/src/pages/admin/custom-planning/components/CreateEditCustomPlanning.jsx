import {
  MUT<PERSON>ION_ADD_GROUP_TO_CUSTOM_PLANNING,
  MUTATION_CREATE_CUSTOM_PLANNING,
  MUTATION_EDIT_CUSTOM_PLANNING,
  MUTATION_REMOVE_GROUP_FROM_CUSTOM_PLANNING,
} from '@/shared/graphql/customPlanning.js';
import { MUTATION_ADD_GROUPE_DATE_DIFFUSION, MUTATION_REMOVE_GROUPE_DATE_DIFFUSION } from '@/shared/graphql/edt.js';
import { GlobalContext } from '@/shared/layouts/BlankLayout.jsx';
import { CustomPlanningGroupsSelector } from '@/shared/pages/admin/custom-planning/components/CustomPlanningGroupsSelector.jsx';
import {
  CustomEventsTable,
} from '@/shared/pages/admin/custom-planning/components/EventInCustomPlanning.jsx';
import { ModalType } from '@/shared/pages/admin/folders/modals/CreateEditFolderModal.jsx';
import AbstractGroupsManager from '@/shared/pages/admin/permissions/AbstractGroupsManager.jsx';
import IndividualPermissionsManager from '@/shared/pages/admin/permissions/IndividualPermissionsManager.jsx';
import { AdminEventsTable } from '@/shared/pages/event/admin/allEvents.jsx';
import { getLanguageName, tr } from '@/shared/services/translate.js';
import { DeleteOutlined } from '@ant-design/icons';
import { useMutation } from '@apollo/client';
import { Space, Button, Form, Input, message, Modal, Tabs, Timeline, TimePicker, Divider } from 'antd';
import dayjs from 'dayjs';
import React, { useContext, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

const getMutationFromModalType = modalType => {
  switch (modalType) {
    case ModalType.CREATE:
      return MUTATION_CREATE_CUSTOM_PLANNING;
    case ModalType.UPDATE:
      return MUTATION_EDIT_CUSTOM_PLANNING;
    default:
      return MUTATION_CREATE_CUSTOM_PLANNING;
  }
};

export const CreateEditCustomPlanning = ({
  closeModalHandler,
  isVisible,
  modalType,
  refetch,
  record,
}) => {
  const { t, i18n } = useTranslation();
  const { enabledLanguages } = useContext(GlobalContext);
  const [selectedLanguage, setSelectedLanguage] = useState(i18n.language);

  const [form] = Form.useForm();
  const [Mutation, { loading, data, error }] = useMutation(getMutationFromModalType(modalType));

  const [addGroupToCustomPlanning] = useMutation(MUTATION_ADD_GROUP_TO_CUSTOM_PLANNING);
  const [removeGroupFromCustomPlanning] = useMutation(MUTATION_REMOVE_GROUP_FROM_CUSTOM_PLANNING);

  const [steps, setSteps] = useState([]);
  const [rowToEdit, setRowToEdit] = useState(null);
  const [eventSelectVisible, setEventSelectVisible] = useState(false);

  useEffect(() => {
    setSteps(record?.steps || []);
  }, [record]);

  const handleFinish = async data => {
    try {
      let newCustomPlanning = { ...data };

      newCustomPlanning.steps = steps;

      if (modalType === ModalType.UPDATE) {
        await Mutation({
          variables: { customPlanningId: record?.id, customPlanning: newCustomPlanning },
        });
        message.success(t('Updated'));
      } else {
        // Create
        await Mutation({ variables: { customPlanning: newCustomPlanning } });
        message.success('Dossier créé!');
      }
      await closeModalHandler();
    } catch (e) {
      console.error(e);
      message.error('Erreur serveur, veuillez réessayer');
    }
  };

  const groups = record?.groupes?.filter(g => g?.isIndividual === false) || [];
  const individualGroups = record?.groupes?.filter(g => g?.isIndividual === true) || [];

  const onAddStep = () => {
    setSteps([
      ...steps,
      {
        hoursMinutes: dayjs(),
        hoursMinutesEnd: dayjs().add(1, 'hour'),
        eventIds: [],
        j: 0, // number of days before / after
      },
    ]);
  };

  const onDelete = index => {
    const newSteps = [...steps];
    newSteps.splice(index, 1);
    setSteps(newSteps);
  };

  return (
    <Modal
      title={t('CustomPlanning')}
      open={isVisible}
      onCancel={closeModalHandler}
      footer={null}
      width={1200}
    >
      <Form
        form={form}
        layout="vertical"
        name="form_in_modal"
        initialValues={record}
        onFinish={handleFinish}
      >
        <Tabs defaultActiveKey={i18n.language}>
          {enabledLanguages &&
            enabledLanguages?.map(lang => (
              <Tabs.TabPane tab={getLanguageName(lang)} key={lang}>
                <Form.Item name={tr('name', lang)} label={t('Title')}>
                  <Input type="text" placeholder={t('Title')} />
                </Form.Item>
                <Form.Item name={tr('description', lang)} label={t('Description')}>
                  <Input.TextArea rows={3} type="text" placeholder="" />
                </Form.Item>

                <Form.Item
                  name={tr('explanation', lang)}
                  label={t('CustomPlanningExplanationLabel')}
                >
                  <Input type="text" placeholder={t('CustomPlanningExplanationPlaceholder')} />
                </Form.Item>
              </Tabs.TabPane>
            ))}
        </Tabs>

        <br />
        <h2>Qui peut ajouter cet évènement à son planning ?</h2>

        <div style={{ marginLeft: '24px' }}>
          <Form.Item label="Sélectionnez les groupes">
            <AbstractGroupsManager
              //entityName={}
              entityId={record?.id}
              groupes={groups}
              addGroupMutation={MUTATION_ADD_GROUP_TO_CUSTOM_PLANNING}
              removeGroupMutation={MUTATION_REMOVE_GROUP_FROM_CUSTOM_PLANNING}
              groupParameterName="groupId"
              entityParameterName="customPlanningId"
            />

          </Form.Item>

          <Form.Item label="Sélectionnez les utilisateurs">
            <div style={{ marginBottom: '20px' }}>
              <IndividualPermissionsManager
                showText={false}
                individualGroups={individualGroups}
                onAdd={async individualGroupId => {
                  await addGroupToCustomPlanning({
                    variables: {
                      customPlanningId: record.id,
                      groupId: individualGroupId,
                    },
                  });
                  await refetch();
                }}
                onRemove={async individualGroupId => {
                  await removeGroupFromCustomPlanning({
                    variables: {
                      customPlanningId: record.id,
                      groupId: individualGroupId,
                    },
                  });
                  await refetch();
                }}
              />
            </div>
          </Form.Item>
        </div>

        <h2>Planning personnalisé</h2>

        <div style={{ marginLeft: '24px' }}>
          <Timeline
            items={
              Array.isArray(steps) &&
              steps?.map((step, k) => ({
                children: (
                  <div>
                    {/* Component for children? */}
                    <div>
                      <Space.Compact size="large">
                        <Input
                          addonBefore={`J${step?.j > 0 ? '+' : ''}`}
                          value={step?.j}
                          onChange={e => {
                            // update step.js
                            const newSteps = [...steps];
                            newSteps[k].j = e.target.value;
                            setSteps(newSteps);
                          }}
                        />
                        <TimePicker
                          format={'HH:mm'}
                          value={dayjs(step?.hoursMinutes, 'HH:mm')}
                          onChange={(times, f) => {
                            const time = times;
                            // update step.hoursMinutes
                            const newSteps = [...steps];
                            newSteps[k].hoursMinutes = time.format('HH:mm');
                            //newSteps[k].hoursMinutesEnd = timeEnd.format('HH:mm');
                            setSteps(newSteps);
                          }}
                        />
                        <TimePicker
                          format={'HH:mm'}
                          value={dayjs(step?.hoursMinutesEnd, 'HH:mm')}
                          onChange={(times, f) => {
                            const time = times;
                            // update step.hoursMinutes
                            const newSteps = [...steps];
                            newSteps[k].hoursMinutesEnd = time.format('HH:mm');
                            //newSteps[k].hoursMinutesEnd = timeEnd.format('HH:mm');
                            setSteps(newSteps);
                          }}
                        />
                        {/* Heure de fin, fuseau horaire, bâtiment */}
                        <Button onClick={() => onDelete(k)}>
                          <DeleteOutlined />
                        </Button>
                      </Space.Compact>
                    </div>

                    <CustomEventsTable
                      eventIds={step?.eventIds}
                      onRemove={(eventIdToRemove) => {
                        // Remove eventId from step
                        const newSteps = [...steps];
                        newSteps[k].eventIds = newSteps[k].eventIds.filter(id => id !== eventIdToRemove);
                        setSteps(newSteps);
                      }}
                    />

                    <br />

                    <Button onClick={() => {
                      setEventSelectVisible(true);
                      setRowToEdit(k);
                    }}>
                      Ajouter un évènement...
                    </Button>

                  </div>
                ),
              }))
            }
          />

          <Divider />
          <Button size={'large'} type="primary" onClick={onAddStep}>
            Ajouter une date (J-X ou J+X)
          </Button>
        </div>

        {/* Modal admin events et importation event */}
        <Modal
          title={t('Events')}
          open={eventSelectVisible}
          onCancel={() => {
            setEventSelectVisible(false)
          }}
          closable
          footer={null}
          width={'100%'}
        >
          <AdminEventsTable
            mode={'import'}
            folderRouting={false}
            onImport={(event)=> {
              // Add event.id to steps
              const k = rowToEdit;
              const newSteps = [...steps];
              newSteps[k].eventIds = [...newSteps[k].eventIds, event.id];
              setSteps(newSteps);

              setEventSelectVisible(false);
            }}
          />
        </Modal>

        <br />
        <div style={{ marginTop: '24px' }}>
          <Button size={'large'} type={'primary'} onClick={() => form.submit()}>
            Sauvegarder ce planning personnalisé
          </Button>
        </div>
      </Form>
    </Modal>
  );
};
