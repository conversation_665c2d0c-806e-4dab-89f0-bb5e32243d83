import { useParams } from 'react-router';
import { useMutation, useQuery } from '@apollo/client';
import {
  MUTATION_ADMIN_REPLACE_IS_DEFAULT_FOR_SCALE,
  MUTATION_DELETE_MCQ_SCALE,
  QUERY_TUTOR_OR_ADMIN_QUESTION_TYPE_SCALE
} from '@/shared/graphql/qcm';
import { onErrorShowErrorsFunction } from '@/shared/utils/utils';
import NoFoundPage from '@/shared//pages/404';
import {
  Avatar,
  Button,
  Drawer,
  notification,
  Popover,
  Table,
  Tag,
  Tooltip,
  Typography
} from 'antd';
import FullMediParticlesBreadCrumb from '@/shared/components/FullMediParticlesBreadCrumb';
import React, { useContext, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ExoAvatarQcmUser } from '@/shared/components/User/ExoAvatarQcmUser';
import { ArrowLeft, Info, Pencil, ScrollText } from 'lucide-react';
import { useHistory } from 'react-router-dom';
import {
  CreateEditBaremeModal,
  ModalType
} from '@/shared/pages/admin/qcm/bareme/modal/CreateEditBaremeModal';
import { ExoUserLight, ExoUserLightDisplayModes } from '@/shared/components/User/ExoUserLight';
import radioImage from '@/shared/assets/GIFs/RADIO.gif';
import freeTextImage from '@/shared/assets/GIFs/FREE_TEXT.gif';
import alphanumericalOrNumericalImage from '@/shared/assets/GIFs/ALPHANUMERICAL_OR_NUMERICAL.gif';
import schemaPointAndClickImage from '@/shared/assets/GIFs/SCHEMA_POINT_AND_CLICK.gif';
import fillInTheBlanksImage from '@/shared/assets/GIFs/FILL_IN_THE_BLANKS.gif';
import flashcardImage from '@/shared/assets/GIFs/FLASHCARD.gif';
import { DeleteOutlined, StarFilled, StarOutlined } from '@ant-design/icons';
import { confirmModalAsFunction } from '@/shared/components/ConfirmModalAsFunction';
import { GlobalContext } from '@/shared/layouts/BlankLayout';
import UserLogs from '@/shared/components/User/UserLogs';
import { TableUeCell } from '@/shared/components/Table/TableUeCell';

const { Title, Text } = Typography;

//TODO Je le fais en parallèle d'une autre branch qui implémente ce dico dans le front. Lorsque les deux branches seront merge il faudra centraliser ce dico
export const McqScaleQuestionType = {
  MultipleChoice: 'mcq',
  UniqueChoice: 'ucq',
  Alphanumerical: 'alphanumerical',
  FreeText: 'freetext',
  Schema: 'schema',
  SchemaFillInLegends: 'schemaFillInLegends',
  FillInTheBlanks: 'fillintheblanks',
  FLASHCARD: 'FLASHCARD'
};

export const MAPPING_SCALE_GIF = {
  mcq: radioImage,
  ucq: radioImage,
  alphanumerical: alphanumericalOrNumericalImage,
  freetext: freeTextImage,
  schema: schemaPointAndClickImage,
  schemaFillInLegends: schemaPointAndClickImage,
  fillintheblanks: fillInTheBlanksImage,
  FLASHCARD: flashcardImage
};

const SubBareme = () => {
  const { t } = useTranslation();
  const { appearance } = useContext(GlobalContext);
  const primaryColor = appearance?.primaryColor;
  const { questionType } = useParams();
  const skip = !Object.values(McqScaleQuestionType)?.includes(questionType);
  const history = useHistory();
  const [createEditBaremeModalOpen, setCreateEditBaremeModalOpen] = useState(false);
  const [isLogsOpen, setIsLogsOpen] = useState(false);
  const [scaleIdToShowLog, setScaleIdToShowLog] = useState(null);

  useEffect(() => {
    if (scaleIdToShowLog !== null) {
      setIsLogsOpen(true);
    }
  }, [scaleIdToShowLog]);

  const [createEditBaremeModalArgs, setCreateEditBaremeModalArgs] = useState(null); // Doit contenir : mcqScale et modalType
  const numberOfLastLogs = 20;

  useEffect(() => {
    if (createEditBaremeModalArgs !== null) {
      setCreateEditBaremeModalOpen(true);
    }
  }, [createEditBaremeModalArgs]);

  const callEditBareme = (mcqScale) => {
    const callArgs = {
      mcqScale,
      modalType: ModalType.UPDATE
    };

    setCreateEditBaremeModalArgs(callArgs);
  };

  const callCreateBareme = (questionType) => {
    const callArgs = {
      createQuestionType: questionType,
      modalType: ModalType.CREATE
    };
    setCreateEditBaremeModalArgs(callArgs);
  };

  const BASE_URL = '/admin/bareme/';
  const goToBareme = () => {
    history.push(`${BASE_URL}`);
  };

  const {
    loading,
    data: { getQuestionTypeMcqScales = [] } = {},
    error,
    refetch
  } = useQuery(QUERY_TUTOR_OR_ADMIN_QUESTION_TYPE_SCALE, {
    fetchPolicy: 'no-cache',
    variables: { questionType, numberOfLastLogs },
    onError: onErrorShowErrorsFunction,
    notifyOnNetworkStatusChange: true,
    skip
  });

  const [changeIsDefault] = useMutation(MUTATION_ADMIN_REPLACE_IS_DEFAULT_FOR_SCALE, {
    onError: (error) => onErrorShowErrorsFunction(error)
  });
  const [deleteMcqScaleMutation] = useMutation(MUTATION_DELETE_MCQ_SCALE, {
    onError: (error) => onErrorShowErrorsFunction(error)
  });

  const callChangeIsDefault = async ({ questionType, scaleId }) => {
    try {
      await changeIsDefault({ variables: { mcqScaleId: scaleId, questionType } });
      await refetch();
    } catch (e) {
      throw new Error(e);
    }
  };

  const callDeleteScale = async ({ scaleId }) => {
    try {
      await deleteMcqScaleMutation({ variables: { id: scaleId } });
      await refetch();
    } catch (e) {
      throw new Error(e);
    }
  };

  const defaultScale = getQuestionTypeMcqScales?.find((subScale) => subScale?.isDefault) || {};
  const otherScales = getQuestionTypeMcqScales?.filter((subScale) => !subScale?.isDefault) || [];

  if (skip) {
    return <NoFoundPage />;
  }

  const showWarningIfScaleLinkedToExercises = () => {
    //
    const linkToMassUpdate = '#/admin/qcm/masschanges';

    // Génération du contenu du lien
    const linkElement = (
      <a href={linkToMassUpdate} target="_blank" rel="noopener noreferrer">
        link
      </a>
    );

    // Configuration de la notification
    const notificationConfig = {
      message: t('Scale.NotificationCantDeleteScaleTitle'),
      description: (
        <>
          {t('Scale.NotificationCantDeleteScaleExplanation')} {linkElement}
        </>
      ),
      duration: 5,
      placement: 'topRight'
    };

    // Affichage de la notification
    notification.error(notificationConfig);
  };

  // Columns for the table
  const columns = [
    {
      title: 'id',
      dataIndex: 'id',
      key: 'id',
      render: (id) => <div>{id}</div>,
      onCell: () => ({ style: { width: '50px' } }),
      onHeaderCell: () => ({ style: { width: '50px' } })
    },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (text) => <Text>{text}</Text>,

      onCell: () => ({ style: { width: '200px' } }),
      onHeaderCell: () => ({ style: { width: '200px' } })
    },
    {
      title: 'Nb exercices',
      dataIndex: 'numberOfLinkedExercises',
      key: 'numberOfLinkedExercises',
      render: (data) => <div>{JSON.stringify(data)}</div>,
      onCell: () => ({ style: { width: '150px' } }),
      onHeaderCell: () => ({ style: { width: '150px' } })
    },
    {
      title: 'UEs',
      dataIndex: 'ues',
      key: 'ues',
      render: (ues) => <TableUeCell data={ues} />
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (data) => {
        return (
          <div style={{ display: 'flex', flexDirection: 'row', gap: '5px' }}>
            <Tooltip title={t('Scale.EditButtonLabel')}>
              <Button
                shape={'circle'}
                type={'primary'}
                onClick={() => {
                  callEditBareme(data);
                }}
              >
                <Pencil />
              </Button>
            </Tooltip>

            {!data?.isDefault && (
              <Tooltip title={t('Scale.DefineDefaultButtonLabel')}>
                <Button
                  shape={'circle'}
                  type={'primary'}
                  onClick={async () => {
                    confirmModalAsFunction({
                      onConfirm: async () => {
                        await callChangeIsDefault({ questionType, scaleId: data?.id });
                      },
                      title: <h1>{t('Scale.ModalConfirmationChangeDefaultScaleTitle')}</h1>,
                      description: (
                        <>
                          {t('Scale.ModalConfirmationChangeDefaultScaleExplanation', {
                            type: t(`ScaleKey.${questionType}`)
                          })}
                        </>
                      ),
                      useCheckbox: true
                    });
                  }}
                >
                  <StarOutlined />
                </Button>
              </Tooltip>
            )}

            {data?.isDefault && (
              <Tooltip title={t('Scale.ShowLabelIsDefault')}>
                <span
                  style={{
                    display: 'inline-block', // ou block, au besoin
                    pointerEvents: 'auto', // capture le survol pour le tooltip
                    cursor: 'default' // pas de curseur "main" (doigt)
                  }}
                >
                  <Button
                    shape="circle"
                    type="primary"
                    style={{
                      pointerEvents: 'none' // aucune interaction sur le bouton
                    }}
                  >
                    <StarFilled style={{ color: 'yellow' }} />
                  </Button>
                </span>
              </Tooltip>
            )}

            <Tooltip title={t('Scale.ShowLogModalButtonLabel')}>
              <Button
                shape={'circle'}
                type={'primary'}
                onClick={() => {
                  setScaleIdToShowLog(data?.id);
                }}
              >
                <ScrollText />
              </Button>
            </Tooltip>

            {!data?.isDefault && (
              <Tooltip title={t('Delete')}>
                <Button
                  shape="circle"
                  danger
                  icon={<DeleteOutlined />}
                  onClick={() => {
                    if (data?.numberOfLinkedExercises !== 0) {
                      showWarningIfScaleLinkedToExercises();
                    } else {
                      confirmModalAsFunction({
                        onConfirm: () => {
                          callDeleteScale({ scaleId: data?.id });
                        },
                        title: <h1>{t('Scale.ModalConfirmationDeleteScaleTitle')}</h1>,
                        description: <>{t('Scale.ModalConfirmationDeleteScaleExplanation')}</>,
                        useCheckbox: true
                      });
                    }
                  }}
                />
              </Tooltip>
            )}
          </div>
        );
      },
      onCell: () => ({ style: { width: '200px' } }),
      onHeaderCell: () => ({ style: { width: '200px' } })
    },
    {
      title: 'Auteur',
      dataIndex: 'author',
      key: 'author',
      render: (user) => {
        return (
          <div style={{ display: 'flex', flexDirection: 'row' }}>
            <ExoAvatarQcmUser
              username={user?.username}
              id={user?.id}
              withUsername={user?.username}
            />
          </div>
        );
      },
      onCell: () => ({ style: { width: '150px' } }),
      onHeaderCell: () => ({ style: { width: '150px' } })
    },
    {
      title: 'last updaters',
      dataIndex: 'logs',
      key: 'logs',
      render: (logs) => {
        return (
          <Avatar.Group maxCount={5}>
            {[...new Set(logs?.map(({ reporteeUserId }) => reporteeUserId))].map(
              (reporteeUserId) => (
                <ExoUserLight
                  key={reporteeUserId}
                  id={reporteeUserId}
                  displayMode={ExoUserLightDisplayModes.avatarOnly}
                />
              )
            )}
          </Avatar.Group>
        );
      },
      onCell: () => ({ style: { width: '150px' } }),
      onHeaderCell: () => ({ style: { width: '150px' } })
    }
  ];

  return (
    <>
      <FullMediParticlesBreadCrumb
        title={
          <div style={{ display: 'flex', alignItems: 'end' }}>
            <ArrowLeft
              style={{ zIndex: 1, cursor: 'pointer' }}
              size={28}
              onClick={() => goToBareme()}
            />
            &nbsp;
            {t('ExercicesScale')} : {t(`ScaleKey.${questionType}`)}
          </div>
        }
      />

      <div
        style={{ display: 'flex', flexDirection: 'column', padding: '10px', alignItems: 'center' }}
      >
        <div
          style={{
            display: 'flex',
            alignItems: 'baseline',
            justifyContent: 'start',
            width: '100%'
          }}
        >
          <Title
            strong={true}
            style={{
              color: primaryColor,
              fontWeight: '30px'
            }}
          >
            {t('Scale.ModalTitleDefaultScale')}
          </Title>
          &nbsp;
          <Popover
            content={t('Scale.ModalTitleDefaultScalePopoverExplanation', {
              type: t(`ScaleKey.${questionType}`)
            })}
            trigger={['hover', 'click']}
          >
            <Info />
          </Popover>
        </div>

        {defaultScale?.id ? (
          <div style={{ margin: '20px', width: '100%' }}>
            <Table
              dataSource={[defaultScale]}
              columns={columns}
              tableLayout={'fixed'}
              scroll={{ x: 'auto' }}
            />
          </div>
        ) : (
          <Tag color={'purple'}>
            <Title level={2} style={{ margin: '0px', padding: '0px' }}>
              {' '}
              Pas de scale par default{' '}
            </Title>
          </Tag>
        )}

        <Drawer
          open={isLogsOpen}
          placement={'right'}
          onClose={() => {
            setScaleIdToShowLog(null);
            setIsLogsOpen(false);
          }}
          onCancel={() => {
            setScaleIdToShowLog(null);
            setIsLogsOpen(false);
          }}
          destroyOnClose={true}
          size={'large'}
        >
          <UserLogs
            scaleId={scaleIdToShowLog}
            withTypeSelector={false}
            showUser={true}
            canShowAll
          />
        </Drawer>

        <br />
        <br />
        <div
          style={{
            display: 'flex',
            alignItems: 'self-end',
            marginTop: '15px',
            width: '100%'
          }}
        >
          <Title
            strong={true}
            style={{
              color: primaryColor,
              fontWeight: '30px',
              margin: 0
            }}
          >
            {t('Scale.ModalOtherScales')}
          </Title>
          &nbsp;
          <Button
            type={'primary'}
            onClick={() => {
              callCreateBareme(questionType);
            }}
          >
            {t('Scale.ModalCreateNewScale')}
          </Button>
        </div>

        <div style={{ margin: '20px', width: '100%' }}>
          <Table dataSource={otherScales} columns={columns} />
        </div>

        {createEditBaremeModalArgs && (
          <CreateEditBaremeModal
            isVisible={createEditBaremeModalOpen}
            closeModalHandler={async () => {
              setCreateEditBaremeModalOpen(false);
              setCreateEditBaremeModalArgs(null);
              await refetch();
            }}
            modalType={createEditBaremeModalArgs?.modalType}
            mcqScale={createEditBaremeModalArgs?.mcqScale}
            createQuestionType={createEditBaremeModalArgs?.createQuestionType}
          />
        )}
      </div>
    </>
  );
};

export default SubBareme;
