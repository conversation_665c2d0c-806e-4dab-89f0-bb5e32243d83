import FullMediParticlesBreadCrumb from '@/shared/components/FullMediParticlesBreadCrumb.jsx';
import EditFullSchema from '@/shared/pages/admin/schemas/components/EditFullSchema.jsx';
import { PictureOutlined } from '@ant-design/icons';
import { Breadcrumb } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'umi';

export default function EditSchemaPage(props) {
  const { t } = useTranslation();
  const schemaId = props.match.params.id;

  const [breadcrumbTitle, setBreadcrumbTitle] = React.useState(t('Schemas.SchemasLibrary'));

  return (
    <>
      <FullMediParticlesBreadCrumb title={breadcrumbTitle} />
      <Breadcrumb style={{ marginLeft: '12px', fontWeight: 'bold' }}>
        <Breadcrumb.Item>
          <Link to="/admin-schemas">
            <PictureOutlined /> Bibliothèque de Schémas
          </Link>
        </Breadcrumb.Item>
      </Breadcrumb>
      <div style={{ margin: 24 }}>
        <EditFullSchema schemaId={schemaId} setBreadcrumbTitle={setBreadcrumbTitle} />
      </div>
    </>
  );
}
