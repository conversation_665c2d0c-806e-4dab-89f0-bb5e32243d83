import { EditAICard } from '@/shared/pages/admin/users/editUser/EditAICard';
import { gql, useMutation, useQuery } from '@apollo/client';
import { ModalType } from '@/shared/pages/admin/forfaits/components/CreateEditForfaitModal';
import {
  Alert,
  Button,
  Card,
  Divider,
  Form,
  Input,
  message,
  Modal,
  Popconfirm,
  Select,
  Space,
  Table,
  Tag,
  Tooltip
} from 'antd';
import { useTranslation } from 'react-i18next';
import {
  useExoteachCompaniesQuery,
  useExoteachIntegrationsQuery
} from '@/shared/hooks/config/useConfigHooks';
import React, { useContext, useEffect, useState } from 'react';
import { CONFIG_KEYS } from '@/shared/services/config';
import { tryParseJSONObject } from '@/shared/utils/utils';
import { ErrorResult } from '@/shared/components/ErrorResult';
import { allGPTModels } from '@/shared/pages/admin/ai/index';
import { SpinnerCentered } from '@/shared/components/PageLoading/SpinnerCentered';
import { ExoUserLight, ExoUserLightDisplayModes } from '@/shared/components/User/ExoUserLight';
import { Link, router } from 'umi';
import { EditOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import SearchUser from '@/shared/components/User/SearchUser';
import { GlobalContext } from '@/shared/layouts/BlankLayout';
import { useEffectScrollTop } from '@/shared/utils/hooks/useEffectScrollTop';

export const QUERY_AI_CONFIGS = gql`
  query AiSettings {
    aiConfigs {
      id
      key
      value {
        model
        templates
        temperature
        top_p
        max_tokens
        frequency_penalty
        presence_penalty
        logit_bias
        user
        aiUserId
        integrationId
        companyId
      }
      createdAt
      updatedAt
      domain
    }
  }
`;

export const MUTATION_CREATE_AI_CONFIG = gql`
  mutation createGPTConfig($input: GPTConfigInput!, $username: String!) {
    createGPTConfig(input: $input, username: $username)
  }
`;
export const MUTATION_UPDATE_AI_CONFIG = gql`
  mutation updateGPTConfig($id: ID!, $input: GPTConfigInput!) {
    updateGPTConfig(id: $id, input: $input)
  }
`;
export const MUTATION_DELETE_AI_CONFIG = gql`
  mutation deleteGPTConfig($id: ID!) {
    deleteGPTConfig(id: $id)
  }
`;

const CreateEditIntegrationModal = ({ isVisible, handleCloseModal, config, modalType }) => {
  const [Mutation, { loading, error }] = useMutation(
    modalType === ModalType.UPDATE ? MUTATION_UPDATE_AI_CONFIG : MUTATION_CREATE_AI_CONFIG
  );

  const [form] = Form.useForm();
  const { t } = useTranslation();

  const { integrationsConfig } = useExoteachIntegrationsQuery();
  const [chatGptIntegrationId, setChatGptIntegrationId] = useState(null);

  const { companyInfos } = useExoteachCompaniesQuery();

  const defaultValues = {
    model: 'gpt-4o',
    templates: {
      exerciseQuestion:
        "\nVoici une question d’un étudiant sur un exercice de type {{questionTypeString}}, la question porte sur {{array_linkedCoursesString}}.\nPour te donner le contexte : \n\nÉnoncé du {{questionTypeString}}: \n{{enonceQuestion}}\nPropositions du {{questionTypeString}}: \n{{answersToString}}\n\nLa proposition concernée par la question de l'étudiant est : {{currentAnswerLetter}}\n\nVoici la question de l'élève à répondre :\n\n{{questionTitle}} : {{questionContent}} \n \n{{precisionPromptForChatGPT}}\n{{botPersonnalityPromptFragment}}",
      courseQuestion:
        '\nVoici une question d’un étudiant sur le thème de {{coursName}} : {{coursDescription}}\n{{questionTitle}} : {{questionContent}}\n{{precisionPromptForChatGPT}}\n{{botPersonnalityPromptFragment}}\n',
      questionImportation:
        '\nVoici un QCM qui comprend un énoncé et plusieurs questions. \nJe veux que tu transformes ce QCM dans le format JSON suivant, en prenant soin de mettre isTrue à true pour les réponses justes et à false pour les réponses fausses, d\'après les lettres de la correction. Si il y a plusieurs questions ajoute les à la suite en respectant l\'ordre dans le tableau JSON.\n      \n      [{\n        "question": "Titre de la question",\n        "question_answers": [\n          {\n            "text": "Réponse 1",\n            "isTrue": true,\n            "explanation": "Explication de la réponse 1, si il y en a une."\n          },\n          {\n            "text": "Réponse 2",\n            "isTrue": false,\n            "explanation": "Explication de la réponse 2, si il y en a une."\n          },\n        ]\n      }]\n      \n      Il est très important de respecter ce format JSON, tu ne dois pas le modifier. Ne renvoie que le JSON dans la réponse et rien d’autre. Voici un exemple de ce qui est attendu: \n      \n        "Parmi les propositions suivantes, laquelle ou lesquelles sont exactes :\n         A. Les mitochondries sont bleu \n         B. Les oiseaux sont verts\n         C. Le chat miaule\n         D. Le ciel est gris en permanence\n         E. Le bras et plus grand que l’avant bras.\n         Réponses : ACE"\n       Donnera en JSON : \n       [{\n        "question": "Parmi les propositions suivantes, laquelle ou lesquelles sont exactes :",\n        "question_answers": [\n          {\n            "text": "Les mitochondries sont bleu",\n            "isTrue": true\n          },\n          {\n            "text": "Les oiseaux sont verts",\n            "isTrue": false\n          },\n          {\n            "text": "Le chat miaule",\n            "isTrue": true\n          },\n          {\n            "text": "Le ciel est gris en permanence",\n            "isTrue": false\n          },\n          {\n            "text": "Le bras et plus grand que l’avant bras.",\n            "isTrue": true\n          },\n        ]\n      }]\n      \n      Voici l’énoncé du QCM à transformer dans le format JSON évoqué :\n      {{subjectWithCorrection}}\n      '
    },
    user: 'Exoteach'
  };

  const onFinish = async (data) => {
    try {
      if (modalType === ModalType.CREATE) {
        if (!chatGptIntegrationId) {
          message.error('Vous devez sélectionner une intégration Chat GPT');
          return;
        }
        if (!data.username) {
          message.error("Vous devez saisir un nom d'utilisateur");
          return;
        }
        if (!data.companyId) {
          message.error(t('EnterCompanyNameForfaitForm'));
          return;
        }

        const valueObject = {
          ...defaultValues,
          integrationId: chatGptIntegrationId,
          companyId: data.companyId
        };

        await Mutation({
          variables: {
            username: data.username,
            input: valueObject
          }
        });
      } else {
        // Update

        await Mutation({
          variables: {
            id: config.id,
            value: JSON.stringify(data)
          }
        });
      }
      handleCloseModal();
    } catch (e) {
      console.error(e);
    }
  };

  return (
    <Modal
      title={modalType === ModalType.CREATE ? t('general.add') : t('Edit')}
      open={isVisible}
      onCancel={handleCloseModal}
      footer={null}
      destroyOnClose
    >
      <Form form={form} name="basic" onFinish={onFinish}>
        <>
          <h2>Créer un nouvel utilisateur IA</h2>
          <p>
            Un utilisateur IA est un utilisateur qui peut générer du texte pour répondre
            automatiquement aux questions des apprenants. Il est possible de créer plusieurs
            utilisateurs IA pour avoir plusieurs modèles différents.
          </p>

          <br />

          <Form.Item name={'username'} label={"Nom d'utilisateur"}>
            <Input />
          </Form.Item>

          <Form.Item label={'Intégration Chat GPT'}>
            <Select
              value={chatGptIntegrationId}
              style={{ minWidth: '300px' }}
              placeholder={'Sélectionnez une intégration Chat GPT'}
              onChange={(value, option) => setChatGptIntegrationId(value)}
            >
              {integrationsConfig &&
                integrationsConfig
                  ?.filter((c) => c.key === CONFIG_KEYS.CHAT_GPT_INTEGRATION)
                  ?.map((integration, index) => {
                    const value = tryParseJSONObject(integration?.value);
                    return (
                      <Select.Option value={integration?.id} key={integration?.id}>
                        {value?.name}
                      </Select.Option>
                    );
                  })}
            </Select>
          </Form.Item>

          <Form.Item label={t('general.Company')} name="companyId">
            <Select mode="multiple">
              {companyInfos?.map((companyInfo) => (
                <Select.Option key={companyInfo?.id} value={companyInfo?.id}>
                  {companyInfo?.commercialName}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <br />
          <p>Les permissions et autres réglages seront disponibles après création</p>
          <br />
        </>

        {error && <ErrorResult error={error} />}

        <Form.Item>
          <Button loading={loading} type="primary" onClick={() => form.submit()}>
            {t('general.save')}
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
};

const EditConfig = ({ id, config, integrationsConfig }) => {
  const { t, i18n } = useTranslation();
  const selectedModel = config?.model;

  const [updateAIConfig, { loading: loadingMutation }] = useMutation(MUTATION_UPDATE_AI_CONFIG);
  const [deleteAIConfig, { loading: loadingMutationDelete }] =
    useMutation(MUTATION_DELETE_AI_CONFIG);
  const [form] = Form.useForm();
  const [chatGptIntegrationId, setChatGptIntegrationId] = useState(config?.integrationId);

  const [selectedUserId, setSelectedUserId] = useState(config?.aiUserId);
  const [showUserSearch, setShowUserSearch] = useState(false);

  const { companyInfos, loading: loadingCompany } = useExoteachCompaniesQuery();

  useEffect(() => {
    setChatGptIntegrationId(config?.integrationId);
    setSelectedUserId(config?.aiUserId);
  }, [config]);

  const handleFinish = async (values) => {
    if (!values?.companyId || values?.companyId?.length <= 0) {
      message.error(t('EnterCompanyNameForfaitForm'));
      return;
    }
    // Récupération du modèleId
    const trueModelName=values?.model

    // model, templates
    const input = {
      model: trueModelName,
      user: values.user,
      aiUserId: selectedUserId,
      templates: {
        //exerciseQuestion: values.exerciseQuestion,
        //courseQuestion: values.courseQuestion,
        questionImportation: values.questionImportation
      },
      companyId: values.companyId,
      integrationId: chatGptIntegrationId
    };
    await updateAIConfig({
      variables: {
        id,
        input
      }
    });
    message.success(t('general.Updated!'));
  };

  if (loadingCompany) {
    return <SpinnerCentered />;
  }
  return (
    <>
      <Card title={t('ConfigIA')}>
        <br />
        {selectedUserId ? (
          <Space direction={'horizontal'}>
            <Space>
              {!showUserSearch && (
                <Space direction="horizontal">
                  <ExoUserLight
                    id={selectedUserId}
                    displayMode={ExoUserLightDisplayModes.avatarOnly}
                  />
                  <Link to={`/admin/users/edit/${selectedUserId}`}>
                    <Button size={'large'} icon={<EditOutlined />}>
                      {t('CustomMyIA')}&nbsp;
                      <ExoUserLight
                        id={selectedUserId}
                        displayMode={ExoUserLightDisplayModes.usernameOnly}
                      />
                    </Button>

                    <Tooltip title={t('CustomMyIAInfo')}>
                      <QuestionCircleOutlined style={{ marginLeft: '10px', cursor: 'pointer' }} />
                    </Tooltip>
                  </Link>
                </Space>
              )}

              {showUserSearch && (
                <div style={{ marginBottom: '20px', textAlign: 'center' }}>
                  <SearchUser
                    botsOnly
                    onSelectUser={async (value, option) => {
                      setSelectedUserId(option?.key);
                      setShowUserSearch(false);
                    }}
                  />
                </div>
              )}

              {/* Trop confusant pour les admins
                <Button
                  type="dashed"
                  block
                  onClick={() => setShowUserSearch(!showUserSearch)}
                  icon={showUserSearch ? null : <UserOutlined />}
                >
                  {showUserSearch ? t('Cancel') : t('Change')}
                </Button>
              */}
            </Space>
          </Space>
        ) : (
          'Aucun utilisateur IA, contactez un admin'
        )}
        <br />
        <br />
        <br />

        <Form.Item label={t('OpenAIKey')}>
          <Select
            value={chatGptIntegrationId}
            style={{ minWidth: '300px' }}
            placeholder={t('OpenAIKey')}
            onChange={(value, option) => setChatGptIntegrationId(value)}
          >
            {integrationsConfig &&
              integrationsConfig
                ?.filter((c) => c.key === CONFIG_KEYS.CHAT_GPT_INTEGRATION)
                ?.map((integration, index) => {
                  const value = tryParseJSONObject(integration?.value);
                  return (
                    <Select.Option value={integration?.id} key={integration?.id}>
                      {value?.name}
                    </Select.Option>
                  );
                })}
          </Select>
        </Form.Item>
        {!chatGptIntegrationId && (
          <Alert message={t('YouMustChooseOpenAIKey')} type="error" showIcon />
        )}

        <Form
          // layout="vertical"
          layout="vertical"
          onFinish={handleFinish}
          form={form}
          initialValues={{
            model: config?.model,
            user: config?.user,
            //exerciseQuestion: config?.templates?.exerciseQuestion,
            //courseQuestion: config?.templates?.courseQuestion,
            questionImportation: config?.templates?.questionImportation,
            companyId: config?.companyId || null
          }}
        >
          <Form.Item name={'model'} label={'Modèle'}>
            <Select
              placeholder="Choisir le modèle"
              dropdownMatchSelectWidth={false}
            >
              {allGPTModels?.map((n) => (
                <Select.Option key={n.key} value={n.key}>{n.name} </Select.Option>
              ))}
            </Select>
          </Form.Item>
          <br />
          <Form.Item
            name={'user'}
            label={
              'Nom utilisateur qui apparaît dans votre API openAI (facultatif, permet de suivre les requêtes)'
            }
          >
            <Input />
          </Form.Item>

          {companyInfos && (
            <Form.Item label={t('general.Company')} name="companyId">
              <Select mode="multiple">
                {companyInfos?.map((companyInfo) => (
                  <Select.Option key={companyInfo?.id} value={companyInfo?.id}>
                    {companyInfo?.commercialName}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          )}

          {/*
          <Divider orientation={"left"}>Prompts</Divider>
          <Alert
            message="Les prompts sont des phrases qui servent à l'IA pour générer le texte. Attention à ne pas supprimer les mots clés qui sont entre crochets."
            type="info"
            showIcon
          />
          */}
          <br />
          {/*
          <Form.Item name={'exerciseQuestion'} label={'Question sur un exercice'}>
            <Input.TextArea rows={10} />
          </Form.Item>
          <Form.Item name={'courseQuestion'} label={'Question sur un cours'}>
            <Input.TextArea rows={10} />
          </Form.Item>
          */}
          {/*
          <Form.Item name={'questionImportation'} label={'Importation exercice 1'}>
            <Input.TextArea rows={10} />
          </Form.Item>
          */}
          <Form.Item>
            <Button htmlType="submit" type="primary" loading={loadingMutation}>
              {t('Update')}
            </Button>
          </Form.Item>

          <Form.Item>
            <Popconfirm
              title={t('SureToDelete')}
              onConfirm={async () => {
                try {
                  await deleteAIConfig({
                    variables: {
                      id: id
                    }
                  });
                  message.success(t('general.SuccessfullyDeleted'));
                  router.push(`/admin/ai-settings`);
                } catch (e) {
                  console.error(e);
                }
              }}
            >
              <Button loading={loadingMutationDelete} danger>
                {t('Delete')}
              </Button>
            </Popconfirm>
          </Form.Item>
        </Form>
      </Card>
    </>
  );
};

export default function AiUsers(props) {
  const { t, i18n } = useTranslation();
  const { enabledLanguages } = useContext(GlobalContext);
  const [selectedLanguage, setSelectedLanguage] = useState(i18n.language);
  const [createNewAi, setCreateNewAi] = useState(false);

  useEffectScrollTop();
  const { loading, error, data, refetch } = useQuery(QUERY_AI_CONFIGS, {
    fetchPolicy: 'no-cache'
  });

  const { integrationsConfig } = useExoteachIntegrationsQuery();

  const configs = data?.aiConfigs;

  const configId = props.match.params?.id;
  const configToEdit = configId && configs?.find((c) => c.id === configId)?.value;

  const { companyInfos } = useExoteachCompaniesQuery();

  return (
    <>
      {configToEdit ? (
        <>
          <Button onClick={() => router.push(`/admin/ai-settings`)}>{t('back')}</Button>
          <br />
          <br />
          <EditConfig integrationsConfig={integrationsConfig} config={configToEdit} id={configId} />
        </>
      ) : (
        <>
          <Button onClick={() => setCreateNewAi(true)}>Créer une nouvelle IA</Button>

          {createNewAi && (
            <CreateEditIntegrationModal
              handleCloseModal={() => {
                setCreateNewAi(false);
                refetch();
              }}
              isVisible={createNewAi}
              modalType={ModalType.CREATE}
              refetch={refetch}
              loading={loading}
            />
          )}
          <br />

          <Table
            dataSource={configs}
            loading={loading}
            rowKey={(record) => record.id}
            pagination={false}
            columns={[
              {
                title: t('AIUser'),
                render: (text, record) => {
                  const userbotId = record?.value?.aiUserId;
                  return (
                    <div
                      onClick={() => router.push(`/admin/ai-settings/${record.id}`)}
                      style={{ cursor: 'pointer' }}
                    >
                      <ExoUserLight id={userbotId} />
                    </div>
                  );
                }
              },
              {
                title: t('general.Properties'),
                render: (text, record) => {
                  const integrationConfig = integrationsConfig?.find(
                    (c) => c.id === record?.value?.integrationId
                  );
                  const companyIds = record?.value?.companyId || []; // S'assurer que c'est un tableau
                  const integrationName = tryParseJSONObject(integrationConfig?.value)?.name;

                  return (
                    <Space>
                      <Tag>{record?.value?.model}</Tag>
                      <Tag>{integrationName}</Tag>
                      {companyIds.map((id) => {
                        const commercialName =
                          companyInfos.find((obj) => obj.id === id)?.commercialName || 'N/A';
                        return <Tag key={id}>{commercialName}</Tag>;
                      })}
                    </Space>
                  );
                }
              },
              {
                title: 'Actions',
                render: (text, record) => {
                  return (
                    <Space>
                      <Button
                        type="primary"
                        onClick={() => router.push(`/admin/users/edit/${record?.value?.aiUserId}`)}
                      >
                        {t('general.Edit')}
                      </Button>
                    </Space>
                  );
                }
              }
            ]}
          />
        </>
      )}
    </>
  );
}
