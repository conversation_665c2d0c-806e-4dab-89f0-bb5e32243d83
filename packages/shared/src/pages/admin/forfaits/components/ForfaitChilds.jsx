import RenderQuillHtml from '@/shared/components/ExoQuill/RenderQuillHtml';
import {
  CREATE_FORFAIT,
  MUTATION_ADD_CHILD_TO_FORFAIT,
  MUTATION_REMOVE_CHILD_FROM_FORFAIT,
  QUERY_SEARCH_FORFAITS
} from '@/shared/graphql/forfaits.js';
import { ForfaitImage } from '@/shared/pages/account/forfait/components/ForfaitImage.jsx';
import {
  CreateEditForfaitModal,
  ForfaitTypes,
  ModalType
} from '@/shared/pages/admin/forfaits/components/CreateEditForfaitModal.jsx';
import { ForfaitActions } from '@/shared/pages/admin/forfaits/components/ForfaitActions.jsx';
import { renderForfaitType, SubscriptionTableList } from '@/shared/pages/admin/forfaits/index.jsx';
import {
  displayDirectHtml,
  showGqlErrorsInMessagePopupFromException
} from '@/shared/utils/utils.js';
import { PlusOutlined } from '@ant-design/icons';
import { useMutation, useQuery } from '@apollo/client';
import { Button, Dropdown, List, Modal, Popover, Space, Table, Tag } from 'antd';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'umi';

export const ForfaitChilds = ({ forfaitId, typeForfait }) => {
  const { t } = useTranslation();
  // Get forfaits where parentId = forfaitId

  const { data, loading, error, refetch } = useQuery(QUERY_SEARCH_FORFAITS, {
    variables: {
      filter: {
        parentId: forfaitId
      }
    }
  });

  const [createForfait, { loading: loadingCreation }] = useMutation(CREATE_FORFAIT);

  const [addChildToForfait, { loading: loadingAddChild }] = useMutation(
    MUTATION_ADD_CHILD_TO_FORFAIT
  );
  const [removeChildFromForfait, { loading: loadingRemoveChild }] = useMutation(
    MUTATION_REMOVE_CHILD_FROM_FORFAIT
  );

  const [currentForfaitToEdit, setCurrentForfaitToEdit] = useState(null);
  const [editVisible, setEditVisible] = useState(false);
  const [importVisible, setImportVisible] = useState(false);

  const childsForfaits = data && data.searchForfaits;

  const columns = [
    {
      title: 'id',
      dataIndex: 'id',
      key: 'id'
    },
    {
      title: 'Image',
      dataIndex: 'image',
      key: 'image',
      render: (image, record) => (
        <ForfaitImage key={record.id} image={image} style={{ maxHeight: '50px' }} />
      )
    },
    {
      title: t('TitleAndDescription'),
      dataIndex: 'name',
      key: 'name',
      render: (name, record) => {
        return (
          <>
            <b>{name}</b>
            <br />
            <div style={{ overflow: 'hidden', height: '50px', width: '100%', fontSize: '10px' }}>
              <RenderQuillHtml>{record.description}</RenderQuillHtml>
            </div>
          </>
        );
      }
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      render: (type) => (
        <span style={{ fontSize: '16px', fontWeight: 'bold' }}>
          {type ? renderForfaitType(type) : 'inconnu'}
        </span>
      )
    },
    {
      title: 'Donne accès à',
      dataIndex: 'groupes',
      key: 'groupes',
      width: '140px',
      render: (groupes, record) => {
        const groupCount = groupes?.length || 0;
        return (
          <Popover
            content={
              <div style={{ maxHeight: '200px', overflowY: 'auto', width: '300px' }}>
                <List
                  dataSource={groupes}
                  renderItem={(groupe) => (
                    <List.Item key={groupe.id}>
                      <List.Item.Meta
                        title={<Link to={`/admin/users/ingroup/${groupe.id}`}>{groupe.name}</Link>}
                      />
                    </List.Item>
                  )}
                />
              </div>
            }
            title={t('general.Groups')}
            trigger="click"
          >
            <Button
              style={{
                // whiteSpace: '',
                // overflow: 'hidden',
                textOverflow: 'ellipsis',
                maxWidth: '100%'
              }}
            >
              {groupCount || 0} groupe{groupCount > 1 ? 's' : ''}
            </Button>
          </Popover>
        );
      }
    },
    {
      title: 'Action',
      key: 'action',
      render: (text, record) => (
        <ForfaitActions
          record={record}
          refetch={refetch}
          key={record.id}
          settings={{
            editable: true,
            deletable: true,
            unlinkable: true,
            importable: false,
            duplicable: true,
            handleUnlink: handleUnlink
          }}
        />
      )
    }
  ];

  if (!forfaitId) {
    return 'Erreur: Forfait parent invalide, créer le forfait puis éditez le pour ajouter des choix';
  }

  const handleAddButtonClick = async (e) => {
    const { key } = e;
    if (key === '1') {
      const result = await createForfait({
        variables: {
          forfait: {
            type: ForfaitTypes.Unique,
            parentId: forfaitId
          }
        }
      });
      const forfaitCreated = result?.data?.createForfait;
      if (forfaitCreated) {
        setCurrentForfaitToEdit(forfaitCreated);
        setEditVisible(true);
      }
    }
    if (key === '2') {
      setImportVisible(true);
    }
  };

  const handleAddExisting = async (forfaitIdToAdd) => {
    try {
      await addChildToForfait({
        variables: {
          forfaitId: forfaitId,
          childId: forfaitIdToAdd
        }
      });
      setImportVisible(false); // Close modal
      refetch();
    } catch (e) {
      console.error(e);
      showGqlErrorsInMessagePopupFromException(e);
    }
  };
  const handleUnlink = async (forfaitIdToRemove) => {
    try {
      await removeChildFromForfait({
        variables: {
          forfaitId: forfaitId,
          childId: forfaitIdToRemove
        }
      });
      refetch();
    } catch (e) {
      console.error(e);
      showGqlErrorsInMessagePopupFromException(e);
    }
  };

  // Show forfait list and reuse CRUD but with parentId
  return (
    <>
      <Dropdown
        menu={{
          items: [
            {
              label: 'Créer une nouvelle offre',
              key: '1'
            },
            {
              label: 'Ajouter offre existante',
              key: '2'
            }
          ],
          onClick: handleAddButtonClick
        }}
      >
        <Button>
          <Space>
            {t('general.add')}
            <PlusOutlined />
          </Space>
        </Button>
      </Dropdown>
      <br />
      <br />

      {/* Créer nouvelle offre children */}
      {editVisible && currentForfaitToEdit && (
        <CreateEditForfaitModal
          forfait={currentForfaitToEdit}
          isVisible={editVisible}
          modalType={ModalType.UPDATE}
          closeModalHandler={() => {
            setEditVisible(false);
            refetch();
          }}
          refetch={refetch}
          loading={loading}
        />
      )}

      {/* Add existing forfait as a child */}
      {importVisible && (
        <Modal
          open={importVisible}
          onCancel={() => {
            setImportVisible(false);
            refetch();
          }}
          footer={null}
          closable
          width={1000}
        >
          <SubscriptionTableList
            editable={false}
            deletable={false}
            importable
            showCreateButtons={false}
            simpleOffersOnly
            handleAddExisting={handleAddExisting}
          />
        </Modal>
      )}

      {/* Child offers list */}
      <Table
        loading={loading}
        columns={columns}
        dataSource={childsForfaits}
        scroll={{ x: true }}
        pagination={{
          defaultPageSize: 50
        }}
      />
    </>
  );
};
