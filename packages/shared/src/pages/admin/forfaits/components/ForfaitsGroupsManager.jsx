import { QUERY_ALL_FOLDERS } from '@/shared/graphql/folders.js';
import { ADD_GROUP_TO_FORFAIT, REMOVE_GROUP_FROM_FORFAIT } from '@/shared/graphql/forfaits.js';
import { mapGroupsForTreeSelection } from '@/shared/services/groupes.js';
import { useMutation, useQuery } from '@apollo/client';
import { QUERY_ALL_GROUPS } from '@/shared/graphql/cours.js';
import { message, Tag, TreeSelect } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';

export const ForfaitsGroupsManager = ({ groupes, forfait }) => {
  const {t} = useTranslation();
  const dataGroupes = useQuery(QUERY_ALL_GROUPS, { fetchPolicy: 'cache-and-network' });
  const [addGroupToUE, addGroupData] = useMutation(ADD_GROUP_TO_FORFAIT);
  const [removeGroupToUE, removeGroupData] = useMutation(REMOVE_GROUP_FROM_FORFAIT);

  // All Folders query
  const {
    loading: loadingFolders,
    error: errorFolders,
    data: dataFolders,
    refetch: refetchFolders,
  } = useQuery(QUERY_ALL_FOLDERS, { fetchPolicy: 'cache-and-network' });
  const folders = dataFolders?.folders;

  const allGroupes = dataGroupes?.data?.allGroupes;
  const foldersIds = allGroupes?.map(gr => gr?.folderId);
  const foldersToShow = folders?.filter(f => foldersIds?.includes(f.id));

  const groupeTagRender = ({ label, value, closable, onClose, key }) => (
    <Tag value={value} key={key} color="geekblue" closable={closable} onClose={onClose} style={{ marginRight: 3 }}>
      {label}
    </Tag>
  );

  const handleSelect = async (_, option) => {
    try {
      await addGroupToUE({ variables: { forfaitId: forfait.id, groupId: option.key } });
      message.success(`Groupe ${option.value} inclus dans ${forfait.name}`);
    } catch (e) {
      message.error(`Groupe ${option.value} n'a PAS été ajouté à ${forfait.name} `);
      console.error(e);
    }
  };
  const handleDeselect = async (_, option) => {
    try {
      await removeGroupToUE({ variables: { forfaitId: forfait.id, groupId: option.key } });
      message.success(`Groupe ${option.value} n'est plus inclus dans ${forfait.name}`);
    } catch (e) {
      message.error(`Groupe ${option.value} n'a PAS été supprimé de ${forfait.name} `);
      console.error(e);
    }
  };
  const handleChange = async (shouldAdd, groupId) => {
    const option = { key: groupId, value: allGroupes.find(g => g.id === groupId)?.name };
    if (shouldAdd) {
      await handleSelect(null, option);
    } else {
      await handleDeselect(null, option);
    }
  };

  return (
    <>
      {allGroupes && foldersToShow && groupes && (
        <TreeSelect
          treeCheckable
          placeholder={t('ChooseGroups')}
          treeNodeFilterProp="title"
          treeData={mapGroupsForTreeSelection(foldersToShow, allGroupes)}
          defaultValue={groupes?.map(groupe => ({ key: groupe.id, title: groupe.name, value: groupe?.id }))}
          style={{ width: '100%' }}
          onChange={async (newValue, label, extra) => {
            const groupId = extra.triggerValue;
            const shouldAdd = extra.checked;
            if (groupId?.startsWith('folder')) {
              // Find groups in folder
              const folder = mapGroupsForTreeSelection(foldersToShow, allGroupes)?.find(g => g?.key === groupId);
              // Multiple group
              for (const group of folder.children) {
                handleChange(shouldAdd, group?.key);
              }
            } else {
              // Single group
              handleChange(shouldAdd, groupId);
            }
          }}
          tagRender={allGroupes && groupeTagRender}
        />
      )}
    </>
  );
};