import { MUTATION_DELETE_FOLDER } from '@/shared/graphql/folders.js';
import { CreateEditFolderModal } from '@/shared/pages/admin/folders/modals/CreateEditFolderModal.jsx';
import { ModalType } from '@/shared/pages/admin/forfaits/components/CreateEditForfaitModal.jsx';
import { showGqlErrorsInMessagePopupFromException } from '@/shared/utils/utils.js';
import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { useMutation } from '@apollo/client';
import { Button, message, Popconfirm } from 'antd';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

export const FolderActions = ({ refetch, record, loading }) => {
  const {t} = useTranslation();
  const [editVisible, setEditVisible] = useState(false)
  const [deleteFolder] = useMutation(MUTATION_DELETE_FOLDER)
  const handleDelete = async id => {
    try {
      await deleteFolder({ variables: { id } })
      message.success(t('DeletedWithSuccess'))
      refetch()
    } catch (e) {
      showGqlErrorsInMessagePopupFromException(e)
    }
  }

  return (
    <span>
      <Button
        onClick={() => {
          setEditVisible(true)
        }}
        style={{ marginRight: 16 }}
        type="primary"
        shape="circle"
        icon={<EditOutlined/>}
      />
      <CreateEditFolderModal
        folder={record}
        type={record.type}
        parentId={record.parentId}
        isVisible={editVisible}
        modalType={ModalType.UPDATE}
        closeModalHandler={() => {
          setEditVisible(false)
          refetch() // Load new modifications
        }}
      />
      <Popconfirm
        title={t('SureOfDeletion')}
        onConfirm={() => handleDelete(record.id)}
      >
        <Button
          shape="circle"
          style={{ marginRight: 16 }}
          danger
          icon={<DeleteOutlined/>}
        />
      </Popconfirm>
    </span>
  )
}