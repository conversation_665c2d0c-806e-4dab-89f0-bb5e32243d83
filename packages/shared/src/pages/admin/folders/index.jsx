import { QUERY_ALL_FOLDERS } from '@/shared/graphql/folders.js';
import { FolderActions } from '@/shared/pages/admin/folders/components/FolderActions.jsx';
import { CreateEditFolderModal } from '@/shared/pages/admin/folders/modals/CreateEditFolderModal.jsx';
import { ModalType } from '@/shared/pages/admin/forfaits/components/CreateEditForfaitModal.jsx';
import React, { useState } from 'react';
import { useQuery } from '@apollo/client';
import { Button, Table } from 'antd';
import { ErrorResult } from '@/shared/components/ErrorResult';
import { useTranslation } from 'react-i18next';
import FullMediParticlesBreadCrumb from '@/shared/components/FullMediParticlesBreadCrumb';

export const AdminFolders = (props) => {
  const { t } = useTranslation();
  const { loading, error, data, refetch } = useQuery(QUERY_ALL_FOLDERS, {
    fetchPolicy: 'cache-and-network'
  });
  const folders = data?.folders;
  const [createVisible, setCreateVisible] = useState(false);

  const closeModalHandler = () => {
    refetch(); // Load new modifications
    setCreateVisible(false);
  };
  const openModalHandler = () => {
    setCreateVisible(true);
  };

  const columns = [
    {
      title: 'id',
      dataIndex: 'id',
      key: 'id'
    },
    {
      title: t('Name'),
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: 'Action',
      key: 'action',
      render: (text, record) => <FolderActions record={record} refetch={refetch} key={record.id} />
    }
  ];

  return (
    <>
      <Button onClick={openModalHandler}>{t('general.add')}</Button>
      <CreateEditFolderModal
        closeModalHandler={closeModalHandler}
        isVisible={createVisible}
        modalType={ModalType.CREATE}
        refetch={refetch}
        loading={loading}
      />

      {!error && data && (
        <Table
          loading={loading}
          columns={columns}
          dataSource={folders}
          scroll={{ x: true }}
          pagination={{
            defaultPageSize: 50
          }}
        />
      )}
      {error && !loading && <ErrorResult refetch={refetch} error={error} />}
    </>
  );
};

export default function (props) {
  const { t } = useTranslation();
  return (
    <>
      <FullMediParticlesBreadCrumb title={t('general.Folders')} />
      <AdminFolders />
    </>
  );
}
