import { ErrorResult } from '@/shared/components/ErrorResult.jsx';
import { SpinnerCentered } from '@/shared/components/PageLoading/SpinnerCentered.jsx';
import { QUERY_FOLDERS_TREE_DATA } from '@/shared/graphql/folders.js';
import { NO_OP_MUTATION } from '@/shared/graphql/groupes.js';
import { FOLDER_TYPES } from '@/shared/hooks/folders/useQueryFolders.jsx';
import i18n from 'i18next';
import React, { useState } from 'react';
import { useMutation, useQuery } from '@apollo/client';
import { message, Tag, Tree, TreeSelect } from 'antd';

/**
 * AbstractGroupsManager
 * - Affiche un TreeSelect pour gérer les groupes d'une entité (cours, forfait, etc.)
 *
 * @param entityName
 * @param entityId
 * @param entityParameterName
 * @param groupParameterName
 * @param groupes
 * @param addGroupMutation
 * @param removeGroupMutation
 * @param onChange
 * @param enableFolderChecking
 * @param treeCheckable
 * @param showNumberOfUsersInGroup
 * @returns {JSX.Element}
 * @constructor
 */
const AbstractGroupsManager = ({
  entityName = 'cet élément', // Nom de l'entité en DB ("Joli cours", "Forfait 1er semestre", etc.)
  entityId, // id of the entity, ex: cours.id, forfait.id
  entityParameterName, // Par exemple: 'coursId', 'forfaitId'
  groupParameterName = 'groupId', // Souvent, 'groupId'

  groupes = [], // Groupes déjà liés à l'entité (ex: cours.groupes), nécessite au moins un champ

  addGroupMutation = NO_OP_MUTATION, // Mutation pour ajouter un groupe à l'entité (ex: addGroupToCours)
  removeGroupMutation = NO_OP_MUTATION, // Mutation pour supprimer un groupe de l'entité (ex: removeGroupFromCours)

  onChange = null, // Si on ne veut pas utiliser de mutation, on peut utiliser cette fonction pour gérer les changements. ATTENTION Si non null, ignore les mutations
  onChangeArray = null, // Si on ne veut pas utiliser de mutation, on peut utiliser cette fonction pour gérer les changements (envoi ids groupes en array)
  enableFolderChecking = true, // Si true, permet de cocher/décocher un dossier entier. Déconseillé avec onChange défini
  treeCheckable = true, // Si false, permet de cocher/décocher plusieurs groupes, sinon 1 seul à la fois.
  showNumberOfUsers = false,

  style = null,
  placeholder = i18n.t('ChooseGroups'),
  disabled = false,
  useTree = false, // ✅ Nouvelle prop pour choisir entre TreeSelect et Tree
  isForeground = false
}) => {
  const [addGroupToEntity] = useMutation(addGroupMutation);
  const [removeGroupFromEntity] = useMutation(removeGroupMutation);

  // Transform groupes to treeData, if needed. If groupes is already treeData, do nothing
  let groupsMapped = [];
  if (groupes.length > 0 && groupes?.[0].type !== 'GROUP') {
    groupsMapped = groupes?.map((group) => ({
      title: group?.name,
      value: `GROUP-${group.id}`,
      key: `GROUP-${group.id}`,
      id: group.id,
      type: 'GROUP',
      checkable: true
    }));
  } else {
    groupsMapped = groupes;
  }

  const defaultChecked = groupsMapped.map((g) => g.key); // ["GROUP-1", "GROUP-2", ...]
  const [checkedKeys, setCheckedKeys] = useState(defaultChecked);

  // QUERY_FOLDERS_TREE_DATA // all Folders with all groups
  const {
    loading,
    error,
    data: dataFolders,
    refetch: refetchFolders
  } = useQuery(QUERY_FOLDERS_TREE_DATA, {
    fetchPolicy: 'cache-and-network',
    variables: {
      type: FOLDER_TYPES.GROUP,
      folderType: FOLDER_TYPES.GROUP,
      folderCheckable: enableFolderChecking,
      showNumberOfUsers
    }
  });
  const foldersTreeData = dataFolders?.foldersTreeData;

  const tagRender = (props) => {
    const { value, closable, onClose, key, label } = props;
    return (
      <Tag
        color={'blue'}
        value={value}
        key={key}
        closable={closable}
        onClose={onClose}
        style={{ marginRight: 3 }}
      >
        {label}
      </Tag>
    );
  };

  const handleChange = async (shouldAdd, groupId) => {
    try {
      if (onChange) {
        onChange(shouldAdd, parseInt(groupId));
        return;
      }
      const action = shouldAdd ? addGroupToEntity : removeGroupFromEntity;
      await action({
        variables: { [entityParameterName]: entityId, [groupParameterName]: groupId }
      });
      const messageText = `Groupe ${shouldAdd ? 'ajouté à' : 'supprimé de'} ${entityName}`;
      message.success(messageText);
    } catch (e) {
      message.error(
        `Une erreur s'est produite lors de la modification des groupes de ${entityName}`
      );
      console.error(e);
    }
  };

  /**
   * Find folder in tree
   * @param treeData
   * @param folderKey
   * @returns {*|null}
   */
  function findFolderInTree(treeData, folderKey) {
    for (const node of treeData) {
      if (node.key === folderKey) {
        return node;
      }
      if (node.children && node.children.length > 0) {
        const found = findFolderInTree(node.children, folderKey);
        if (found) return found;
      }
    }
    return null;
  }

  /**
   * Handle folder selection : add or remove all groups in folder recursively
   * @param folder
   * @param shouldAdd
   */
  function handleFolderSelection(folder, shouldAdd) {
    const allChildsIds = [];
    if (folder?.children && folder?.children?.length > 0) {
      folder?.children?.forEach((child) => {
        if (child.type === 'GROUP') {
          allChildsIds.push(child.id);
          if (!child.disabled) {
            handleChange(shouldAdd, child.id);
          }
        } else if (child.type === 'folder') {
          const idsToAdd = handleFolderSelection(child, shouldAdd);
          allChildsIds.push(...idsToAdd);
        }
      });
    }
    return allChildsIds;
  }

  const onChangeTreeSelect = async (newValue, label, extra) => {
    const groupKey = extra.triggerValue; // 'folder-123' or 'GROUP-123'
    const groupId = groupKey?.split('-')[1];
    const shouldAdd = extra.checked;

    if (!enableFolderChecking && groupKey?.startsWith('folder')) {
      message.error(
        'Impossible de cocher/décocher un dossier entier ici. Veuillez cocher/décocher les groupes individuellement.'
      );
      return;
    }
    if (groupKey?.startsWith('folder')) {
      // Find groups in folder
      const folder = findFolderInTree(foldersTreeData, groupKey);
      // Multiple group
      if (folder) {
        const allIds = [...new Set(handleFolderSelection(folder, shouldAdd))];
        if (onChangeArray) {
          onChangeArray(allIds, shouldAdd);
        }
      }
    } else {
      // Single group
      handleChange(shouldAdd, groupId);
    }
  };

  /*
  const onCheckTree = (checkedKeysValue, { node, checked }) => {
    setCheckedKeys(checkedKeysValue);
    handleChange(checked, node.key.split('-')[1]);
  };
  */
  const onCheckTree = (checkedKeysValue, { node, checked }) => {
    const isFolder = node.type === 'folder' || node.key.startsWith('folder');

    if (isFolder && enableFolderChecking) {
      const folder = findFolderInTree(foldersTreeData, node.key);
      if (folder) {
        const allGroupIds = [...new Set(handleFolderSelection(folder, checked))];
        if (onChangeArray) {
          onChangeArray(allGroupIds, checked);
        }
        const allGroupKeys = allGroupIds.map((id) => `GROUP-${id}`);
        const folderKey = node.key;

        setCheckedKeys((prevKeys) => {
          const newSet = new Set(prevKeys);

          if (checked) {
            allGroupKeys.forEach((k) => newSet.add(k));
            newSet.add(folderKey); // ← cocher le dossier
          } else {
            allGroupKeys.forEach((k) => newSet.delete(k));
            newSet.delete(folderKey); // ← décocher le dossier aussi
          }

          return Array.from(newSet);
        });
      }
    } else {
      setCheckedKeys(checkedKeysValue);
      const groupId = node.key.split('-')[1];
      handleChange(checked, groupId);
    }
  };

  if (loading && !foldersTreeData) return <SpinnerCentered />;
  return (
    <>
      {error && <ErrorResult error={error} />}
      {foldersTreeData &&
        groupes &&
        (useTree ? (
          <Tree
            checkable={treeCheckable}
            dropdownStyle={{ zIndex: isForeground ? 999999 : 2 }}
            selectable={false}
            treeData={foldersTreeData}
            checkedKeys={checkedKeys}
            onCheck={onCheckTree}
            style={style || { width: '100%', zIndex: isForeground ? 999999 : 2 }}
            disabled={disabled}
          />
        ) : (
          <TreeSelect
            tagRender={tagRender}
            treeCheckable={treeCheckable}
            //dropdownStyle={{ zIndex: isForeground ? 999999 : 2 }}
            placeholder={placeholder}
            treeNodeFilterProp="title"
            treeData={foldersTreeData}
            treeLine
            defaultValue={groupsMapped}
            showCheckedStrategy={TreeSelect.SHOW_PARENT}
            style={style || { width: '100%' }}
            onChange={onChangeTreeSelect}
            disabled={disabled}
          />
        ))}
    </>
  );
};

export default AbstractGroupsManager;
