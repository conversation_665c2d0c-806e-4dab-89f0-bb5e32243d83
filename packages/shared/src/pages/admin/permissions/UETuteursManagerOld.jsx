import {QUERY_HIERARCHY_TREE, QUERY_USER_UES_ACCESSIBLE} from '@/shared/graphql/cours.js';
import { renderUENameAndDescriptionWithFallback } from '@/shared/services/translate.js';
import {gql, useMutation, useQuery} from '@apollo/client';
import {
  ADD_TUTEUR_TO_UE,
  REMOVE_TUTEUR_FROM_UE,
} from '@/shared/graphql/user';
import { Button, message, Select, Tag } from 'antd';
import { useTranslation } from 'react-i18next';
import {HierarchySelecter, validesTypes} from "@/shared/components/HierarchySelecter";
import React, { useEffect, useState } from 'react';

/* For one user, select in which U<PERSON> he is responsible */
export const UETuteursManager = ({ userId, ueIds, refetch }) => {
  const { t } = useTranslation();

  // Query UEs accessible for user
  const dataUE = useQuery(QUERY_USER_UES_ACCESSIBLE, {
    fetchPolicy: 'cache-and-network',
    variables: { userId },
    skip: !userId,
  });
  const uesOld = dataUE?.data?.UEsAccessibleFor;

  const [addTuteurToUE, addTuteurData] = useMutation(ADD_TUTEUR_TO_UE);
  const [removeTuteurFromUE, removeTuteurData] = useMutation(REMOVE_TUTEUR_FROM_UE);

  const ueTagRender = ({ label, value, closable, onClose, key }) => (
    <Tag value={value} key={key} color="purple" closable={closable} onClose={onClose} style={{ marginRight: 3 }}>
      {label}
    </Tag>
  );

  const handleSelect = async (_, option) => {
    try {
      console.log("option.key :",option.key)
      await addTuteurToUE({ variables: { ueId: option.key, userId } });
    } catch (e) {
      message.error(`ERREUR UE ${option.value} n'a pas été ajouté `);
      console.error(e);
    }
  };
  const handleDeselect = async (_, option) => {
    try {
      console.log("option.key :",option.key)
      await removeTuteurFromUE({ variables: { ueId: option.key, userId } });
    } catch (e) {
      message.error(`ERREUR UE ${option.value} n'a pas été enlevée`);
      console.error(e);
    }
  };

  const QUERY_UES_FOR_PLUG_ON_HIERARCHY_SELECTER=gql(`
    query Ues($ids: [ID!]) {
      ues(ids: $ids) {
        id
        isFolder
      }
    }
  `)

  const [initStructure,setInitStructure]=useState(null)

  const {data: {ues = null} = {}} = useQuery(QUERY_UES_FOR_PLUG_ON_HIERARCHY_SELECTER, {
    fetchPolicy: 'cache-and-network',
    variables: {ids:ueIds?.map(ueToPlug => ueToPlug.replace("ue-",""))},
    onCompleted:(data)=>{
      const placeholder={
        [validesTypes.CTYPE_FOLDER]:[],
        [validesTypes.CTYPE_UE]:[]
      }
      console.log("data :",data)
      data?.ues?.map((node)=>{
        if (node.isFolder===true){placeholder[validesTypes.CTYPE_FOLDER].push(node.id)}
        else if (node.isFolder===false){placeholder[validesTypes.CTYPE_UE].push(node.id)}
      })

      console.log("placeholder :",placeholder)
      setInitStructure(placeholder)
    }
  });

  const [outputStructure,setOutputStructure]=useState({})

  //const constanteUeIds=ueIds
  //const initUeArray=constanteUeIds?.map(value=> value.replace("ue-",""))
  //const [selectedUeIds,setSelectedUeIds]=useState(initUeArray)

  return (
    <span>
      <div>initStructure : {JSON.stringify(initStructure)}</div>
      <div>outputStructure : {JSON.stringify(outputStructure)}</div>
      <HierarchySelecter
        setterHookSelection={(value) => setOutputStructure(value)}
        initialisationVariable={initStructure && {...initStructure}}

        //simplificationFeature={validesTypes.CTYPE_UE}
        rankToRemoveIfLeaf={[validesTypes.CTYPE_FOLDER, validesTypes.CTYPE_COURS, validesTypes.CTYPE_PAGE, validesTypes.CTYPE_UNKNOWN, validesTypes.CTYPE_CATEGORY]}
        //disabledTypes={[validesTypes.CTYPE_COURS, validesTypes.CTYPE_PAGE, validesTypes.CTYPE_UNKNOWN, validesTypes.CTYPE_CATEGORY]}
        isTreeSelectCheckable
        useTreeSelect
        additionalTreeProps={{
          style:{width:"500px"}
        }}
      />
      <Select
        showArrow
        mode="multiple"
        style={{ minWidth: '160px', maxWidth: '450px', width: '100%' }}
        tagRender={ueTagRender}
        placeholder={t('Choose')}
        defaultValue={ueIds}
        loading={dataUE.loading || addTuteurData.loading || removeTuteurData.loading}
        options={!dataUE.error && uesOld?.map(ue => (
          {
            label: renderUENameAndDescriptionWithFallback(ue),
            value: ue.id,
            key: ue.id,
          }
        ))}
        onDeselect={handleDeselect}
        onSelect={handleSelect}
      />
      <br/>
      <Button
        type="link"
        size="small"
        onClick={async () => {
          for(const id of ueIds) {
            await removeTuteurFromUE({
              variables: {
                ueId: id,
                userId,
              },
            });
          }
          window.location.reload();
        }
      }
      >
        Tout enlever
      </Button>
    </span>
  );
};