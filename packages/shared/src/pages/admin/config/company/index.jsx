import { FileImage } from '@/shared/components/FileImage.jsx';
import { useMutation, useQuery } from '@apollo/client';
import {
  GET_CONFIG,
  MUTATION_DELETE_CONFIG_BY_ID,
  MUTATION_UPDATE_CONFIG,
  MUTATION_UPLOAD_FILE
} from '@/shared/graphql/home';
import { CONFIG_KEYS, DEFAULT_CONFIG_VARIABLES } from '@/shared/services/config';
import { useTranslation } from 'react-i18next';
import { Button, Form, message, Popconfirm, Space, Table, Tag } from 'antd';
import React, { useState } from 'react';
import FullMediParticlesBreadCrumb from '@/shared/components/FullMediParticlesBreadCrumb';
import ExoteachLayout from '@/shared/components/ExoteachLayout';
import { showGqlErrorsInMessagePopupFromException, tryParseJSONObject } from '@/shared/utils/utils';
import { DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';
import { ModalType } from '@/shared/pages/admin/forfaits/components/CreateEditForfaitModal';
import { ErrorResult } from '@/shared/components/ErrorResult';
import { CreateEditCompanyModal } from '@/shared/pages/admin/config/company/CreateEditCompanyModal';

export const CompanyAction = ({ refetch, record, loading }) => {
  const { t } = useTranslation();
  const [editVisible, setEditVisible] = useState(false);
  const [MutationDeleteConfig] = useMutation(MUTATION_DELETE_CONFIG_BY_ID);

  const company = tryParseJSONObject(record.value);

  const handleDelete = async (id) => {
    try {
      await MutationDeleteConfig({ variables: { id } });
      message.success(t('DeletedWithSuccess'));
      refetch();
    } catch (e) {
      showGqlErrorsInMessagePopupFromException(e);
    }
  };

  return (
    <span>
      <Button
        onClick={() => {
          setEditVisible(true);
        }}
        style={{ marginRight: 16 }}
        type="primary"
        shape="circle"
        icon={<EditOutlined />}
      />
      <CreateEditCompanyModal
        company={company}
        isVisible={editVisible}
        modalType={ModalType.UPDATE}
        closeModalHandler={() => {
          setEditVisible(false);
          refetch(); // Load new modifications
        }}
        companyDomain={record?.domain}
        id={record?.id}
      />

      <Popconfirm title={t('SureOfDeletion')} onConfirm={() => handleDelete(record.id)}>
        <Button shape="circle" danger style={{ marginRight: 16 }} icon={<DeleteOutlined />} />
      </Popconfirm>
    </span>
  );
};

export default function (props) {
  const { loading, error, data, refetch } = useQuery(GET_CONFIG, {
    fetchPolicy: 'cache-and-network',
    ...DEFAULT_CONFIG_VARIABLES
  });
  const { t } = useTranslation();
  // CONFIG KEYS TO BE SHOWN in Admin
  const toShow = [CONFIG_KEYS.COMPANY_INFORMATION];
  const [Mutation] = useMutation(MUTATION_UPDATE_CONFIG);
  const [uploadFile] = useMutation(MUTATION_UPLOAD_FILE);
  const [form] = Form.useForm();
  const [createVisible, setCreateVisible] = useState(false);

  const companiesConfig = data?.config?.filter((c) => toShow.includes(c.key));

  const closeModalHandler = () => {
    refetch(); // Load new modifications
    setCreateVisible(false);
  };
  const openModalHandler = () => {
    setCreateVisible(true);
  };
  const columns = [
    {
      title: t('Id'),
      key: 'Id',
      dataIndex: 'id',
      render: (id) => {
        return <span style={{ width: '100px' }}>{id}</span>;
      }
    },
    {
      title: t('Name'),
      // dataIndex: 'name',
      key: 'name',
      render: (_, record) => {
        const value = tryParseJSONObject(record?.value);
        const companyName = value?.commercialName;
        return (
          <>
            <Space>
              {value?.file && (
                <span>
                  <FileImage image={value?.file} fileType="public" />{' '}
                </span>
              )}
              <span>{companyName}</span>
            </Space>
          </>
        );
      }
    },
    {
      title: t('Domain'),
      dataIndex: 'domain',
      key: 'domain',
      render: (domain, record) => {
        return <>{domain || <Tag>Default</Tag>}</>;
      }
    },
    {
      title: 'Action',
      key: 'action',
      render: (text, record) => <CompanyAction record={record} refetch={refetch} key={record.id} />
    }
  ];

  return (
    <>
      <FullMediParticlesBreadCrumb title={t('general.Company')} />
      <ExoteachLayout>
        <br />
        <Button type="primary" icon={<PlusOutlined />} onClick={openModalHandler}>
          {t('general.add')}
        </Button>
        <br />

        <CreateEditCompanyModal
          closeModalHandler={closeModalHandler}
          isVisible={createVisible}
          modalType={ModalType.CREATE}
          refetch={refetch}
          loading={loading}
        />

        {!error && (
          <Table
            loading={loading}
            columns={columns}
            dataSource={companiesConfig}
            scroll={{ x: true }}
            pagination={false}
          />
        )}
        {error && !loading && <ErrorResult refetch={refetch} error={error} />}
      </ExoteachLayout>
    </>
  );
}
