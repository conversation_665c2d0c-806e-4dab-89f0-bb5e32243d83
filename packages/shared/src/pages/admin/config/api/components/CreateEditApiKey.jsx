import { CREATE_API_KEY, UPDATE_API_KEY } from '@/shared/graphql/apiKeys';
import PermissionsSettings from '@/shared/pages/admin/config/api/components/PermissionsSettings';
import { CloseOutlined } from '@ant-design/icons';
import { useMutation } from '@apollo/client';
import { Button, Drawer, Form, Input } from 'antd';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

export default function CreateEditApiKey({ isVisible, onCancelModal, modalType, apiKey }) {
  const { t } = useTranslation();

  // Mutation to update/create
  const [mutation, { loading, error }] = useMutation(
    modalType === 'CREATE' ? CREATE_API_KEY : UPDATE_API_KEY
  );

  const DEFAULT_PERMISSION = {
    users: 'none',
    groups: 'none'
  };
  const [permissions, setPermissions] = useState(DEFAULT_PERMISSION);

  const onFinish = async (values) => {
    try {
      const input = {
        ...values,
        permissions
      };
      if (modalType === 'UPDATE') {
        await mutation({
          variables: {
            id: apiKey?.id,
            input
          }
        });
      } else {
        await mutation({
          variables: {
            input
          }
        });
      }

      onCancelModal();
    } catch (e) {
      console.error(e);
    }
  };

  useEffect(() => {
    setPermissions(apiKey?.permissions || []);
  }, [apiKey]);

  return (
    <Drawer
      placement={'bottom'}
      onClose={onCancelModal}
      closeIcon={null}
      extra={<CloseOutlined onClick={onCancelModal} />}
      title={modalType === 'CREATE' ? t('ApiKeys.Create') : t('ApiKeys.Update')}
      open={isVisible}
      footer={null}
      destroyOnClose
      height={'calc(100% - 64px)'}
    >
      <Form layout="vertical" initialValues={apiKey} onFinish={onFinish}>
        <Form.Item name="name" label={t('ApiKeys.name')} rules={[{ required: true }]}>
          <Input />
        </Form.Item>
        <Form.Item name="description" label={t('ApiKeys.description')}>
          <Input.TextArea rows={2} />
        </Form.Item>
        <h3>Permissions</h3>

        <PermissionsSettings setPermissions={setPermissions} permissions={permissions} />
        <Form.Item>
          <Button htmlType="submit" type="primary" loading={loading}>
            {apiKey ? t('Update') : t('Create')}
          </Button>
        </Form.Item>
      </Form>
    </Drawer>
  );
}
