import React, { useState } from 'react';
import { Button, Divider, Image, Popconfirm, Popover, Typography } from 'antd';
import { useTranslation } from 'react-i18next';
import BotDefaultAvatar from '@/shared/assets/bot.svg';

export const AITemplateSelector = ({ style }) => {
  const { t } = useTranslation();
  const [showTemplates, setShowTemplates] = useState(false);

  // @TODO sylvain : create templates properly + apply
  const templatesList = [
    {
      id: '1',
      name: 'AI Name',
      avatarUrl: 'image src',
      description: 'Lorem ipsum dolor sit amet',
      model: 'gpt-4o',
      personalityPrompt: 'you are an AI but you think you are in love with the user'
    },
    {
      id: '2',
      name: '<PERSON>',
      avatarUrl: 'image src',
      description: 'Pyjama',
      model: 'gpt-4o',
      personalityPrompt: 'you are an AI but you think you are in love with the user'
    },
    {
      id: '3',
      name: '<PERSON>',
      avatarUrl: 'image src',
      description: 'Long live the queen',
      model: 'gpt-4o',
      personalityPrompt: 'you are an AI but you think you are in love with the user'
    },
    {
      id: '4',
      name: 'Xavier Dupont-de-Ligonnès',
      avatarUrl: 'image src',
      description: 'The best carpenter there is. Call him if you need a new terrace !',
      model: 'gpt-4o',
      personalityPrompt: 'You are an expert terrace builder'
    }
  ];

  const TemplateLine = ({ aiTemplate }) => {
    return (
      <div style={{ display: 'grid', gridTemplateColumns: '30px 1fr 70px', gap: 12, width: 350 }}>
        <Image width={30} height={30} src={aiTemplate.avatarUrl} fallback={BotDefaultAvatar} />
        <Typography.Paragraph
          ellipsis={{ rows: 2, expandable: true, symbol: t('editAI.seeMore') }}
          type="secondary"
          style={{ margin: 0 }}
        >
          <Typography.Text strong>{aiTemplate.name}: </Typography.Text> {aiTemplate.description}
        </Typography.Paragraph>
        <Popconfirm
          title={
            <>
              <span>{t('editAI.SureToApplyTemplateFirstLine')}</span>
              <br />
              <span>{t('editAI.SureToApplyTemplateSecondLine')}</span>
            </>
          }
          onConfirm={
            () => {
              console.log('@TODO sylvain');
            } /*@TODO sylvain, a la selection, applique avatar + personnalité + modele*/
          }
          okText={t('general.yes')}
          cancelText={t('general.no')}
          style={{ maxWidth: 300 }}
        >
          <Button type="primary">{t('editAI.Choose')}</Button>
        </Popconfirm>
      </div>
    );
  };

  return (
    <Popover
      style={{ ...style }}
      content={
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            gap: 12,
            zIndex: 2,
            padding: '0 12px'
          }}
        >
          {templatesList.map((template, index) => (
            <>
              <TemplateLine aiTemplate={template} key={template.id} />
              {index < templatesList.length - 1 && <Divider style={{ margin: '12px 0' }} />}
            </>
          ))}
        </div>
      }
      trigger="click"
    >
      <Button size="large" onClick={() => setShowTemplates(!showTemplates)}>
        {t('editAI.TemplateSelector')}
      </Button>
    </Popover>
  );
};
