import { Radio } from 'antd';
import React from 'react';

export const TriStateRadioGroup = ({ varInit, onChange }) => {
  const handleChange = (e) => {
    onChange(e.target.value);
  };

  return (
    <Radio.Group defaultValue={varInit} onChange={handleChange}>
      <Radio.Button value>Oui</Radio.Button>
      <Radio.Button value={false}>Non</Radio.Button>
      <Radio.Button value={null}>Peu importe</Radio.Button>
    </Radio.Group>
  );
};
