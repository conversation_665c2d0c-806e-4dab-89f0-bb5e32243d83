import PostTypeTag from '@/shared/components/Commentaires/PostTypeTag';
import RenderQuillHtml from '@/shared/components/ExoQuill/RenderQuillHtml';
import { FileImage } from '@/shared/components/FileImage';
import ExoAvatar from '@/shared/components/User/ExoAvatar';
import { UserProfileCard } from '@/shared/components/User/UserProfileCard/UserProfileCard';
import { QUERY_ADMIN_POST_SEARCH, UPDATE_POST } from '@/shared/graphql/posts';
import { APPROVE_POST, REJECT_POST, BULK_PUBLISH_POSTS, BULK_UNPUBLISH_POSTS, BULK_DELETE_POSTS } from '@/shared/graphql/moderation';
import useLocalStorage from '@/shared/hooks/useLocalStorage';
import { CustomInfoTag } from '@/shared/pages/admin/forum/components/CustomInfoTag';
import { TagCollection } from '@/shared/pages/admin/forum/components/TagCollection';
import { ThreadPost } from '@/shared/pages/forum/$post';
import { getCommentaireTypeFromObject, getTypeIdFromObject } from '@/shared/services/commentaires';
import {
  isMobile,
  showGqlErrorsInMessagePopupFromException,
  stripHtml
} from '@/shared/utils/utils';
import { CheckCircleTwoTone, CloseCircleTwoTone, EyeOutlined, CheckOutlined, CloseOutlined, DeleteOutlined } from '@ant-design/icons';
import { useMutation, useQuery } from '@apollo/client';
import { Button, Card, Drawer, Empty, message, Popover, Spin, Statistic, Table, Checkbox, Tag, Space, Dropdown, Menu } from 'antd';
import dayjs from 'dayjs';
import React, { useMemo, useState } from 'react';
import CountUp from 'react-countup';
import { useTranslation } from 'react-i18next';

export const SearchResult = ({ filter }) => {
  const ComponantChangePostStatus = ({ postId, initResolved }) => {
    /* Component qui lie le resolve / unresolve Button afin de pouvoir faire qqc de plus joli et organique */

    const [updatePostMutation] = useMutation(UPDATE_POST);
    const [stateAction, setStateAction] = useState(null);

    // La fonction de changement de status
    const changeSubjectStatus = async (isResolved, postId) => {
      try {
        await updatePostMutation({
          variables: { id: postId, post: { isResolved } }
        });
        message.success(`${t('TopicMarkedAs')} ${isResolved ? t('Resolved') : t('NonResolved')}`);
        //await refetch();
      } catch (e) {
        showGqlErrorsInMessagePopupFromException(e);
        console.error(e);
      }
    };

    // Le boutton qui active status => true
    const ResolveButton = ({ postId }) => {
      const resolveColor = '#52c41a';

      return (
        <Popover content={t('FilterQuestions.ResolvePost')}>
          <Button
            style={{ backgroundColor: stateAction === true && resolveColor }}
            onClick={() => {
              changeSubjectStatus(true, postId);
              setStateAction(true);
            }}
          >
            <CheckCircleTwoTone twoToneColor={resolveColor} />
          </Button>
        </Popover>
      );
    };

    // Le boutton qui active status => false
    const UnResolveButton = ({ postId }) => {
      const unResolveColor = '#cf1f1f';

      return (
        <Popover content={t('FilterQuestions.UnResolvePost')}>
          <Button
            style={{ backgroundColor: stateAction === false && unResolveColor }}
            onClick={() => {
              changeSubjectStatus(false, postId);
              setStateAction(false);
            }}
          >
            <CloseCircleTwoTone twoToneColor={unResolveColor} />
          </Button>
        </Popover>
      );
    };

    return (
      <div style={{ display: 'flex' }}>
        <ResolveButton postId={postId} />
        <UnResolveButton postId={postId} />
      </div>
    );
  };

  // ===== MODERATION FUNCTIONS =====

  const handleSelectPost = (postId, checked) => {
    if (checked) {
      setSelectedPosts([...selectedPosts, postId]);
    } else {
      setSelectedPosts(selectedPosts.filter(id => id !== postId));
      setSelectAll(false);
    }
  };

  const handleSelectAll = (checked) => {
    setSelectAll(checked);
    if (checked) {
      const allPostIds = adminSearchPosts?.postResults?.map(post => post.id) || [];
      setSelectedPosts(allPostIds);
    } else {
      setSelectedPosts([]);
    }
  };

  const handleApprovePost = async (postId) => {
    try {
      await approvePostMutation({
        variables: { postId, reason: 'Approuvé par modérateur' }
      });
      message.success('Post approuvé avec succès');
      refetch();
    } catch (error) {
      showGqlErrorsInMessagePopupFromException(error);
    }
  };

  const handleRejectPost = async (postId) => {
    try {
      await rejectPostMutation({
        variables: { postId, reason: 'Rejeté par modérateur' }
      });
      message.success('Post rejeté avec succès');
      refetch();
    } catch (error) {
      showGqlErrorsInMessagePopupFromException(error);
    }
  };

  const handleBulkApprove = async () => {
    if (selectedPosts.length === 0) {
      message.warning('Veuillez sélectionner au moins un post');
      return;
    }

    try {
      const result = await bulkPublishMutation({
        variables: { postIds: selectedPosts, reason: 'Approbation en masse' }
      });

      if (result.data.bulkPublishPosts.success) {
        message.success(`${result.data.bulkPublishPosts.successCount} posts approuvés avec succès`);
      } else {
        message.warning(`${result.data.bulkPublishPosts.successCount} posts approuvés, ${result.data.bulkPublishPosts.errorCount} erreurs`);
      }

      setSelectedPosts([]);
      setSelectAll(false);
      refetch();
    } catch (error) {
      showGqlErrorsInMessagePopupFromException(error);
    }
  };

  const handleBulkReject = async () => {
    if (selectedPosts.length === 0) {
      message.warning('Veuillez sélectionner au moins un post');
      return;
    }

    try {
      const result = await bulkUnpublishMutation({
        variables: { postIds: selectedPosts, reason: 'Rejet en masse' }
      });

      if (result.data.bulkUnpublishPosts.success) {
        message.success(`${result.data.bulkUnpublishPosts.successCount} posts rejetés avec succès`);
      } else {
        message.warning(`${result.data.bulkUnpublishPosts.successCount} posts rejetés, ${result.data.bulkUnpublishPosts.errorCount} erreurs`);
      }

      setSelectedPosts([]);
      setSelectAll(false);
      refetch();
    } catch (error) {
      showGqlErrorsInMessagePopupFromException(error);
    }
  };

  const handleBulkDelete = async () => {
    if (selectedPosts.length === 0) {
      message.warning('Veuillez sélectionner au moins un post');
      return;
    }

    try {
      const result = await bulkDeleteMutation({
        variables: { postIds: selectedPosts, reason: 'Suppression en masse' }
      });

      if (result.data.bulkDeletePosts.success) {
        message.success(`${result.data.bulkDeletePosts.deletedPostsCount} posts et ${result.data.bulkDeletePosts.deletedCommentsCount} commentaires supprimés`);
      } else {
        message.warning(`Suppression partielle: ${result.data.bulkDeletePosts.errorCount} erreurs`);
      }

      setSelectedPosts([]);
      setSelectAll(false);
      refetch();
    } catch (error) {
      showGqlErrorsInMessagePopupFromException(error);
    }
  };

  const handleDeleteSinglePost = async (postId) => {
    try {
      const result = await bulkDeleteMutation({
        variables: { postIds: [postId], reason: 'Suppression individuelle' }
      });

      if (result.data.bulkDeletePosts.success) {
        message.success('Post supprimé avec succès');
      } else {
        message.error('Erreur lors de la suppression');
      }

      refetch();
    } catch (error) {
      showGqlErrorsInMessagePopupFromException(error);
    }
  };

  // Gestion de la pagnination
  const [offset, setOffset] = useState(0);
  const [limit, setLimit] = useState(20);
  const [currentPage, setCurrentPage] = useLocalStorage('admin-question-reponse-current-page', 1);

  const [selectedPost, setSelectedPost] = React.useState(null);

  // ===== MODERATION STATE =====
  const [selectedPosts, setSelectedPosts] = useState([]);
  const [selectAll, setSelectAll] = useState(false);

  // ===== MODERATION MUTATIONS =====
  const [approvePostMutation] = useMutation(APPROVE_POST);
  const [rejectPostMutation] = useMutation(REJECT_POST);
  const [bulkPublishMutation] = useMutation(BULK_PUBLISH_POSTS);
  const [bulkUnpublishMutation] = useMutation(BULK_UNPUBLISH_POSTS);
  const [bulkDeleteMutation] = useMutation(BULK_DELETE_POSTS);

  // Important pour reset l'offset à 0
  const subFilter = useMemo(() => {
    const temp = {
      generalCoursIds: filter.coursIds,
      category: filter.category,
      typeIds: filter.typeIds,
      startDateCreationFilter: filter.startDateCreationFilter,
      endDateCreationFilter: filter.endDateCreationFilter,
      userIds: filter.userIds,
      participantsUserIds: filter.participantsUserIds,
      isResolved: filter.isResolved,
      isQuestion: true, // On veut uniquement des questions
      titreFilter: filter.titreFilter,
      contentFilter: filter.contentFilter,
      iaAnswered: filter.iaAnswered,
      isResolvedByAi: filter.isResolvedByAi,
      isAskingForHumanHelp: filter.isAskingForHumanHelp,
      categoryExerciceAdvancedFilter: filter.categoryExerciceAdvancedFilter,
      categoryEventAdvancedFilter: filter.categoryEventAdvancedFilter,
      // ===== MODERATION FILTERS =====
      moderationStatus: filter.moderationStatus,
      contentType: filter.contentType
    };
    setOffset(0);
    setCurrentPage(1);
    return temp;
  }, [filter]);

  const { t } = useTranslation();
  const { data: { adminSearchPosts = null } = {}, loading, refetch } = useQuery(QUERY_ADMIN_POST_SEARCH, {
    fetchPolicy: 'no-cache',
    variables: {
      filter: {
        ...subFilter,
        offset,
        limit
      }
    }
  });

  const totalResults = adminSearchPosts?.count || 0;

  // Composant pour afficher le texte HTML avec un bouton pour expand/reduce
  const ExpandableHtmlText = ({ htmlContent, previewLength = 100 }) => {
    const [isExpanded, setIsExpanded] = useState(false);

    const toggleExpand = () => setIsExpanded(!isExpanded);

    // Fonction pour obtenir un extrait du contenu HTML
    const getPreview = (content) => {
      // Remarque : Cette méthode est très basique et pourrait ne pas fonctionner correctement si votre HTML contient des éléments complexes.
      // Elle est fournie à titre d'exemple simple.
      const strippedContent = content.replace(/<[^>]+>/g, ''); // Supprimer les balises HTML
      return strippedContent.length > previewLength
        ? unescape(strippedContent).slice(0, previewLength) + '...'
        : content;
    };

    return (
      <div>
        {isExpanded ? (
          <RenderQuillHtml>{htmlContent || ''}</RenderQuillHtml>
        ) : (
          <RenderQuillHtml>{getPreview(htmlContent)}</RenderQuillHtml>
        )}
        <Button type="link" onClick={toggleExpand}>
          {isExpanded ? 'Réduire' : 'Lire la suite'}
        </Button>
      </div>
    );
  };

  const formatDate = (date) => {
    const now = dayjs();
    const inputDate = dayjs(date);
    if (now.diff(inputDate, 'hour') < 24 && now.isSame(inputDate, 'day')) {
      return inputDate.format('HH[:]mm');
    } else if (now.subtract(1, 'day').isSame(inputDate, 'day')) {
      return t('general.Yesterday');
    } else {
      return inputDate.format('D MMM');
    }
  };

  const columns = [
    // ===== MODERATION COLUMNS =====
    {
      title: (
        <Checkbox
          checked={selectAll}
          onChange={(e) => handleSelectAll(e.target.checked)}
        />
      ),
      dataIndex: 'selection',
      key: 'selection',
      width: 50,
      render: (_, record) => (
        <Checkbox
          checked={selectedPosts.includes(record.id)}
          onChange={(e) => handleSelectPost(record.id, e.target.checked)}
        />
      )
    },
    {
      title: 'Type',
      dataIndex: 'contentType',
      key: 'contentType',
      width: 100,
      render: (_, record) => (
        <Tag color={record.parentId ? 'blue' : 'green'}>
          {record.parentId ? 'Commentaire' : 'Post'}
        </Tag>
      )
    },
    {
      title: 'Statut',
      dataIndex: 'moderation_status',
      key: 'moderation_status',
      width: 120,
      render: (_, record) => {
        const status = record.moderation_status || 'approved';
        const colors = {
          pending: 'orange',
          approved: 'green',
          rejected: 'red'
        };
        const labels = {
          pending: 'En attente',
          approved: 'Publié',
          rejected: 'Rejeté'
        };
        return (
          <Tag color={colors[status]}>
            {labels[status]}
          </Tag>
        );
      }
    },
    {
      title: 'Actions',
      dataIndex: 'actions',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space size="small">
          <Button
            size="small"
            icon={<EyeOutlined />}
            onClick={() => setSelectedPost(record)}
            title="Voir"
          />
          <Button
            size="small"
            icon={<CheckOutlined />}
            type="primary"
            onClick={() => handleApprovePost(record.id)}
            title="Approuver"
          />
          <Button
            size="small"
            icon={<CloseOutlined />}
            danger
            onClick={() => handleRejectPost(record.id)}
            title="Rejeter"
          />
          <Button
            size="small"
            icon={<DeleteOutlined />}
            danger
            onClick={() => handleDeleteSinglePost(record.id)}
            title="Supprimer"
          />
        </Space>
      )
    },
    /*
    {
      title: t('general.User'),
      dataIndex: 'user',
      key: 'user',
      render: (user) => {
        if (!user) {
          return <span>no User</span>;
        }
        return (
          <>
            <UserProfileCard userId={user?.id} username={user?.username}>
              <ExoAvatar avatar={user.avatar} size="small" isActive={user.isActive} />
            </UserProfileCard>
            &nbsp;{user.username}
          </>
        );
      }
    },
    */
    {
      title: t('general.Question'),
      dataIndex: 'title2',
      key: 'title2',
      render: (_, record) => {
        const { isResolved, isResolvedByAi, isAskingForHumanHelp, answeredByAi } = record;
        return (
          <div onClick={() => setSelectedPost(record)}>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <div>
                <UserProfileCard userId={record?.user?.id} username={record?.user?.username}>
                  <ExoAvatar
                    avatar={record?.user.avatar}
                    size="small"
                    isActive={record?.user.isActive}
                  />
                </UserProfileCard>
                &nbsp;{record?.user.username}
              </div>
              <div>{dayjs(record.createdAt).format('Do MMMM YYYY')}</div>
            </div>

            <div onClick={() => setSelectedPost(record)} style={{ cursor: 'pointer' }}>
              <h4
                style={{
                  margin: '5px 0px 2px 0px'
                }}
              >
                {stripHtml(record?.title || '')}
              </h4>
              <div>{stripHtml(record.text?.slice(0, 200))}</div>
            </div>

            {/* Tags */}
            <div
              style={{ cursor: 'pointer', display: 'flex', flexWrap: 'wrap' }}
              onClick={() => setSelectedPost(record)}
            >
              <CustomInfoTag postId={record.id} type="courOrForumName" />{' '}
              <PostTypeTag postType={record.type} />
              {(isResolved === true || isResolvedByAi === true) && (
                <TagCollection string={t(`FilterQuestions.Resolved`)} type="resolved" />
              )}
              {isResolved === false && isResolvedByAi === false && (
                <TagCollection string={t(`FilterQuestions.UnResolved`)} type="resolved" />
              )}
              {/* Aide humaine demandée seulement si demandé */}
              {/*
              {isResolvedByAi !== null && (
                <TagCollection
                  string={t(
                    `FilterQuestions.${isResolvedByAi ? 'ResolvedByAi' : 'UnResolvedByAi'}`
                  )}
                  type="resolved"
                />
              )}
              */}
              {isAskingForHumanHelp && (
                <TagCollection string={t(`FilterQuestions.${'AskingHumanHelp'}`)} type="help" />
              )}
              {answeredByAi && (
                <TagCollection string={t(`FilterQuestions.${'AiAnswered'}`)} type="aiAnswered" />
              )}
            </div>
            {/*
              <Link to={buildThreadLinkFromComment(record)}>
                {' '}
                <RenderQuillHtml>{record.text?.slice(0, 200)}</RenderQuillHtml>{' '}
              </Link>
            */}
          </div>
        );
      }
    }
    /*
    {
      title: (
        <div>
          {t('FilterQuestions.LastFeedback')}
          &nbsp;
          <Popover
            content={<div>{t('FilterQuestions.LastFeedbackExplanation')}</div>}
            trigger="hover"
          >
            <QuestionCircleOutlined style={{fontSize: '16px', color: '#d4af37'}}/>
          </Popover>
        </div>
      ),
      render: (_, record) => {
        const text = record?.approuvedResponse?.lastResponseFromNonUserOrBot?.text;

        return (
          <>
            {record?.approuvedResponse &&
              record?.approuvedResponse?.lastResponseRole &&
              record?.approuvedResponse?.lastResponseFromNonUserOrBot && (
                <>
                  <>
                    <Tag
                      color={
                        record?.approuvedResponse?.lastResponseRole === 'ADMIN' ||
                        record?.approuvedResponse?.lastResponseRole === 'SUB_ADMIN'
                          ? 'geekblue'
                          : 'purple'
                      }
                      //style={{ height: 'auto' }}
                    >
                      {record?.approuvedResponse?.lastResponseRole !== 'AI'
                        ? mapRoleToShortRoleName(record?.approuvedResponse?.lastResponseRole)
                        : record?.approuvedResponse?.lastResponseRole}
                    </Tag>
                    <>
                      <UserProfileCard
                        userId={record?.approuvedResponse?.lastResponseFromNonUserOrBot?.user?.id}
                        username={
                          record?.approuvedResponse?.lastResponseFromNonUserOrBot?.user?.username
                        }
                      >
                        <ExoAvatar
                          avatar={
                            record?.approuvedResponse?.lastResponseFromNonUserOrBot?.user?.avatar
                          }
                          size="small"
                          isActive={
                            record?.approuvedResponse?.lastResponseFromNonUserOrBot?.user?.isActive
                          }
                        />
                      </UserProfileCard>
                      &nbsp;
                      {record?.approuvedResponse?.lastResponseFromNonUserOrBot?.user?.username}
                    </>
                  </>
                  <ExpandableHtmlText htmlContent={text} previewLength={200}/>
                </>
              )}
          </>
        );
      }
    },

    {
      title: t('FilterQuestions.CreationDate'),
      dataIndex: 'date',
      key: 'date',
      //sorter: (a, b) => new Date(a.createdAt) - new Date(b.createdAt),
      render: (_, record) => {
        const { createdAt } = record;
        return (
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              cursor: 'pointer',
            }}
            onClick={() => setSelectedPost(record)}
          >
            {<TagCollection string={dayjs(createdAt).format('DD/MM/YYYY kk:mm')} type="date" />}
            <span>{dayjs(createdAt).fromNow()}</span>
          </div>
        );
      },
    },
    {
      title: t('FilterQuestions.Category'),
      dataIndex: 'type',
      key: 'type',
      render: (_, record) => (
        <div style={{ cursor: 'pointer' }} onClick={() => setSelectedPost(record)}>
          <TagCollection
            string={t(`FilterQuestions.PostType${getCommentaireTypeFromObject(record)}`)}
            type="category"
          />
          <CustomInfoTag postId={record.id} type="categoryType" />
        </div>
      ),
    },
    {
      title: t('FilterQuestions.State'),
      dataIndex: 'état',
      key: 'état',
      render: (_, record) => {
        const { isResolved, isResolvedByAi, isAskingForHumanHelp, answeredByAi } = record;
      :
        return (
          <div style={{ cursor: 'pointer' }} onClick={() => setSelectedPost(record)}>
            {isResolved !== null && (
              <TagCollection
                string={t(`FilterQuestions.${isResolved ? 'Resolved' : 'UnResolved'}`)}
                type="resolved"
              />
            )}
            {isResolvedByAi !== null && (
              <TagCollection
                string={t(`FilterQuestions.${isResolvedByAi ? 'ResolvedByAi' : 'UnResolvedByAi'}`)}
                type="resolved"
              />
            )}
            {isAskingForHumanHelp !== null && (
              <TagCollection
                string={t(
                  `FilterQuestions.${isAskingForHumanHelp ? 'AskingHumanHelp' : 'NotAskingHumanHelp'}`,
                )}
                type="help"
              />
            )}
            {answeredByAi !== null && (
              <TagCollection
                string={t(`FilterQuestions.${answeredByAi ? 'AiAnswered' : 'NotAiAnswered'}`)}
                type="aiAnswered"
              />
            )}
          </div>
        );
      },
    },
    */
    /*
    {
      title: t('FilterQuestions.Actions'),
      dataIndex: 'action',
      key: 'action',
      render: (id, record) => {
        return <ComponantChangePostStatus postId={record.id} initResolved={record.isResolved} />;
      }
    }
    */
  ];

  const formatter = (value) => <CountUp end={value} separator="," />;

  return (
    <>
      <Card style={{ margin: '10px' }} bordered={false}>
        <Statistic
          title={t('FilterQuestions.ResultNumber')}
          value={loading ? <Spin /> : totalResults | 0}
          formatter={formatter}
          valueStyle={{ fontWeight: 'bold' }} // Appliquer le style en gras ici
        />
      </Card>

      {/* ===== BULK ACTIONS TOOLBAR ===== */}
      {selectedPosts.length > 0 && (
        <Card style={{ margin: '10px', backgroundColor: '#f0f2f5' }} bordered={false}>
          <Space>
            <span style={{ fontWeight: 'bold' }}>
              {selectedPosts.length} élément(s) sélectionné(s)
            </span>
            <Button
              type="primary"
              icon={<CheckOutlined />}
              onClick={handleBulkApprove}
            >
              Approuver tout
            </Button>
            <Button
              danger
              icon={<CloseOutlined />}
              onClick={handleBulkReject}
            >
              Rejeter tout
            </Button>
            <Button
              danger
              icon={<DeleteOutlined />}
              onClick={handleBulkDelete}
            >
              Supprimer tout
            </Button>
            <Button
              onClick={() => {
                setSelectedPosts([]);
                setSelectAll(false);
              }}
            >
              Annuler sélection
            </Button>
          </Space>
        </Card>
      )}

      <div style={isMobile ? {} : { display: 'grid', gridTemplateColumns: '1fr 1fr' }}>
        <div style={isMobile ? {} : { gridColumn: '1 / 2' }}>
          <Table
            loading={loading}
            columns={columns}
            dataSource={adminSearchPosts?.postResults}
            scroll={{ x: true }}
            pagination={{
              current: currentPage,
              total: totalResults,
              showTotal: false,
              onChange: (page, pageSize) => {
                setCurrentPage(page);
                setOffset((page - 1) * pageSize);
                setLimit(pageSize);
              },
              position: ['bottomLeft'],
              defaultPageSize: 20,
              pageSizeOptions: [10, 20, 50],
              responsive: true,
              size: 'default'
            }}
            size="large"
            search={false}
          />
        </div>

        {isMobile ? (
          <Drawer
            open={selectedPost}
            placement="bottom"
            onClose={() => setSelectedPost(null)}
            height={isMobile ? 'calc(100% - 109px)' : 'calc(100% - 64px)'}
            styles={{ body: { padding: '0 24px' } }}
          >
            {selectedPost && (
              <ThreadPost
                postId={selectedPost?.id}
                typeId={getTypeIdFromObject(
                  selectedPost,
                  getCommentaireTypeFromObject(selectedPost)
                )}
                type={getCommentaireTypeFromObject(selectedPost)}
                showBanner={false} // Ne pas afficher la grosse bannière
                enablePullToRefresh={false}
              />
            )}
          </Drawer>
        ) : (
          <div>
            {selectedPost ? (
              <ThreadPost
                postId={selectedPost?.id}
                typeId={getTypeIdFromObject(
                  selectedPost,
                  getCommentaireTypeFromObject(selectedPost)
                )}
                type={getCommentaireTypeFromObject(selectedPost)}
                showBanner={false} // Ne pas afficher la grosse bannière
                enablePullToRefresh={false}
              />
            ) : (
              <Empty description={t('SelectNotification')} />
            )}
          </div>
        )}
      </div>
    </>
  );
};
