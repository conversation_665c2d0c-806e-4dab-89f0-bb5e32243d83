import { QUERY_HIERARCHY_TREE } from '@/shared/graphql/cours.js';
import {
  MUTATION_MASSCHANGE_QUESTIONS_DISPLAY_SETTINGS,
  QUERY_ADMIN_ALL_SCALES,
  QUERY_ALL_QCM_TYPE
} from '@/shared/graphql/qcm.js';
import { QuestionAnswerType } from '@/shared/pages/admin/qcm/components/modal/CreateEditQuestionModal.jsx';
import { CONTENT_TYPE_VALUES } from '@/shared/pages/admin/qcm/types-qcm/index.jsx';
import { renderIcon } from '@/shared/pages/qcm/$index$.jsx';
import { showGqlErrorsInMessagePopupFromException } from '@/shared/utils/utils.js';
import { BookTwoTone } from '@ant-design/icons';
import { useMutation, useQuery } from '@apollo/client';
import {
  But<PERSON>,
  Card,
  Checkbox,
  Divider,
  Form,
  notification,
  Popover,
  Select,
  Tree
} from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import BatchUpdateScale from '@/shared/pages/admin/bareme/componants/BatchUpdateScale';
import { useTranslation } from 'react-i18next';
import InfoCircleOutlined from '@ant-design/icons/lib/icons/InfoCircleOutlined';
import { confirmModalAsFunction } from '@/shared/components/ConfirmModalAsFunction';

export default function QuestionSettingsMassOperations(props) {
  const [massChangeDisplayExercises, { loading, error, data }] = useMutation(
    MUTATION_MASSCHANGE_QUESTIONS_DISPLAY_SETTINGS
  );
  const { t } = useTranslation();
  const [selectedTypesIds, setSelectedTypesIds] = useState([]);
  const [targetTypesIds, setTargetTypesIds] = useState([]);

  const [mcqScaleId, setMcqScaleId] = useState(null);
  const [selectedCoursIds, setSelectedCoursIds] = useState([]);

  const [answerType, setAnswerType] = useState(null);

  const [contentType, setContentType] = useState(null);
  const [operation, setOperation] = useState('add');

  const {
    data: dataTypes,
    loading: loadingTypes,
    error: errorTypes
  } = useQuery(QUERY_ALL_QCM_TYPE);
  const allTypes = dataTypes?.allTypeQcm;

  const { data: dataScales } = useQuery(QUERY_ADMIN_ALL_SCALES, {
    fetchPolicy: 'no-cache'
  });
  const [changeScale, setChangeScale] = useState(false);
  const [changeDisplay, setChangeDisplay] = useState(false);

  const mcqScales = dataScales?.mcqScales;

  const typesWithCurrentContentTypeOrUndefined = allTypes?.filter(
    (t) => t?.contentType === CONTENT_TYPE_VALUES.EXERCISE
  );
  /* Cours tree related */
  const {
    loading: loadingTree,
    error: errorTree,
    data: dataTree,
    refetch: refetchTree
  } = useQuery(QUERY_HIERARCHY_TREE, {
    fetchPolicy: 'cache-and-network',
    variables: {
      selectable: false
    }
  });
  const hierarchyTree = dataTree?.hierarchicalTreeData;
  const [treeData, setTreeData] = useState([]);
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [checkedKeys, setCheckedKeys] = useState([]);
  const [autoExpandParent, setAutoExpandParent] = useState(true);
  const [selectedKeys, setSelectedKeys] = useState([]);
  const ref = useRef();
  const [formRef] = Form.useForm();

  const onExpand = (expandedKeysValue) => {
    setExpandedKeys(expandedKeysValue);
    setAutoExpandParent(false);
  };

  /* Set initial UE data */
  useEffect(() => {
    if (hierarchyTree) {
      setTreeData(hierarchyTree);
    }
  }, [hierarchyTree]);
  useEffect(() => {
    if (selectedCoursIds) {
      setCheckedKeys([...selectedCoursIds?.map((id) => `cours-${id}`)]);
      /*
        setSelectedKeys([
          ...ueIds?.map(id => `ue-${id}`),
          ...ueCategoryIds?.map(id => `category-${id}`),
        ]);
      */
    }
  }, [selectedCoursIds]);

  const onSelect = async (selectedKeysValue, event) => {
    const { node, selected } = event;
    const getTypeAndIdFromKey = (k) => {
      const splitted = k.split('-');
      return { id: splitted[1], type: splitted[0] };
    };
    const changePermissions = async ({ type, id, halfChecked = false }) => {
      let options;
      switch (type) {
        case 'cours':
          const coursId = id;
          if (selected || halfChecked) {
            setSelectedCoursIds((prev) => [...prev, coursId]);
          } else {
            setSelectedCoursIds((prev) => prev.filter((idPrev) => idPrev !== coursId));
          }
          break;
      }
    };
    await changePermissions(getTypeAndIdFromKey(node.key));
  };

  const onCheck = (checkedKeysValue, event) => {
    const { halfCheckedKeys, checked, node } = event;

    // Petite fonction utilitaire pour extraire type et id
    const getTypeAndIdFromKey = (key) => {
      const [type, id] = key.split('-');
      return { type, id };
    };

    const idsToAdd = [];
    const idsToRemove = [];

    // Fonction récursive pour parcourir le node et tous ses enfants
    const traverseTree = (currentNode) => {
      const { type, id } = getTypeAndIdFromKey(currentNode.key);

      // On applique la logique seulement si c'est un "cours"
      if (type === 'cours') {
        // Si le node est coché OU à moitié coché, on l'ajoute ;
        if (checked || halfCheckedKeys.includes(currentNode.key)) {
          idsToAdd.push(id);
        } else {
          idsToRemove.push(id);
        }
      }

      // On descend dans les enfants
      if (currentNode.children?.length) {
        currentNode.children.forEach((child) => {
          traverseTree(child);
        });
      }
    };

    traverseTree(node);

    setSelectedCoursIds((prev) => {
      const newSet = new Set(prev);

      idsToAdd.forEach((id) => {
        newSet.add(id);
      });

      idsToRemove.forEach((id) => {
        newSet.delete(id);
      });

      return Array.from(newSet);
    });
  };

  const handleFinish = async () => {
    try {
      notification.info({
        message: `Opération en cours...`,
        placement: 'topLeft',
        key: 'displayOperationProgress'
      });

      // Update des display Change si 'changeDisplay===true'
      if (changeDisplay) {
        const result = await massChangeDisplayExercises({
          variables: {
            selectedCoursIds,
            displaySetting: {
              type: answerType
            },
            changeDisplay
          }
        });

        if (result?.data) {
          const questionsTraitees = result.data.massChangesQuestionsDisplaySettings;
          notification.success({
            message: `Opération terminée`,
            description: `Affichage de ${questionsTraitees} questions traitées`,
            placement: 'topLeft',
            key: 'massChangeDisplayExercises'
          });
        }
      }

      // Update des scales => Si pas de scale à changer, retourne rien
      const result2 = await ref.current.callMutation();

      if (result2?.data) {
        const questionsTraitees = result2.data.mutationMassChangesScaleForQuestions;
        notification.success({
          message: `Opération terminée`,
          description: `Modification de ${questionsTraitees} barèmes`,
          placement: 'topLeft',
          key: 'massChangeScaleExercises'
        });
      }

      notification.info({
        message: `Opération terminée !`,
        placement: 'topLeft',
        key: 'displayOperationProgress'
      });
    } catch (e) {
      console.error(e);
      showGqlErrorsInMessagePopupFromException(e);
    }
  };

  const renderTreeNodeIcon = (node) => {
    if (node?.type === 'cours') {
      return <BookTwoTone />;
    }
    return renderIcon(node?.image);
  };

  return (
    <Card loading={loadingTypes}>
      <Form layout="vertical" onFinish={handleFinish} form={formRef}>
        {/*
        <Form.Item label="Types à sélectionner">
          <TypeQcmSelector
            onChange={(names, options) => setSelectedTypesIds(options.map(o => o.key))}
            allTypes={typesWithCurrentContentTypeOrUndefined}
          />
        </Form.Item>
        */}
        <p>
          <b>
            Cela sélectionnera toutes les questions liées aux cours sélectionnés, et changera le
            barême et / ou l'affichage.
          </b>
        </p>
        <br />

        <h3>Sélectionnez les cours liés :</h3>
        <Tree
          selectable
          checkable
          multiple
          onExpand={onExpand}
          expandedKeys={expandedKeys}
          autoExpandParent={autoExpandParent}
          onCheck={onCheck}
          checkedKeys={checkedKeys}
          onSelect={onSelect}
          selectedKeys={selectedKeys}
          treeData={treeData}
          showIcon
          icon={renderTreeNodeIcon}
        />

        <br />

        <Divider>Actions à effectuer sur la sélection</Divider>

        <h3>
          {t('Scale.MassUpdateScaleNewTitle')}
          &nbsp;
          <Popover content={t('Scale.MassUpdateScaleExplanation')} trigger="hover">
            <InfoCircleOutlined style={{ cursor: 'pointer' }} />
          </Popover>
          {/*
          <Checkbox value={changeScale} onChange={() => setChangeScale(p => !p)}>
            Modifier le barême
          </Checkbox>
          */}
        </h3>

        <BatchUpdateScale
          ref={ref}
          coursIds={selectedCoursIds}
          questionIds={[]}
          previsualizeCount={true}
        />

        <br />
        <h3>
          <Checkbox value={changeDisplay} onChange={() => setChangeDisplay((p) => !p)}>
            Modifier l'affichage
          </Checkbox>
        </h3>
        {changeDisplay && (
          <Form.Item label="Type d'affichage vrai/faux">
            <Select
              placeholder="Type d'affichage vrai/faux"
              onSelect={(v) => setAnswerType(v)}
              value={answerType}
            >
              <Select.Option key={null} value={null}>
                Affichage classique
              </Select.Option>
              <Select.Option
                key={QuestionAnswerType.TRUE_OR_FALSE_OR_UNDEFINED}
                value={QuestionAnswerType.TRUE_OR_FALSE_OR_UNDEFINED}
              >
                Affichage avec sélection Vrai ou Faux
              </Select.Option>
            </Select>
          </Form.Item>
        )}

        <br />
        <Form.Item>
          <Button
            onClick={() => {
              confirmModalAsFunction({
                onConfirm: () => {
                  formRef.submit();
                },
                title: <h1>{t('Scale.ScaleConfirmMassOperationTitle')}</h1>,
                description: null,
                useCheckbox: true
              });
            }}
            danger
            loading={loading}
          >
            Lancer la requête de modification
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );
}
