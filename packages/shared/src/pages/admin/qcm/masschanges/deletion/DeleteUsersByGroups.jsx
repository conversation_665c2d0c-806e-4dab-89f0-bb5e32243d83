import ExoAvatar from '@/shared/components/User/ExoAvatar';
import AbstractGroupsManager from '@/shared/pages/admin/permissions/AbstractGroupsManager';
import { useTranslation } from 'react-i18next';
import React from 'react';
import { But<PERSON>, Popconfirm, Alert, notification, Collapse, List, Tag } from 'antd';
import { gql, useMutation, useQuery } from '@apollo/client';
import { mapRoleToShortRoleName } from '@/shared/utils/authority';

const DELETE_ALL_USERS_IN_GROUPS = gql`
  mutation DeleteAllUsersInGroups($groupIds: [ID]) {
    deleteAllUsersInGroups(groupIds: $groupIds)
  }
`;

const QUERY_COUNT_USERS_IN_GROUPS = gql`
  query CountUsersInGroups($groupIds: [ID]) {
    countUsersInGroups(groupIds: $groupIds)
  }
`;

const QUERY_PREVIEW_DELETE_USERS_IN_GROUPS = gql`
  query PreviewMassDeleteUsersInGroups($groupIds: [ID]) {
    previewMassDeleteUsersInGroups(groupIds: $groupIds) {
      id
      username
      firstName
      avatar
      isActive
      name
      email
      role
    }
  }
`;

export const DeleteUsersByGroups = () => {
  const { t } = useTranslation();

  const [groupIdArraySelection, setGroupIdArraySelection] = React.useState([]);
  const [deleteUsers, { loading }] = useMutation(DELETE_ALL_USERS_IN_GROUPS);

  //useQuery pour compter les utilisateurs dans les groupes sélectionnés
  const { loading: loadingCount, data: countData } = useQuery(QUERY_COUNT_USERS_IN_GROUPS, {
    variables: { groupIds: groupIdArraySelection },
    skip: groupIdArraySelection.length === 0,
    fetchPolicy: 'no-cache'
  });
  const countUsers = countData?.countUsersInGroups || 0;

  //QUERY_PREVIEW_DELETE_USERS_IN_GROUPS
  const { loading: loadingPreview, data: previewData } = useQuery(
    QUERY_PREVIEW_DELETE_USERS_IN_GROUPS,
    {
      variables: { groupIds: groupIdArraySelection },
      skip: groupIdArraySelection.length === 0,
      fetchPolicy: 'no-cache'
    }
  );
  const userList = previewData?.previewMassDeleteUsersInGroups || [];

  // Compter les rôles
  const roleCounts = React.useMemo(() => {
    const counts = {};
    userList.forEach((user) => {
      const role = user.role;
      counts[role] = (counts[role] || 0) + 1;
    });
    return counts;
  }, [userList]);

  // Générer le texte du header avec les totaux par rôle
  const roleLabels = [
    { key: 'ADMIN', color: 'geekblue' },
    { key: 'SUB_ADMIN', color: 'geekblue' },
    { key: 'TUTEUR', color: 'purple' },
    { key: 'USER', color: 'purple' },
    { key: 'PARENT', color: 'purple' },
    { key: 'COMMERCIAL', color: 'purple' }
  ];

  const headerWithCounts = (
    <span>
      {t('UserMassChange.UserList')}
      <span style={{ marginLeft: 16, fontWeight: 400, fontSize: 13 }}>
        {roleLabels.map(({ key, color }) =>
          roleCounts[key] ? (
            <Tag key={key} color={color} style={{ marginRight: 4 }}>
              {mapRoleToShortRoleName(key)}: {roleCounts[key]}
            </Tag>
          ) : null
        )}
      </span>
    </span>
  );
  console.log('headerWithCounts', headerWithCounts);

  const handleDeleteUsers = async () => {
    const notifKey = 'delete-users';
    notification.info({
      key: notifKey,
      message: t('UserMassChange.deletingUsers'),
      description: t('UserMassChange.deletingUsersInProgress'),
      duration: 0
    });
    try {
      await deleteUsers({ variables: { groupIds: groupIdArraySelection } });
      notification.success({
        key: notifKey,
        message: t('UserMassChange.usersDeletedWithSuccess'),
        description: ''
      });
      setGroupIdArraySelection([]);
    } catch (e) {
      notification.error({
        key: notifKey,
        message: t('UserMassChange.usersDeleteError'),
        description: e.message
      });
    }
  };

  return (
    <>
      <h1>{t('UserMassChange.deleteUsersByGroupsTitle')}</h1>
      <Alert
        type="warning"
        showIcon
        style={{ marginBottom: 16 }}
        message={t('Warning')}
        description={t('UserMassChange.deleteUsersByGroupsWarning')}
      />
      {/* Groupes */}
      <AbstractGroupsManager
        onChange={(toAdd, groupId) => {
          if (toAdd) {
            const temp = [...new Set([...groupIdArraySelection, groupId])];
            setGroupIdArraySelection(temp);
          } else if (toAdd === false) {
            setGroupIdArraySelection(
              groupIdArraySelection.filter(
                (item) => !(groupId === item || groupId === parseInt(item))
              )
            );
          }
        }}
        onChangeArray={
          (groupArray, toAdd) => {
            if (toAdd) {
              const temp = [...new Set([...groupIdArraySelection, ...groupArray])];
              setGroupIdArraySelection(temp);
            } else if (toAdd === false) {
              setGroupIdArraySelection(
                groupIdArraySelection.filter(
                  (item) => !(groupArray.includes(item) || groupArray.includes(parseInt(item)))
                )
              );
            } else {
              throw new Error(`toAdd ni true ni false, : ${JSON.stringify(toAdd)}`);
            }
          } // Si on ne veut pas utiliser de mutation, on peut utiliser cette fonction pour gérer les changements (envoi ids groupes en array)
        }
      />

      <br />
      <br />
      {/* Show user count in alert */}
      <Alert
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
        message={t('UserMassChange.usersInGroupsCount')}
        description={
          loadingCount
            ? t('UserMassChange.loadingUsersCount')
            : `${countUsers} ${t('UserMassChange.usersInSelectedGroups')}`
        }
      />

      <br />
      {/* show user list in collapsable */}
      <Collapse>
        <Collapse.Panel header={headerWithCounts} key="1">
          {loadingPreview ? (
            'Loading...'
          ) : userList.length === 0 ? (
            <Alert type="info" message={t('UserMassChange.noUsersInGroups')} showIcon />
          ) : (
            <List
              itemLayout="horizontal"
              dataSource={userList}
              renderItem={(user) => (
                <List.Item key={user.id}>
                  <List.Item.Meta
                    avatar={
                      <ExoAvatar avatar={user.avatar} isActive={user.isActive} size="large" />
                    }
                    title={
                      <span style={{ fontWeight: 500 }}>
                        {user.username}{' '}
                        <Tag
                          color={['ADMIN', 'SUB_ADMIN'].includes(user.role) ? 'geekblue' : 'purple'}
                          style={{ marginLeft: 8 }}
                        >
                          {mapRoleToShortRoleName(user.role)}
                        </Tag>
                      </span>
                    }
                    description={
                      <span>
                        {user.firstName} {user.name} <br />
                        <a href={`mailto:${user.email}`}>{user.email}</a>
                      </span>
                    }
                  />
                </List.Item>
              )}
            />
          )}
        </Collapse.Panel>
      </Collapse>

      <br />
      <br />
      <Popconfirm
        title={t('UserMassChange.deleteUsersByGroupsConfirm')}
        description={t('UserMassChange.deleteUsersByGroupsIrreversible')}
        onConfirm={handleDeleteUsers}
        okText={t('yes')}
        cancelText={t('no')}
        disabled={groupIdArraySelection.length === 0}
      >
        <Button
          type="primary"
          danger
          loading={loading}
          disabled={groupIdArraySelection.length === 0}
          style={{ marginTop: 24 }}
        >
          {t('UserMassChange.deleteUsersByGroupsButton')}
        </Button>
      </Popconfirm>
    </>
  );
};
