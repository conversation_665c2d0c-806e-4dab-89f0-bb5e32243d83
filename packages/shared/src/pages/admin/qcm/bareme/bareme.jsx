import { ErrorResult } from '@/shared/components/ErrorResult.jsx';
import { QUERY_ADMIN_ALL_SCALES } from '@/shared/graphql/qcm.js';
import { CreateEditBaremeModal } from '@/shared/pages/admin/qcm/bareme/modal/CreateEditBaremeModal.jsx';
import { renderUENameAndDescriptionWithFallback } from '@/shared/services/translate.js';
import { DeleteOutlined, EditOutlined, PlusSquareOutlined } from '@ant-design/icons';
import ProTable from '@ant-design/pro-table';
import { gql, useMutation, useQuery } from '@apollo/client';
import { Button, message, Popconfirm, Popover, Space, Tag } from 'antd';
import dayjs from 'dayjs';
import React, { Fragment, useState } from 'react';
import { useTranslation } from 'react-i18next';
import FullMediParticlesBreadCrumb from '@/shared/components/FullMediParticlesBreadCrumb';
import { QuestionAnswerType } from '@/shared/pages/admin/qcm/components/modal/CreateEditQuestionModal';

const MUTATION_DELETE_MCQ_SCALE = gql`
  mutation deleteMcqScale($id: ID!) {
    deleteMcqScale(id: $id)
  }
`;

const BaremeActions = ({ mcqScale, refetch }) => {
  const [editVisible, setEditVisible] = useState(false);
  const [deleteMcqScaleMutation] = useMutation(MUTATION_DELETE_MCQ_SCALE);
  const { t } = useTranslation();
  const deleteScale = async (id) => {
    try {
      const result = await deleteMcqScaleMutation({ variables: { id } });
      await refetch();
      message.success(t('DeletedWithSuccess'));
    } catch (e) {
      console.error(e);
      message.error("Le barême n'a pas été supprimé");
    }
  };

  const menu = (
    <Button danger icon={<DeleteOutlined />} onClick={() => deleteScale(mcqScale.id)}>
      {t('Delete')}
    </Button>
  );
  const buttonEdit = (
    <Button
      icon={<EditOutlined />}
      onClick={() => setEditVisible(true)}
      type="primary"
      shape="circle"
    />
  );
  return (
    <Fragment>
      <Space>
        {buttonEdit}
        <Popconfirm
          title="Êtes-vous sûr de vouloir supprimer?"
          onConfirm={() => deleteScale(mcqScale.id)}
          onCancel={() => {}}
          okText="Oui"
          cancelText="Non"
        >
          <Button danger icon={<DeleteOutlined />}></Button>
        </Popconfirm>
      </Space>
      <CreateEditBaremeModal
        closeModalHandler={() => {
          setEditVisible(false);
          refetch(); // Load new modifications
        }}
        isVisible={editVisible}
        modalType="UPDATE"
        refetch={refetch}
        loading={false}
        mcqScale={mcqScale}
      />
    </Fragment>
  );
};

export default function (props) {
  const { t } = useTranslation();
  const { loading, data, error, refetch } = useQuery(QUERY_ADMIN_ALL_SCALES, {
    fetchPolicy: 'no-cache'
  });
  const [createVisible, setCreateVisible] = useState(false);
  const mcqScales = data && data.mcqScales;
  const columns = [
    {
      title: t('Name'),
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: 'Type de question',
      dataIndex: 'questionType',
      key: 'questionType',
      render: (questionType, q) => {
        switch (questionType) {
          case 'mcq':
            return <Tag color="blue">QCM</Tag>;
          case 'ucq':
            return <Tag color="green">QCU</Tag>;
          case 'alphanumerical':
            return <Tag color="orange">{t('Alphanumerical')}</Tag>;
          case 'schema':
            return (
              <Tag color="purple">
                {t('Schemas.Schema')} : {t('Schemas.LegendsToPositionOnSchema')}
              </Tag>
            );
          case 'schemaFillInLegends':
            return (
              <Tag color="purple">
                {t('Schemas.Schema')} : {t('Schemas.LegendsToComplete')}
              </Tag>
            );
          case 'fillintheblanks':
            return <Tag color="cyan">{t('FillInTheBlanks')}</Tag>;
          case QuestionAnswerType.FLASHCARD:
            return <Tag>{t('Flashcard.Flashcard')}</Tag>;
          default:
            return <Tag color="red">N/A</Tag>;
        }
      }
    },
    {
      title: 'Valeur (pts)',
      dataIndex: 'rules',
      key: 'rules',
      render: (rules, q) => rules.pointsPerQuestion
    },
    {
      title: 'Note minimale',
      dataIndex: 'rules',
      key: 'rules',
      render: (rules, q) => {
        const grade = rules.minimumGrade;

        let color;
        if (grade > 0) {
          color = 'green';
        } else if (grade < 0) {
          color = 'red';
        } else {
          color = 'black';
        }

        return <span style={{ color }}>{grade}</span>;
      }
    },
    {
      title: 'Matières',
      dataIndex: 'ues',
      key: 'ues',
      render: (ues, scale) => (
        <>
          <Popover
            content={
              <div>
                {ues?.map((ue, k) => (
                  <div key={k}>{renderUENameAndDescriptionWithFallback(ue)}</div>
                ))}
              </div>
            }
            trigger="hover"
          >
            <Tag>
              {ues?.length} {ues?.length === 1 ? t('general.Subject') : t('general.Subjects')}
            </Tag>
          </Popover>
        </>
      )
    },
    {
      title: 'Dates',
      key: 'dates',
      render: (record) => {
        const createdAt = dayjs(record.createdAt).fromNow();
        const updatedAt = dayjs(record.updatedAt).fromNow();

        return (
          <div>
            <div>
              <strong>Créé le:</strong> {createdAt}
            </div>
            <div>
              <strong>Modifié le:</strong> {updatedAt}
            </div>
          </div>
        );
      }
    },
    {
      title: 'Actions',
      dataIndex: 'name',
      key: 'name',
      render: (_, mcqScale) => <BaremeActions mcqScale={mcqScale} refetch={refetch} />
    }
  ];

  if (error) {
    return <ErrorResult error={error} refetch={refetch} />;
  }
  return (
    <>
      {/*<AdminMenu selected="qcm/bareme">*/}
      <FullMediParticlesBreadCrumb title={t('ExercicesScale')} />
      <ProTable
        headerTitle={t('ExercicesScale')}
        search={false}
        loading={loading}
        columns={columns}
        dataSource={mcqScales}
        pagination={{
          defaultPageSize: 50,
          pageSizeOptions: [50, 100, 1000]
        }}
        scroll={{ x: true }}
        toolBarRender={(action) => [
          <Button
            type="primary"
            key="1"
            icon={<PlusSquareOutlined />}
            onClick={() => {
              setCreateVisible(true);
            }}
          >
            {t('newScale')}
          </Button>
        ]}
      />

      <CreateEditBaremeModal
        closeModalHandler={() => {
          setCreateVisible(false);
          refetch(); // Load new modifications
        }}
        isVisible={createVisible}
        modalType="CREATE"
        refetch={refetch}
        loading={loading}
        mcqScale={{}}
      />
    </>
  );
}
