import { But<PERSON>, Modal, Spin, Steps, message, Badge, notification} from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useMutation, useSubscription } from '@apollo/client';
import { SettingTwoTone } from '@ant-design/icons';
import {
  MUTATION_ACCEPT_QUESTION,
  MUTATION_ADD_LINK_QUESTIONS_IDS_WITH_QCM_ID,
  MUTATION_REJECT_QUESTION,
  MUTATION_STOP_GPT_GENERATION,
  SUBSCRIPTION_FETCH_QUESTION_ID_FROM_GPT_BACK
} from '@/shared/pages/admin/qcm/components/modal/AiCreateAndImport/AiExercisePrompts';
import { onErrorShowErrorsFunction } from '@/shared/utils/utils';
import { BlockPrevious } from '../components/BlockPrevious';

import CreateExerciseHoC from '@/shared/pages/admin/qcm/components/modal/AiCreateAndImport/CreateExerciseHoC.jsx';
import ImportExerciseHoC from '@/shared/pages/admin/qcm/components/modal/AiCreateAndImport/ImportExerciseHoC';
import { ManageAiCreatedExercises } from '@/shared/pages/admin/qcm/components/modal/AiCreateAndImport/ManageAiCreatedExercises';

// Dict qui encadre l'utilisation de ce componant
export const QUESTION_AI_CREATION_MODAL_TYPE = {
  EXERCISE_SERIE: 'serie_exercice', // Creation de question, validation et intégration à la série reliée
  QUESTION_STANDALONE: 'question_standalone', // Creation de question et validation
  IMPORT_EXERCICES_FROM_PDF_WITH_AI_IN_SERIE: 'import_exercices_from_pdf_with_ai_in_serie', // Importe des questions depuis un pdf dans une serie
  IMPORT_EXERCICES_FROM_PDF_WITH_AI_STANDALONE: 'import_exercices_from_pdf_with_ai_standalone' // Import importe des questions depuis un pdf dans la BDD
};

const EVENT_TYPE_SUBSCRIPTION = {
  END_GENERATION: 'END_GENERATION',
  NEW_QUESTION: 'NEW_QUESTION',
  ERROR_GENERATION: 'ERROR_GENERATION'
};

const CreateOrImportExercisesWithAiModal = ({
  closeModalHandler,
  isVisible,
  qcmId,
  qcmDefaultQuestionType,
  refetch,
  modalType
}) => {
  //// Les constantes
  const STEP_STATE = { PENDING: 'pending', ACCEPTED: 'accepted', REJECTED: 'rejected' }; // les états possible des steps
  const DATA_KEYS = { ID: 'id', STATE: 'state', TITLE: 'title', STEP: 'step' }; // les données de state à remplir
  const { t } = useTranslation();
  const dataFirstStep = {
    title: t('AiQuestionCreationModal.DefineParametersStepTitle'),
    icon: <SettingTwoTone style={{ cursor: 'pointer' }} />,
    onClick: async () => {
      await saveQuestionAndJumpToStep(0);
    }
  };

  const isCreation =
    modalType === QUESTION_AI_CREATION_MODAL_TYPE.EXERCISE_SERIE ||
    modalType === QUESTION_AI_CREATION_MODAL_TYPE.QUESTION_STANDALONE;
  const isImport =
    modalType === QUESTION_AI_CREATION_MODAL_TYPE.IMPORT_EXERCICES_FROM_PDF_WITH_AI_IN_SERIE ||
    modalType === QUESTION_AI_CREATION_MODAL_TYPE.IMPORT_EXERCICES_FROM_PDF_WITH_AI_STANDALONE;
  const needToImportInSerie =
    modalType === QUESTION_AI_CREATION_MODAL_TYPE.IMPORT_EXERCICES_FROM_PDF_WITH_AI_IN_SERIE ||
    modalType === QUESTION_AI_CREATION_MODAL_TYPE.EXERCISE_SERIE;

  const SpinWithNumber = ({ number, onClick }) => {
    // Le micro componant qui permet d'afficher un chiffre dans le spinner
    return (
      <div
        style={{ position: 'relative', display: 'inline-block', cursor: 'pointer' }}
        onClick={onClick}
      >
        <Spin size="large" />
        <div
          style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            fontSize: '16px',
            color: 'black'
          }}
        >
          {number}
        </div>
      </div>
    );
  };

  //// Les states
  const [shouldSubscribe, setShouldSubscribe] = useState(true); // enable de la subscription ou pas
  const [frontendTokenForSubscription, setFrontendTokenForSubscription] = useState(null); // frontendToken => sert pour synchroniser la subscription et la query
  const [step, setStep] = useState(0); // le step actuel => utile pour afficher le componant associé
  const [dataDict, setDataDict] = useState({}); // Contiendra les données sous la forme : // Contiendra : {1:{id,ref,status,etc...}} où 1 est le step
  //const [dataDict,setDataDict]=useState({1:{id:50223,state:"rejected",title:"1",step:1},2:{id:49023,state:"rejected",title:"2",step:2},3:{id:49034,state:"accepted",title:"3",step:3}})
  const [dataForStepper, setDataForStepper] = useState([dataFirstStep]);
  const [isConfirmationExitModalOpen, setIsConfirmationExitModalOpen] = useState(false);
  const [isSubscriptionLoading, setIsSubscriptionLoading] = useState(false); // Permet de savoir si la query de génération a finie dans le back
  const [isAllAcceptationQuestionLoading, setIsAllAcceptationQuestionLoading] = useState(false);
  const questionWrapperRef = useRef(null);

  //// Les useEffects
  useEffect(() => {
    // Use effect qui contrôle la frise du stepper, qui est mis à jour dès que dataDict est updated (init , creation de questions (blank), nouvelles questions)
    const tempArray = [dataFirstStep];
    Object.keys(dataDict).map((key) => {
      const { title, state, step } = dataDict[key];
      if (state === STEP_STATE.ACCEPTED) {
        tempArray.push({
          title: '',
          icon: (
            <Badge
              count={key}
              size={'default'}
              color="green"
              style={{ cursor: 'pointer' }}
              onClick={async () => {
                await saveQuestionAndJumpToStep(step);
              }}
            />
          )
        });
      } else if (state === STEP_STATE.REJECTED) {
        tempArray.push({
          title: '',
          icon: (
            <Badge
              count={key}
              size={'default'}
              color="red"
              style={{ cursor: 'pointer' }}
              onClick={async () => {
                await saveQuestionAndJumpToStep(step);
              }}
            />
          )
        });
      } else if (state === STEP_STATE.PENDING) {
        tempArray.push({
          title: '',
          icon: (
            <SpinWithNumber
              number={key}
              onClick={async () => {
                await saveQuestionAndJumpToStep(step);
              }}
            />
          )
        });
      }
    });
    setDataForStepper(tempArray);
  }, [dataDict]);

  ///////////////////////////// Stop de la query lorsque l'user n'est "plus là"
  // Arrête la subscription linké à un front-end Token => ESSENTIEL lorsque l'on part de la page // Demonte le componant => Évite de charger dans le vide
  const [stopGptGeneration] = useMutation(MUTATION_STOP_GPT_GENERATION, {
    onError: (errors) => {
      onErrorShowErrorsFunction(errors);
    }
  });

  // Wrapper qui permet de simplement arrêter la génération du componant actuel
  async function wrapperStopGptIfLoading() {
    if (isSubscriptionLoading) {
      const result = await stopGptGeneration({
        variables: { frontendToken: frontendTokenForSubscription }
      });
      if (result) {
        setIsSubscriptionLoading(false);
      }
    }
  }

  // use effect Essentiel qui arrête la génération de question dans le back lorsque le componant est arrêté / démonté / L'user quitte la page
  useEffect(() => {
    const handleBeforeUnload = () => {
      wrapperStopGptIfLoading();
    }; // gere les fermetures de window / onglet
    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    }; // Gere la fermeture du componant ;
  }, [stopGptGeneration, frontendTokenForSubscription, isSubscriptionLoading]);

  //// Les queries
  const { data, loading, error } = useSubscription(SUBSCRIPTION_FETCH_QUESTION_ID_FROM_GPT_BACK, {
    variables: { frontendToken: frontendTokenForSubscription },
    onData({ data }) {
      // Recup du payload de la subscription
      const payload = data?.data?.generateAiQuestions;

      // Recup de l'event type
      const eventType = payload?.eventType;

      // Vérification que l'événement est valide
      if (!Object.keys(EVENT_TYPE_SUBSCRIPTION).includes(eventType)) {
        notification.error({
          message: 'EventType non reconnu',
          description: `Reçu : '${eventType}'. Les types valides sont : ${Object.keys(EVENT_TYPE_SUBSCRIPTION).join(', ')}`,
          duration: 0
        });
        return null;
      }

      // Execution conditionnellement au type de subscription
      if (eventType === EVENT_TYPE_SUBSCRIPTION.END_GENERATION) {
        setIsSubscriptionLoading(false);
      } else if (eventType === EVENT_TYPE_SUBSCRIPTION.NEW_QUESTION) {
        // Recup des questionsId
        const questionId = payload?.questionId;
        const arrayIndex = payload?.arrayIndex;

        // Si on est dans la création de question, alors on modifie le step pour chaque question
        if (
          modalType === QUESTION_AI_CREATION_MODAL_TYPE.EXERCISE_SERIE ||
          modalType == QUESTION_AI_CREATION_MODAL_TYPE.QUESTION_STANDALONE
        ) {
          replaceId(arrayIndex + 1, questionId);
        }

        // Si on est dans l'importation de question, alors pour la question 1 on modifie la question, sinon on ajoute le state
        else if (
          modalType ===
            QUESTION_AI_CREATION_MODAL_TYPE.IMPORT_EXERCICES_FROM_PDF_WITH_AI_STANDALONE ||
          modalType == QUESTION_AI_CREATION_MODAL_TYPE.IMPORT_EXERCICES_FROM_PDF_WITH_AI_IN_SERIE
        ) {
          if (Object.keys(dataDict).length === 1 && dataDict?.['1']?.['id'] === null) {
            replaceId(1, questionId); // Ici, c'est fixé, on remplace que l'index === 1 car on remplace le premier élément, le reste est ajouté dynamiquement
          } else {
            addOneState({ id: questionId });
          }
        }
      } else if (eventType === EVENT_TYPE_SUBSCRIPTION.ERROR_GENERATION) {
        notification.error({
          description: payload?.errorMessage || 'pas de message dans le Error.Payload',
          duration: 0
        });
      }
    },

    onError: (errors) => {
      setIsSubscriptionLoading(false);
      onErrorShowErrorsFunction(errors, 0);
    },
    skip: !shouldSubscribe || !frontendTokenForSubscription
  }); //// La subscription qui permet de recevoir les question dès que gpt a fini de les créer

  const [
    acceptQuestionHook,
    { data: acceptationData, loading: acceptationLoading, error: acceptationError }
  ] = useMutation(MUTATION_ACCEPT_QUESTION);
  const [
    rejectQuestionHook,
    { data: rejectionData, loading: rejectionLoading, error: rejectionError }
  ] = useMutation(MUTATION_REJECT_QUESTION);
  const [addLinkQuestionsFunction, { data: addData, loading: addLoading, error: addError }] =
    useMutation(MUTATION_ADD_LINK_QUESTIONS_IDS_WITH_QCM_ID);

  //// Les fonctions helper
  const createBlankStatesRework = (n) => {
    const createDataStructure = (step) => {
      const dataStructure = {
        [DATA_KEYS.ID]: null, // le placeholder de l'id de la question
        [DATA_KEYS.STATE]: STEP_STATE.PENDING, // Le state du step (pending/loading , accepted , rejected)
        [DATA_KEYS.TITLE]: t('AiQuestionCreationModal.DynamicEditQuestionStepWithNumber', {
          number: step
        }), // Le titre
        [DATA_KEYS.STEP]: step // le numéro du step de 1 à n ;
      };
      return dataStructure;
    };

    const dynamicObject = {};
    for (let i = 1; i <= n; i++) {
      dynamicObject[i] = createDataStructure(i);
    }

    setDataDict(dynamicObject);
    return dynamicObject;
  }; /// Fonction qui permet de créer le placeholder suivant : {1:{id,state,title,step}, 2:{id,state,title,step}} pour accueillir la génération de question.

  // fonction qui contairement à createBlankStatesRework permet de rajouter un état full
  const addOneState = ({ id }) => {
    const step = Object.keys(dataDict).length + 1;

    const structure = {
      [DATA_KEYS.ID]: id, // le placeholder de l'id de la question
      [DATA_KEYS.STATE]: STEP_STATE.REJECTED, // Le state du step (pending/loading , accepted , rejected)
      [DATA_KEYS.TITLE]: t('AiQuestionCreationModal.DynamicEditQuestionStepWithNumber', {
        number: step
      }), // Le titre
      [DATA_KEYS.STEP]: step // le numéro du step de 1 à n ;
    };

    // Add a dataDict la Structure
    setDataDict((prev) => {
      const newStruct = {
        ...prev,
        [step]: structure
      };

      return newStruct;
    });
  };

  const replaceKeyInDataDict = (step, key, newValue) => {
    setDataDict((prevState) => {
      // Vérifier si la step existe
      if (!prevState.hasOwnProperty(step)) {
        console.error("La step n'est pas trouvée dans la fonction de remplacement");
        return prevState;
      }

      const subDict = prevState[step];

      // Vérifier si la key existe dans le subDict
      if (!subDict.hasOwnProperty(key)) {
        console.error("La key n'est pas trouvée dans le sub-dico");
        return prevState;
      }

      // Mettre à jour la valeur
      const updatedSubDict = {
        ...subDict,
        [key]: newValue
      };

      return {
        ...prevState,
        [step]: updatedSubDict
      };
    });

    // Si la fonction atteint ce point, cela signifie que la mise à jour a été réussie
    return true;
  }; // Fonction qui permet de remplacer une key, pour une step, par une new value

  const StepFooter = ({ type, setStep }) => {
    const oneMoreStep = () => {
      setStep((prevState) => prevState + 1);
    };
    const oneLessStep = () => {
      setStep((prevState) => prevState - 1);
    };

    const showOneLessStep = type !== 'first';
    const showOneMoreStep = type !== 'last';
    const enableNone = type === 'None';

    return (
      <>
        <div
          style={{
            display: 'flex',
            flewGrow: true,
            justifyContent: 'space-between',
            maxWidth: '80%',
            margin: 'auto'
          }}
        >
          {
            <Button
              disabled={!showOneLessStep || enableNone}
              type={'primary'}
              onClick={() => oneLessStep()}
            >
              {t('Previous')}
            </Button>
          }
          {
            <Button
              disabled={!showOneMoreStep || enableNone}
              type={'primary'}
              onClick={() => oneMoreStep()}
            >
              {t('Next')}
            </Button>
          }
        </div>
      </>
    );
  }; //// Componant pour changer de step, note => c'est important de faire via le setStep

  const resetExerciseData = () => {
    setDataDict({});
    setStep(0);
  }; // Reset le modal

  const replaceId = (step, newId) => {
    const bool1 = replaceKeyInDataDict(step, DATA_KEYS.ID, newId);
    const bool2 = replaceKeyInDataDict(step, DATA_KEYS.STATE, STEP_STATE?.REJECTED);
    return bool1 && bool2;
  }; // Helper qui prend un step et le nouvel id à set et change l'id et le state => permet de remplacer un blank State par un state avec le bon ID et le bon state

  const switchState = (step) => {
    setDataDict((prevState) => {
      // Vérifier que la step existe
      if (!prevState.hasOwnProperty(step)) {
        console.error("La step n'est pas trouvée dans la fonction de switch de state");
        return prevState;
      }

      const subDict = prevState[step];

      // Vérifier si la key existe dans le subDict
      if (!subDict.hasOwnProperty(DATA_KEYS.STATE)) {
        console.error("La key n'est pas trouvée dans le sub-dico");
        return prevState;
      }

      // Definition de la new value par rapport à l'état actuel
      let newValue;
      if (subDict[DATA_KEYS.STATE] === STEP_STATE.REJECTED) {
        newValue = STEP_STATE.ACCEPTED;
      } else if (subDict[DATA_KEYS.STATE] === STEP_STATE.ACCEPTED) {
        newValue = STEP_STATE.REJECTED;
      } else {
        newValue = STEP_STATE.PENDING;
      }

      // Mettre à jour la valeur
      const updatedSubDict = {
        ...subDict,
        [DATA_KEYS.STATE]: newValue
      };

      return {
        ...prevState,
        [step]: updatedSubDict
      };
    });
  }; // Fonction qui pour un step, switch le state de REJECTED à ACCEPTED et inversement.

  const setAcceptationState = (step, boolean) => {
    setDataDict((prevState) => {
      // Vérifier que la step existe
      if (!prevState.hasOwnProperty(step)) {
        console.error("La step n'est pas trouvée dans la fonction de switch de state");
        return prevState;
      }

      const subDict = prevState[step];

      // Vérifier si la key existe dans le subDict
      if (!subDict.hasOwnProperty(DATA_KEYS.STATE)) {
        console.error("La key n'est pas trouvée dans le sub-dico");
        return prevState;
      }

      // Definition de la new value par rapport à l'état actuel
      let newValue;

      if (boolean === true) {
        newValue = STEP_STATE.ACCEPTED;
      } else if (boolean === false) {
        newValue = STEP_STATE.REJECTED;
      } else {
        newValue = STEP_STATE.PENDING;
      }

      // Mettre à jour la valeur
      const updatedSubDict = {
        ...subDict,
        [DATA_KEYS.STATE]: newValue
      };

      return {
        ...prevState,
        [step]: updatedSubDict
      };
    });
  }; // fonction qui set State

  // Fonction qui gère la fermeture du modal => stop de la subscription, reset des données d'exercices, close du modal
  const closeModalWrapper = async () => {
    await wrapperStopGptIfLoading(); // arrête le processing de GPT
    resetExerciseData(); // Reset les données d'exercices
    setFrontendTokenForSubscription(null);
    closeModalHandler(); // Ferme le modal
  };

  const modifyAiAcceptationState = async ({ arg, questionId }) => {
    // Permet d'accepter ou refuser une question
    if (arg === 'ACCEPTATION') {
      return acceptQuestionHook({ variables: { id: questionId } });
    } else if (arg === 'REJECTION') {
      return rejectQuestionHook({ variables: { id: questionId } });
    }
  };

  async function acceptAllQuestionFunction() {
    try {
      // Fonction qui permet a condition que le chargement ai fini, d'accepter toutes les questions
      setIsAllAcceptationQuestionLoading(true);

      // Verifie que la subscription s'est bien arrêtée
      if (isSubscriptionLoading) {
        setIsAllAcceptationQuestionLoading(false);
        message.error(t('AiQuestionCreationModal.ErrorSubscriptionIsStillLoading'));
        return null;
      }

      // Récup tous les questionId depuis dataDict
      const allQuestionsArray = dataDict
        ? Object.keys(dataDict).map((keyId) => dataDict[keyId]?.[DATA_KEYS?.ID])
        : [];

      // Vérifie que l'array est non null
      if (allQuestionsArray.length === 0) {
        setIsAllAcceptationQuestionLoading(false);
        message.error(t('AiQuestionCreationModal.ErrorNumberOfQuestionsToImportIsZero'));
        return null;
      }

      // Verifie que chaque ID est valide
      const isAllIds = allQuestionsArray?.every((element) => Number.isInteger(element));
      if (!isAllIds) {
        setIsAllAcceptationQuestionLoading(false);
        message.error(t('AiQuestionCreationModal.ErreurInIdForFullImportation'));
        return null;
      }

      // Acceptation et possiblement linkage de chaque questions
      const mutationPromises = allQuestionsArray.map(async (qId) => {
        // l'array qui contiendra les deux promises possible pour une question (validation + link)
        const promiseArray = [];

        // Mise en promise de la mutation d'acceptation
        promiseArray.push(
          modifyAiAcceptationState({ arg: 'ACCEPTATION', questionId: qId })
            // Si réussite, retourne true
            .then(() => true)

            // Si echec, retourne un message d'erreur + false
            .catch((error) => {
              message.error(
                `${t('AiQuestionCreationModal.ErrorValidationQuestionInFullImportation', { qId })} (${error})`
              );
              return false;
            })
        );

        ////// Mise en promise de la mutation de linkage
        // Si on doit effectuer la mutation alors on l'effectue, sinon on retourne automatiquement un true
        if (needToImportInSerie) {
          if (qcmId) {
            promiseArray.push(
              addLinkQuestionsFunction({ variables: { qcmId, questionId: qId } })
                // Si réussite, returne true
                .then(() => true)

                // Si echec, return un message d'erreur + false
                .catch((error) => {
                  message.error(
                    `${t('AiQuestionCreationModal.ErrorLinkageQuestionInFullImportation', {
                      qId,
                      qcmId
                    })} (${error})`
                  );
                  return false;
                })
            );
          } else {
            throw new Error('error : pas de qcmId pour linker');
          }
        } else {
          // On retourne une promise qui resolve à true
          promiseArray.push(Promise.resolve(true));
        }

        // Await de la réalisation des deux promises
        const fullfiledPromisesArray = await Promise.all(promiseArray);

        // Si les deux promises sont 'true', alors on dit que l'exercice est validé => normalement, pas de raison pour que 1 marche et pas l'autre.
        const isOk = fullfiledPromisesArray.reduce(
          (acc, valeurCourante) => acc && valeurCourante,
          true
        );

        // Si c'est 'OK', alors on set l'acceptation state à true.
        if (isOk) {
          const subObject = Object.values(dataDict).find((item) => item.id === qId);
          const localStep = subObject?.[DATA_KEYS?.STEP];
          setAcceptationState(localStep, true);
        }
      });

      // Await des deux sous-promise pour chaque question
      await Promise.all(mutationPromises);

      // indication que l'on a fini de valider toutes les questions
      setIsAllAcceptationQuestionLoading(false);
      return true;
    } catch (e) {
      setIsAllAcceptationQuestionLoading(false);
    }
  }

  const saveQuestion = async () => {
    // Fonction qui permet de sauvegarder la question en utilisant la ref de la questionWrapper si elle existe
    if (questionWrapperRef.current) {
      return questionWrapperRef?.current?.saveQuestion();
    }
  };

  const saveQuestionAndJumpToStep = async (step) => {
    // Fonction qui save le componant de question si il existe et change de step
    await saveQuestion();
    setStep(step);
  };

  return (
    <Modal
      title={
        isCreation
          ? t('AiQuestionCreationModal.ModalBigTitle')
          : t('MathpixIntegration.ModalBigTitle')
      }
      open={isVisible}
      onCancel={() => {
        setIsConfirmationExitModalOpen(true);
      }}
      footer={null}
      closable
      confirmLoading={false}
      width="100%"
      destroyOnClose
      style={{ marginLeft: 0, marginRight: 0, maxWidth: '100%' }}
    >
      <>
        <BlockPrevious />
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          {isSubscriptionLoading === true && (
            <>
              <Badge status="processing" text={t('AiQuestionCreationModal.GeneratingMarker')} />
              &nbsp;
              <Button
                onClick={async () => {
                  await wrapperStopGptIfLoading();
                }}
                type={'primary'}
                danger
                size={'small'}
              >
                {t('AiQuestionCreationModal.StopGptQueryButtonLabel')}
              </Button>
            </>
          )}
          {isSubscriptionLoading === false && Object.keys(dataDict)?.length > 0 && (
            <Button
              type={'primary'}
              loading={isAllAcceptationQuestionLoading}
              disabled={isAllAcceptationQuestionLoading}
              onClick={async () => {
                const response = await acceptAllQuestionFunction();
                if (response) {
                  await closeModalWrapper();
                }
              }}
            >
              {t('AiQuestionCreationModal.AcceptAllButtonLabel')}
            </Button>
          )}
        </div>

        {/* affichage des steps du stepper */}
        {<Steps current={step} items={dataForStepper} />}
        <br />

        {/* affichage du premier componant */}
        <div style={{ display: step === 0 ? 'block' : 'none' }}>
          <StepFooter
            type={dataDict && Object.keys(dataDict)?.length > 0 ? 'first' : 'None'}
            setStep={setStep}
          />
          {isCreation && (
            <CreateExerciseHoC
              qcmDefaultQuestionType={qcmDefaultQuestionType}
              createBlankStatesRework={createBlankStatesRework}
              setShouldSubscribe={setShouldSubscribe}
              frontendTokenForSubscription={frontendTokenForSubscription}
              setFrontendTokenForSubscription={setFrontendTokenForSubscription}
              setStep={setStep}
              setIsSubscriptionLoading={setIsSubscriptionLoading}
              wrapperStopGptIfLoading={wrapperStopGptIfLoading}
            />
          )}
          {isImport && (
            <ImportExerciseHoC
              qcmDefaultQuestionType={qcmDefaultQuestionType}
              createBlankStatesRework={createBlankStatesRework}
              setShouldSubscribe={setShouldSubscribe}
              frontendTokenForSubscription={frontendTokenForSubscription}
              setFrontendTokenForSubscription={setFrontendTokenForSubscription}
              setStep={setStep}
              setIsSubscriptionLoading={setIsSubscriptionLoading}
              wrapperStopGptIfLoading={wrapperStopGptIfLoading}
            />
          )}
          {/* ↓ enable le boutton 'suivant' uniquement si on a un dataDict qui n'est pas vide  */}
          <StepFooter
            type={dataDict && Object.keys(dataDict)?.length > 0 ? 'first' : 'None'}
            setStep={setStep}
          />
        </div>

        {/* affichage du componant Exercice */}
        {step > 0 && (
          <>
            {dataDict?.[step]?.[DATA_KEYS.STATE] === STEP_STATE?.PENDING ? (
              <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                <div
                  style={{
                    width: '200px',
                    height: '200px',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center'
                  }}
                >
                  <Spin />
                </div>

                <div style={{ width: '100%' }}>
                  <StepFooter type={'last'} setStep={setStep} />
                  {/*<Button onClick={()=>{setStep(step-1)}}>previous</Button>*/}
                </div>
              </div>
            ) : (
              <>
                <ManageAiCreatedExercises
                  stepNode={dataDict[step]}
                  switchState={switchState}
                  qcmId={qcmId}
                  setStep={setStep}
                  isLastStep={step === Object.keys(dataDict).length}
                  modalType={modalType}
                  setAcceptationState={setAcceptationState}
                  refetchQcm={refetch}
                  closeModalHandler={() => {
                    resetExerciseData();
                    closeModalHandler();
                  }}
                  isSubscriptionLoading={isSubscriptionLoading}
                  ref={questionWrapperRef}
                />
                <Steps current={step} items={dataForStepper} />
              </>
            )}
          </>
        )}

        <Modal
          title={t('AiQuestionCreationModal.WarningConfirmationExitModalTitle')}
          open={isConfirmationExitModalOpen}
          onCancel={() => {
            setIsConfirmationExitModalOpen(false);
          }}
          onOk={() => {
            setIsConfirmationExitModalOpen(false);
            resetExerciseData();
            closeModalHandler();
          }}
          closable
          confirmLoading={false}
          width="300px"
          bodyStyle={{ paddingTop: '0' }}
          footer={[
            <Button
              onClick={async () => {
                setIsConfirmationExitModalOpen(false);
                await closeModalWrapper();
              }}
              danger
              type={'primary'}
            >
              {t('AiQuestionCreationModal.WarningConfirmationExitModalLeaveButton')}
            </Button>,

            <Button
              type={'primary'}
              onClick={() => {
                setIsConfirmationExitModalOpen(false);
              }}
            >
              {t('AiQuestionCreationModal.WarningConfirmationExitModalStayButton')}
            </Button>
          ]}
          destroyOnClose
        >
          <div>{t('AiQuestionCreationModal.WarningConfirmationExitModalExplanation')}</div>
        </Modal>
      </>
    </Modal>
  );
};

export { CreateOrImportExercisesWithAiModal };
