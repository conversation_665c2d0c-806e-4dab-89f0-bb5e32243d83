import { QUERY_ANSWERS_FOR_QUESTION } from '@/shared/graphql/qcm.js';
import useLocalStorage from '@/shared/hooks/useLocalStorage.js';
import { QuestionAnswerType } from '@/shared/pages/admin/qcm/components/modal/CreateEditQuestionModal.jsx';
import { ExerciseEditionContext } from '@/shared/pages/admin/qcm/context/ExerciseEditionContext.jsx';
import { QuestionContext } from '@/shared/pages/admin/qcm/context/QuestionContext.jsx';
import { showGqlErrorsInMessagePopupFromException } from '@/shared/utils/utils.js';
import { DownOutlined, PlusSquareOutlined, UpOutlined, PlusOutlined } from '@ant-design/icons';
import { useQuery } from '@apollo/client';
import { Button, Form, message, Alert, notification } from 'antd';
import React, { memo, useContext, useEffect, useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { EditQuestionAnswer } from './EditQuestionAnswer.jsx';
import { AiEnhancementMenu } from '@/shared/pages/admin/qcm/components/modal/components/AiEnhancementMenu/AiEnhancementMenu';
import { enhancementType } from '@/shared/pages/admin/qcm/components/modal/components/AiEnhancementMenu/sharedConstantesFiles';

export const equationEditorLink = 'https://www.codecogs.com/latex/eqneditor.php?lang=fr-fr';

export const EditAnswersQuestion = memo(
  ({
    questionId,
    onUpdate,
    autoUpdateAllAnswer = false,
    aptoriaAnswerType,
    enabledAiEnhancementMenu = false
  }) => {
    const { t } = useTranslation();
    const { CreateAnswer, EditAnswer, RemoveAnswer, createDefaultNumberOfAnswers } =
      useContext(QuestionContext);
    const { setAnswerValidationArray } = useContext(ExerciseEditionContext);
    const { loading, data, error, refetch } = useQuery(QUERY_ANSWERS_FOR_QUESTION, {
      fetchPolicy: 'no-cache',
      variables: { id: questionId }
    });

    const question = data?.question;
    const answers = data && data.question && data.question.answers;
    const hasAnswers = answers && answers.length > 0;
    const [answersCollapsed, setAnswersCollapsed] = useLocalStorage(
      'admin-questions-answers-collapsed',
      false
    );

    /* Setup answerValidationArray for TRUE/FALSE check validation later */
    useEffect(() => {
      if (answers) {
        let answerValidationArray = [];
        // Si au moins une réponse a pas vrai/faux
        answers?.forEach((answer) => {
          let answerValidationObj = { id: answer.id, valid: true };
          if (question?.type === null && !question?.isAnswerFreeText && answer.isTrue === null) {
            answerValidationObj.valid = false;
          }
          answerValidationArray.push(answerValidationObj);
        });
        console.log({ answerValidationArray });
        setAnswerValidationArray(answerValidationArray); // invalide
      }
    }, [data]);

    const onDelete = async (answer) => {
      try {
        await RemoveAnswer({
          variables: {
            id: answer.id
          }
        });
        message.success(t('DeletedWithSuccess'));
        onUpdate();
        await refetch();
      } catch (e) {
        showGqlErrorsInMessagePopupFromException(e);
      }
    };

    const addDefault = async () => {
      try {
        await createDefaultNumberOfAnswers(questionId);
        const numberOfAnswers = 5;
        notification.success({ message: `${numberOfAnswers} réponses ajoutées` });
        onUpdate();
        await refetch();
      } catch (e) {
        console.error(e);
        showGqlErrorsInMessagePopupFromException(e);
      }
    };

    const add = async () => {
      try {
        await CreateAnswer({
          variables: {
            answer: { questionId }
          }
        });
        // Réponse vide
        notification.success({ message: 'Réponse ajoutée' });
        onUpdate();
        await refetch();
      } catch (e) {
        console.error(e);
        showGqlErrorsInMessagePopupFromException(e);
      }
    };

    const onEdit = async (answer, answerLetter) => {
      try {
        const answerDTO = { ...answer, questionId };
        delete answerDTO.id;

        await EditAnswer({
          variables: {
            answer: answerDTO,
            id: answer.id
          }
        });
        // notification.success({ message: `Réponse ${answerLetter} mise à jour !` })
        onUpdate();
        await refetch();
      } catch (e) {
        console.error(e);
        showGqlErrorsInMessagePopupFromException(e);
      }
    };

    const [updateAllAnswer, setUpdateAllAnswer] = useState(autoUpdateAllAnswer); // TODO REMOVE

    useEffect(() => {
      if (updateAllAnswer) {
        setUpdateAllAnswer(false);
      }
    }, [updateAllAnswer]);
    useEffect(() => {
      if (autoUpdateAllAnswer) {
        setUpdateAllAnswer(true);
      }
    }, [autoUpdateAllAnswer]);

    const answerTypeText = () => {
      switch (aptoriaAnswerType) {
        case QuestionAnswerType.FREE_TEXT:
          return 'Texte libre';
        case QuestionAnswerType.RADIO:
          return 'Un seul choix possible parmi les réponses';
        case QuestionAnswerType.CHECKBOX:
          return 'Plusieurs choix possibles parmi les réponses';
        case QuestionAnswerType.MULTIPLE_CHOICE_IN_LIST:
          return 'Choix multiple dans une liste';
        case QuestionAnswerType.UNIQUE_CHOICE_IN_LIST:
          return 'Choix unique dans une liste';
        case QuestionAnswerType.ALPHANUMERICAL_OR_NUMERICAL:
          return 'Alphanumérique ou numérique';
        case QuestionAnswerType.ALPHANUMERICAL:
          return 'Alphanumérique';
        case QuestionAnswerType.NUMERICAL:
          return 'Numérique';

        default:
          return 'Plusieurs choix possibles parmi les réponses';
      }
    };

    const [keyToForceReload, setKeyToForceReload] = useState(0);

    const refArray = useRef([]);

    return (
      <div>
        <div
          style={{
            display: 'flex',
            flexWrap: 'wrap',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}
        >
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'flex-start',
              flexWrap: 'wrap',
              flex: 1
            }}
          >
            <h2>
              {t('NumberOfAnswers')} {answers?.length}
            </h2>
            <Button type="text" onClick={() => setAnswersCollapsed(!answersCollapsed)}>
              {answersCollapsed ? 'Tout déplier' : 'Tout replier'}{' '}
              {answersCollapsed ? <DownOutlined /> : <UpOutlined />}
            </Button>
          </div>
          {/* Enhancement all items */}
          {enabledAiEnhancementMenu && (
            <AiEnhancementMenu
              style={{ marginRight: 16 }}
              secondary
              questionId={questionId}
              refetch={async () => {
                await refetch();
                setAnswersCollapsed(false);
                setKeyToForceReload(keyToForceReload + 1);
              }}
              componantType={enhancementType.ALL_ANSWERS}
            />
          )}
        </div>

        <h5>{answerTypeText()}</h5>

        {answers?.map((answer, key) => (
          <div key={key} style={{ marginBottom: '4px' }}>
            <EditQuestionAnswer
              autoUpdateAnswer={updateAllAnswer}
              answer={answer}
              questionId={questionId}
              key={`${key}-${keyToForceReload}`}
              n={key}
              onDelete={onDelete}
              onEdit={onEdit}
              refetch={refetch}
              aptoriaAnswerType={aptoriaAnswerType}
              answersCollapsed={answersCollapsed}
              enabledAiEnhancementMenu={enabledAiEnhancementMenu}
              ref={(el) => (refArray.current[key] = el)}
            />
          </div>
        ))}

        <Form.Item>
          {!hasAnswers && (
            <div>
              <Alert
                message="Cette question ne comporte aucune réponse"
                type="warning"
                showIcon
                closable
              />
              <Button onClick={addDefault} block>
                {' '}
                <PlusOutlined /> Ajouter 5 réponses
              </Button>
              <br />
            </div>
          )}
        </Form.Item>

        {![QuestionAnswerType.FREE_TEXT, QuestionAnswerType.ALPHANUMERICAL_OR_NUMERICAL].includes(
          aptoriaAnswerType
        ) && (
          <Form.Item>
            <Button type="primary" onClick={add} size={'large'} icon={<PlusSquareOutlined />}>
              {[QuestionAnswerType.ALPHANUMERICAL, QuestionAnswerType.NUMERICAL].includes(
                aptoriaAnswerType
              )
                ? t('AddAcceptedAnswerItem')
                : t('AddItem')}
            </Button>
          </Form.Item>
        )}

        <br />
        <br />
      </div>
    );
  }
);
