import { Select, Tag } from 'antd';
import React, { useEffect, useState } from 'react';
import { useQuery } from '@apollo/client';
import { QUERY_ALL_QCM_TYPE } from '@/shared/graphql/qcm';
import { onErrorShowErrorsFunction } from '@/shared/utils/utils';
import { CONTENT_TYPE_VALUES } from '@/shared/pages/admin/qcm/types-qcm';
import { useTranslation } from 'react-i18next';
import { useAiCreateAndImportContext } from '@/shared/pages/admin/qcm/components/modal/AiCreateAndImport/CreateAndImportAiContextProvider';

const ExerciseTagSelection = ({ qcmDefaultQuestionType }) => {
  const { t } = useTranslation();
  const { exerciceTag, setExerciceTag } = useAiCreateAndImportContext();

  // States du componant
  const [exerciceTagOption, setExerciceTagOption] = useState([]); // Les types de qcm à proposer

  // UseEffect
  useEffect(() => {
    // Souvent, au first load, les données mettent du temps à arriver, on utilise ça pour actualiser les données une fois loaded
    setExerciceTag(qcmDefaultQuestionType?.map((node) => node?.id));
  }, [qcmDefaultQuestionType]);

  // Querie, récupérant notament les options
  const { loading } = useQuery(QUERY_ALL_QCM_TYPE, {
    onError: (errors) => {
      onErrorShowErrorsFunction(errors);
    },
    onCompleted: (data) => {
      const allTypeQcm = data?.allTypeQcm;
      if (allTypeQcm) {
        // Filtration pour prendre les tag associés aux exercices
        const validExercicesTag = allTypeQcm?.filter(
          (ty) => ty.contentType === CONTENT_TYPE_VALUES.EXERCISE
        ); // Constante qui sera mise à jour lorsque les types d'exercice se seront fait fetch

        const validOptionsExercicesTag = validExercicesTag.map((node) => ({
          value: node?.id,
          label: node?.name
        }));

        setExerciceTagOption(validOptionsExercicesTag);
      }
    }
  });

  return (
    <div style={{ display: 'flex', margin: '5px' }}>
      <span style={{ minWidth: '200px', textAlign: 'end', alignContent: 'center' }}>
        {t('AiQuestionCreationModal.QuestionFormatExerciseTypeSelecterLabel')}
      </span>
      <Select
        mode="multiple"
        size="large"
        style={{ width: 500, marginLeft: '5px' }}
        tagRender={({ label, value, closable, onClose, key }) => (
          <Tag
            value={value}
            key={key}
            color="geekblue"
            closable={closable}
            onClose={onClose}
            style={{ marginRight: 3 }}
          >
            {label}
          </Tag>
        )}
        value={exerciceTag}
        placeholder={t('ChooseMCQType')}
        loading={loading}
        options={exerciceTagOption}
        onChange={(checked) => {
          setExerciceTag(checked);
        }}
      />
    </div>
  );
};

export default ExerciseTagSelection;
