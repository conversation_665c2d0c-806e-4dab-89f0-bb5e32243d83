import { Button, Image, message, Upload } from 'antd';
import { DeleteTwoTone } from '@ant-design/icons';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useAiCreateAndImportContext } from '@/shared/pages/admin/qcm/components/modal/AiCreateAndImport/CreateAndImportAiContextProvider';
import { DATA_INPUT_TYPE } from '@/shared/pages/admin/qcm/components/modal/AiCreateAndImport/CreateAndImportAiContextProvider';

const UploadImages = () => {
  const { t } = useTranslation();

  const AUTHORIZED_FILE_EXTENSIONS = [
    '.jpg',
    '.jpeg',
    '.png',
    '.gif',
    '.bmp',
    '.tiff',
    '.tif',
    '.svg',
    '.webp',
    '.heif',
    '.heic'
  ];

  const { imageArrayToImport, setImageArrayToImport } = useAiCreateAndImportContext();

  const handleRemove = (file) => {
    const newArray = imageArrayToImport.filter((image) => image.uid !== file.uid);
    setImageArrayToImport(newArray);
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'row' }}>
      <Upload.Dragger
        name="file"
        showUploadList={false}
        listType="picture"
        style={{ width: '140px' }}
        height={140}
        fileList={imageArrayToImport} // Gestion de l'affichage des fichiers uploadés
        onRemove={handleRemove} // Suppression d'un fichier
        beforeUpload={(file, fileList) => {
          // Récupération de l'extension
          const extension = file?.name?.split('.')?.pop().toLowerCase();

          if (AUTHORIZED_FILE_EXTENSIONS.includes(`.${extension}`)) {
            const newFile = {
              file: file,
              url: URL.createObjectURL(file), // Génération de l'URL
              dataType: DATA_INPUT_TYPE.PICTURE
            };
            setImageArrayToImport((prev) => [...prev, newFile]);
            return false;
          } else {
            message.error(
              t('ImageToLatex.UploadExensionErrorText', {
                extension,
                validesExtension: AUTHORIZED_FILE_EXTENSIONS.join(', ')
              })
            );
            return Upload.LIST_IGNORE;
          }
        }}
      >
        <div>{t('MathpixIntegration.ChoosePictureUploaderLabel')}</div>
      </Upload.Dragger>
      <div
        style={{ flexGrow: 1, marginLeft: '10px', display: 'flex', flexWrap: 'wrap', gap: '10px' }}
      >
        {imageArrayToImport.map((imageNode, index) => (
          <div
            key={index}
            style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '5px'
            }}
          >
            <Image src={imageNode?.url} height={100} width={140} />
            <Button
              icon={<DeleteTwoTone twoToneColor={'red'} />}
              size={'small'}
              onClick={() => {
                handleRemove(imageNode);
              }}
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default UploadImages;
