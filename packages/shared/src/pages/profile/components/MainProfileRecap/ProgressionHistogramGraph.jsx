import { tr } from '@/shared/services/translate';
import React, { useEffect, useState } from 'react';
import DataSet from '@antv/data-set';
import { Chart } from '@antv/g2';
import { useTranslation } from 'react-i18next';
import { getUrlProtectedRessource, GlobalConfig } from '@/shared/utils/utils';
import { renderToString } from 'react-dom/server';
import SubjectPlaceholder from '@/shared/assets/SubjectPlaceholder.png';
import CoursPlaceholder from '@/shared/assets/CoursPlaceholder.png';
import CategoryPlaceholder from '@/shared/assets/CategoryPlaceholder.png';
import { RoundImageForGraphAndLabels } from '@/shared/pages/profile/components/RoundImageForGraphAndLabels';

const ProgressionHistogramGraph = ({ data }) => {
  const { t } = useTranslation();
  const graphId = 'progressionHistogramGraph';
  const YourAverageString = t('YourAverage');
  const GeneralAverageString = t('GeneralAverage');
  const GeneralNumberSample = t('ProgressionRadarGraph.StructureNumber');
  const YourNumberSample = t('ProgressionRadarGraph.UserNumber');
  const NULL_VALUE = 0.00001; // Important d'avoir une null value positive, car lorsque l'on passe sa sourie, si négative, on va avoir un artefact

  /// Hook
  const [graph, setGraph] = useState(null);
  const [reworkedData, setReworkedData] = useState(null);

  /// formatage des données lorsque on les changes
  useEffect(() => {
    if (data) {
      setReworkedData(extractData(data));
    } else {
      setReworkedData(null);
    }
  }, [data]);

  /// fork
  useEffect(() => {
    if (reworkedData && graph) {
      graph.destroy();
    }

    if (reworkedData) {
      setGraph(initChartHisto(reworkedData));
    }
  }, [reworkedData]);

  const extractData = (rawData) => {
    /* Fonction d'extraction des données pour les graphs Radar et Histo */

    function summariseData(e) {
      let itemName = e?.name;
      if (e?.description) {
        itemName = `${e?.[tr('name')] || e?.name}:${e?.[tr('description')] || e?.description}`;
      }

      return {
        item: itemName,
        [YourAverageString]: e?.userResume?.moyenne * 20 || NULL_VALUE,
        [GeneralAverageString]: e?.structureResume?.moyenne * 20 || NULL_VALUE,
        [GeneralNumberSample]: e?.structureResume?.nbResults || 0,
        [YourNumberSample]: e?.userResume?.nbResults || 0,
        internalId: itemName + e?.id,
        image: e?.image,
        type: e?.type,
        isFolder: e?.isFolder
      };
    }

    let formatedData = rawData.map((elem) => summariseData(elem));

    const sortedData = formatedData.sort((a, b) => {
      const diff = b[YourAverageString] - a[YourAverageString];

      if (diff === 0) {
        return a[GeneralAverageString] - b[GeneralAverageString];
      }

      return diff; // le plus élevé à gauche
    });

    return sortedData;
  };

  function getUrlFromImageAndNode(node) {
    /* permet de récupérer les images entre les placeHolders et images originales */
    let securedImageLink;
    if (node?.image) {
      securedImageLink = getUrlProtectedRessource(GlobalConfig.get().FILES_URL + node?.image);
    } else if (node?.isFolder) {
      securedImageLink = CategoryPlaceholder;
    } else if (node?.type === 'cours') {
      securedImageLink = CoursPlaceholder;
    } else if (node?.type === 'ue' || node?.type === 'category') {
      securedImageLink = SubjectPlaceholder;
    }

    return securedImageLink;
  }

  async function processAndRender(data, chart, chartType) {
    let size;
    let spaceAfterAxis;

    if (chartType === 'radar') {
      size = 26;
      spaceAfterAxis = 22;
    } else if (chartType === 'hist') {
      size = 20;
      spaceAfterAxis = -0.5;
    }

    data.forEach((item) => {
      let securedImageLink = getUrlFromImageAndNode(item);

      const imageHtml = renderToString(
        <div style={{ width: `${size}px`, height: `${size}px` }}>
          <RoundImageForGraphAndLabels src={securedImageLink} size={size} />
        </div>
      );

      chart.annotation().html({
        position: [item.internalId, spaceAfterAxis],
        html: imageHtml,
        offsetX: -(size / 2),
        offsetY: -(size / 2)
      });
    });

    chart.render();
  }

  function toolBoxCustom(title, items) {
    /* Fonction shared d'affichage de la toolbox pour les graphs */

    /// Definition des constantes
    const sharedData = items[0]?.data; // Récupération de la première node disponible pour avoir les données partagées (image, nom, etc...)
    const adjustedTitle = sharedData?.item || 'No title';
    const structureNb = sharedData?.[GeneralNumberSample] || 0;
    const userNb = sharedData?.[YourNumberSample] || 0;

    const securedImageLink = getUrlFromImageAndNode(sharedData);
    const IconBalise = renderToString(
      <RoundImageForGraphAndLabels src={securedImageLink} size={24} />
    );

    // Création de la div de toolbox
    let content = `<div style='margin: 10px'>`;

    // ajout du titre + image si présente
    content += `<div style='display: flex; align-items: center; justify-content: center; font-size: 18px; font-weight: bold;'>${adjustedTitle}&nbsp;${IconBalise}</div>`;

    // Ajout des données récupérées si il y en a
    content += '<br>';
    items.forEach((item) => {
      const value =
        item.data.score === NULL_VALUE
          ? t('ProgressionRadarGraph.NoResults')
          : item.data.score.toFixed(2);
      content += `<div>${item.name}: ${value}</div>`;
    });

    // Ajout du nombre d'exercices réalisés (il y a forcément qqc d'écrit => 0 )
    content += '<br>';
    content += `<div>${YourNumberSample}: ${userNb}</div>`;
    content += `<div>${GeneralNumberSample}: ${structureNb}</div>`;

    // Fermeture de la div englobante
    content += `</div>`;
    return content;
  }

  const initChartHisto = (data) => {
    const { DataView } = DataSet;
    const dv = new DataView().source(data);
    dv.transform({
      type: 'fold',
      fields: [YourAverageString, GeneralAverageString],
      key: 'user',
      value: 'score'
    });

    const chart = new Chart({
      container: graphId,
      autoFit: true,
      padding: [50, 50, 50, 50] // permet en display mobile d'avoir quand même un espace pour le graph, quittes à tronquer les étiquettes
    });

    chart.data(dv.rows);
    chart.scale('score', {
      min: 0,
      max: 20
    });

    // Pas besoin de coordonnées polaires pour un histogramme
    // chart.coordinate('rect');

    chart.tooltip({
      shared: true,
      customContent: toolBoxCustom
    });

    // Pour chaque data, on regarde si y a une image, et on l'affiche
    processAndRender(data, chart, 'hist');

    // OSCURRRRRRR : https://antv-g2.gitee.io/en/docs/api/general/axis et https://antv-g2.gitee.io/zh/docs/api/shape/shape-attrs
    chart.axis('internalId', {
      label: {
        style: {
          textAlign: 'start',
          textBaseline: 'top',
          fontWeight: 'bold'
        },
        rotate: Math.PI / 2,
        autoHide: false,
        formatter: (internalId) => {
          return '';
          const correspondingData = data.find((d) => d.internalId === internalId);
          if (correspondingData?.image) {
            return '';
          }
          if (correspondingData?.item) {
            return correspondingData.item.substring(0, 15) + '...';
          }
          return internalId.substring(0, 15) + '...';
        }
      },
      line: null,
      tickLine: null
    });

    chart.axis('score', {
      line: null,
      tickLine: null
    });

    chart
      .interval()
      .position('internalId*score')
      .color('user')
      .adjust([
        {
          type: 'dodge',
          marginRatio: 0.1 // espace entre les groupes de barres
        }
      ]);

    chart.render();
    return chart;
  };

  return (
    <div
      style={{
        minHeight: '475px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        flexWrap: 'wrap'
      }}
    >
      <div style={{ width: '100%', minHeight: '475px', marginBottom: '5px' }} id={graphId} />
    </div>
  );
};

export default React.memo(ProgressionHistogramGraph);
