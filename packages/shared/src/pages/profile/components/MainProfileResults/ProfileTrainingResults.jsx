import { GET_SESSIONS_HISTORY } from '@/shared/graphql/qcm.js';
import { isParent } from '@/shared/utils/authority.js';
import { DATE_FORMATS } from '@/shared/utils/utils.js';
import { useQuery } from '@apollo/client';
import { Button, Tag, Table, Space } from 'antd';
import dayjs from 'dayjs';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { router } from 'umi';

export function ProfileTrainingResults({ userId = null }) {
  const { t } = useTranslation();

  const isParentUser = isParent();

  /* Query sessions with filter */
  const { loading, error, data, refetch } = useQuery(GET_SESSIONS_HISTORY, {
    fetchPolicy: 'no-cache',
    variables: {
      filter: {
        userId
        //isFinished: true,
      }
    }
  });
  const sessions = data?.sessionsHistory || [];

  const columns = [
    {
      title: t('SerieTitle'),
      dataIndex: 'titre',
      key: 'titre',
      render: (_, session) => {
        return (
          <>
            <Space>
              <Tag>
                {session?.questionsIdsDone.length} {t('Exercices')}
              </Tag>
              {session?.isFinished ? (
                <Tag color={'green'}>{t('general.done')}</Tag>
              ) : (
                <Tag color={'orange'}>{t('Unfinished')}</Tag>
              )}
            </Space>
            Entraînement généré
          </>
        );
      }
    },
    {
      title: t('DateOfCompletion'),
      dataIndex: 'updatedAt',
      key: 'date',
      sorter: (a, b) => dayjs(a.updatedAt) - dayjs(b.updatedAt),
      render: (updatedAt, record) => <>{dayjs(updatedAt).format(DATE_FORMATS.FULL_FR)}</>
    },
    {
      title: t('general.Grade'),
      dataIndex: 'result',
      key: 'result',
      //sorter: (a, b) => a[getUserResultIndex()].note - b[getUserResultIndex()].note,
      render: (result, session) => {
        if (result?.note) {
          return (
            <span style={{ color: '#1473cc' }}>{`${result.note} / ${session.maxPoints}`}</span>
          );
        } else {
          return 'Non terminé';
        }
      }
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, session) => {
        const finished = session?.isFinished;
        return (
          <Button
            disabled={isParentUser}
            onClick={() => {
              if (finished) {
                // If it's my MCQ done
                router.push(`/qcm/correction-session/${session.id}`);
              } else {
                // If it's my MCQ not done
                router.push(`/generateurqcm/do/${session.id}`);
              }
            }}
          >
            {finished ? t('Correction') : t('general.Continue')}
          </Button>
        );
      }
    }
  ];

  return (
    <div style={{ margin: '24 0' }}>
      <Table
        loading={loading}
        dataSource={sessions} // userQcmFaits
        columns={columns}
        scroll={{ x: true }}
        pagination={false}
      />
    </div>
  );
}
