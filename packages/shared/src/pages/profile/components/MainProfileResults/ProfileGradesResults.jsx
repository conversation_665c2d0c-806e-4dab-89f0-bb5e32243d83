import { SpinnerCentered } from '@/shared/components/PageLoading/SpinnerCentered.jsx';
import { ExerciseRankResult } from '@/shared/components/Progression/components/ExerciseRankResult.jsx';
import { QUERY_MES_UES_LIGHT } from '@/shared/graphql/cours.js';
import {
  QUERY_EXERCICE_RESULT_BY_UE_FOR_ME,
  QUERY_EXERCICE_RESULT_BY_UE_FOR_USER
} from '@/shared/graphql/progression.js';
import { QUERY_ALL_QCM_TYPE } from '@/shared/graphql/qcm.js';
import { CONTENT_TYPE_VALUES } from '@/shared/pages/admin/qcm/types-qcm';
import { renderIcon } from '@/shared/pages/qcm/$index$.jsx';
import {
  getLastSelectedUE,
  getMaxPointsFromQcm,
  prettyPrintSeconds,
  setLastSelectedUE
} from '@/shared/services/qcm.js';
import {
  missingTranslation,
  renderUENameAndDescriptionWithFallback,
  tr
} from '@/shared/services/translate.js';
import { DATE_FORMATS, showGqlErrorsInMessagePopupFromException } from '@/shared/utils/utils.js';
import { useQuery } from '@apollo/client';
import { Button, Card, Checkbox, DatePicker, Form, Select, Space, Tag, Tooltip } from 'antd';
import frFR from 'antd/es/locale/fr_FR.js';
import Table from 'antd/es/table';
import dayjs from 'dayjs';
import React, { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'umi';
import router from 'umi/router.js';
import { isParent } from '@/shared/utils/authority';

export const renderNote = (note, qcm, isBold = false) => {
  let maxPoints = getMaxPointsFromQcm(qcm);
  if (qcm?.questionPickingStrategy === 'smart') {
    maxPoints = 30;
  }
  const noteSur20 = ((note * 20) / maxPoints)?.toFixed(2);
  return (
    <>
      <Tooltip
        title={
          <span style={{ fontWeight: isBold ? '600' : '400' }}>
            {note} / {maxPoints}
          </span>
        }
      >
        <span style={{ fontWeight: isBold ? '600' : '400' }}>{noteSur20} / 20</span>
      </Tooltip>
    </>
  );
};

/* Relevé de note - Transcript - for userId or requester user */
export function ProfileGradesResults({ userId = null }) {
  const { t } = useTranslation();
  const [ueIdSelected, setUeIdSelected] = useState(getLastSelectedUE());
  /* Serie type choice */
  const [choixTypeQcmValue, setChoixTypeQcmValue] = useState(); // Names

  const [choixTypesQcm, setChoixTypeQcm] = useState([]); // IDS
  const [allTheTime, setAllTheTime] = useState(true);

  const [dateBegin, setDateBegin] = useState(dayjs().subtract(1, 'year').toDate());
  const [dateEnd, setDateEnd] = useState(dayjs().add(1, 'day').toDate());

  const getQueryHistorique = (userId) => {
    if (userId) {
      return QUERY_EXERCICE_RESULT_BY_UE_FOR_USER;
    }
    return QUERY_EXERCICE_RESULT_BY_UE_FOR_ME;
  };

  /* Query UEs only */
  const {
    loading: loadingUEs,
    error: errorUEs,
    data: dataUEs
  } = useQuery(QUERY_MES_UES_LIGHT, {
    fetchPolicy: 'cache-and-network',
    variables: {
      forUser: userId
    }
  });
  const ueData = dataUEs?.mesUEs?.filter((ue) => !ue?.isFolder);

  /* Query ue with transcript datas */
  const { loading, error, data, refetch } = useQuery(getQueryHistorique(userId), {
    fetchPolicy: 'no-cache',
    skip: !ueIdSelected,
    variables: {
      id: parseInt(ueIdSelected, 10),
      ...(userId && { userId }),
      filter: {
        qcmSeriesTypes: choixTypesQcm,
        dateBegin: !allTheTime ? dateBegin : null,
        dateEnd: !allTheTime ? dateEnd : null
      }
    }
  });

  const currentUEWithUserResults = data?.ue;

  const dataTypeQcm = useQuery(QUERY_ALL_QCM_TYPE, {
    fetchPolicy: 'cache-and-network',
    variables: {
      forUser: userId
    }
  });
  const typeQcmSeries = useMemo(
    () =>
      dataTypeQcm?.data?.allTypeQcm?.filter(
        (ty) => ty.contentType === CONTENT_TYPE_VALUES.EXERCISE_SERIES
      ),
    [dataTypeQcm]
  );

  useEffect(() => {
    if (dataTypeQcm && typeQcmSeries) {
      setChoixTypeQcmValue(typeQcmSeries?.map((ty) => ty?.name));
      setChoixTypeQcm(typeQcmSeries?.map((ty) => ty?.id));
    }
  }, [dataTypeQcm]);

  const getUserResultIndex = () => {
    if (userId) {
      return 'statistiques';
    }
    return 'resultat';
  };
  const isParentUser = isParent();

  const columns = [
    {
      title: t('SerieTitle'),
      dataIndex: 'titre',
      key: 'titre',
      render: (titre, qcm) => (
        <>
          {qcm?.type?.map((type, index) => (
            <Tag style={{ marginTop: '10px' }} color="geekblue" key={index}>
              {index >= 1 && ' '} {type?.name}
            </Tag>
          ))}
          <br />
          <Link to={isParentUser ? '#' : `/qcm/${qcm.id_qcm}`}>
            <b>{qcm?.['titre'] || titre}</b>
          </Link>
        </>
      )
    },
    {
      title: t('DateOfCompletion'),
      dataIndex: getUserResultIndex(),
      key: 'date',
      sorter: (a, b) => dayjs(a[getUserResultIndex()].date) - dayjs(b[getUserResultIndex()].date),
      render: ({ date }, record) => <>{dayjs(date).format(DATE_FORMATS.FULL_FR)}</>
    },
    {
      title: t('general.Rank'),
      dataIndex: getUserResultIndex(),
      key: 'classement',
      //sorter: (a, b) => dayjs(a[getUserResultIndex()].date) - dayjs(b[getUserResultIndex()].date),
      render: (_, qcm) => (
        <>
          <ExerciseRankResult userId={userId} qcmId={qcm?.id_qcm} />
          {/*
          {classement} / {qcm[getUserResultIndex()]?.count}
          */}
        </>
      )
    },
    {
      title: t('general.Grade'),
      dataIndex: getUserResultIndex(),
      key: 'note',
      sorter: (a, b) => a[getUserResultIndex()].note - b[getUserResultIndex()].note,
      render: ({ note }, qcm) => (
        <span style={{ color: '#1473cc' }}>
          {qcm?.correctionConfig?.showNote ? renderNote(note, qcm, true) : 'Masqué'}
        </span>
      )
    },
    {
      title: t('general.Average'),
      dataIndex: getUserResultIndex(),
      key: 'moyenne',
      sorter: (a, b) => a[getUserResultIndex()].moyenne - b[getUserResultIndex()].moyenne,
      render: ({ moyenne }, qcm) =>
        qcm?.correctionConfig?.showMoyenne ? renderNote(moyenne, qcm) : 'Masqué'
    },
    {
      title: t('Duration'),
      dataIndex: getUserResultIndex(),
      key: 'seconds',
      sorter: (a, b) => a[getUserResultIndex()].seconds - b[getUserResultIndex()].seconds,
      render: ({ seconds }, qcm) => (seconds ? prettyPrintSeconds(seconds) : '/')
    },

    {
      title: t('MinGrade'),
      dataIndex: getUserResultIndex(),
      key: 'minGrade',
      sorter: (a, b) => a[getUserResultIndex()].minGrade - b[getUserResultIndex()].minGrade,
      render: ({ minGrade = null }, qcm) => (minGrade !== null ? renderNote(minGrade, qcm) : '')
    },
    {
      title: t('MaxGrade'),
      dataIndex: getUserResultIndex(),
      key: 'maxGrade',
      sorter: (a, b) => a[getUserResultIndex()].maxGrade - b[getUserResultIndex()].maxGrade,
      render: ({ maxGrade = null }, qcm) => (maxGrade !== null ? renderNote(maxGrade, qcm) : '')
    },

    {
      title: 'Actions',
      dataIndex: getUserResultIndex(),
      key: 'actions',
      render: (_, qcm) => {
        return (
          <Button
            disabled={isParentUser}
            onClick={() => {
              if (qcm?.mySession) {
                // If it's my MCQ done
                router.push(`/qcm/correction/${qcm.id_qcm}/session/${qcm?.mySession?.id}`);
              } else {
                // If it's not my MCQ done
                const sessionId = qcm?.statistiques?.qcmSessionId;
                if (sessionId) {
                  router.push(`/qcm/correction/${qcm.id_qcm}/user/${userId}/session/${sessionId}`);
                } else {
                  if (userId) {
                    router.push(`/qcm/correction/${qcm.id_qcm}/stat/${qcm?.statistiques?.id}`);
                  } else {
                    router.push(`/qcm/correction/${qcm.id_qcm}`);
                  }
                }
              }
            }}
          >
            {t('Correction')}
          </Button>
        );
      }
    }
  ];

  const getQcmFaits = (ue) => {
    if (!ue) {
      return [];
    }
    if (userId && ue.userQcmFaits) {
      return ue.userQcmFaits;
    }
    return ue.qcmFaits;
  };

  const handleChangeExerciseSerieType = (checked, option) => {
    setChoixTypeQcm(option.map((o) => o.key));
    setChoixTypeQcmValue(option.map((o) => o.value));
  };

  const typeSelection = (
    <div>
      <h3>{t('MCQType')}</h3>
      <Select
        showArrow
        mode="multiple"
        size="large"
        style={{ width: 300 }}
        tagRender={({ label, value, closable, onClose, key }) => (
          <Tag
            value={value}
            key={key}
            color="geekblue"
            closable={closable}
            onClose={onClose}
            style={{ marginRight: 3 }}
          >
            {label}
          </Tag>
        )}
        value={choixTypeQcmValue}
        placeholder={t('ChooseMCQType')}
        loading={dataTypeQcm?.loading}
        options={typeQcmSeries?.map((typeQcm) => ({ value: typeQcm.name, key: typeQcm.id }))}
        onChange={handleChangeExerciseSerieType}
      />
    </div>
  );

  const periodSelection = (
    <div>
      <h3>
        <b>{t('period')}</b>
      </h3>
      <Space>
        <Checkbox checked={allTheTime} onChange={() => setAllTheTime(!allTheTime)}>
          {t('All')}
        </Checkbox>
        <>
          {!allTheTime && (
            <Form>
              <Space
                style={{ display: 'flex', marginBottom: 8 }}
                align="start"
                direction="horizontal"
              >
                <Form.Item label={t('BeginDate')} style={{ minWidth: '221px' }}>
                  <DatePicker
                    value={dayjs(dateBegin)}
                    locale={frFR}
                    style={{ width: '221px' }}
                    showTime={{ format: 'HH:mm' }}
                    format={DATE_FORMATS.FULL_FR}
                    onChange={async (value) => {
                      try {
                        setDateBegin(value.toDate()); // ou pas toDate ?
                      } catch (e) {
                        showGqlErrorsInMessagePopupFromException(e);
                      }
                    }}
                  />
                </Form.Item>
                <Form.Item label={t('EndDate')} style={{ minWidth: '221px' }}>
                  <DatePicker
                    value={dayjs(dateEnd)}
                    locale={frFR}
                    disabledDate={(current) => {
                      return current && current < dayjs(dateBegin);
                    }}
                    style={{ width: '221px' }}
                    showTime={{ format: 'HH:mm' }}
                    format={DATE_FORMATS.FULL_FR}
                    onChange={async (value) => {
                      setDateEnd(value?.toDate());
                    }}
                  />
                </Form.Item>
              </Space>
            </Form>
          )}
        </>
      </Space>
    </div>
  );

  const subjectSelection = (
    <div style={{ minWidth: '300px' }}>
      <h3>{t('ChooseASubject')}</h3>
      {loadingUEs ? (
        <SpinnerCentered />
      ) : (
        <Select
          size="large"
          style={{ width: '100%' }}
          optionFilterProp="children"
          placeholder={t('MySubjects')}
          defaultValue={ueIdSelected}
          onChange={(value) => {
            setUeIdSelected(value);
            setLastSelectedUE(value);
          }}
        >
          {ueData?.map((ue, key) => (
            <Select.Option value={ue?.id} key={ue?.id}>
              <div
                style={{ height: '30px', fontSize: '14px', display: 'flex', alignItems: 'center' }}
              >
                <div style={{ height: '30px' }}>{ue?.image && renderIcon(ue?.image)}</div>
                {renderUENameAndDescriptionWithFallback(ue)}
              </div>
            </Select.Option>
          ))}
        </Select>
      )}
    </div>
  );

  return (
    <div>
      <div>
        <div
          style={{
            display: 'flex',
            flexWrap: 'wrap',
            justifyContent: 'center',
            alignItems: 'center',
            gap: '24px',
            marginTop: '10px'
          }}
        >
          {subjectSelection}
          {typeSelection}
          {periodSelection}
        </div>
      </div>
      <br />

      {ueIdSelected ? (
        <>
          <Table
            loading={loading}
            key={ueIdSelected}
            dataSource={getQcmFaits(currentUEWithUserResults)} // userQcmFaits
            columns={columns}
            scroll={{ x: true }}
            pagination={false}
          />
        </>
      ) : (
        <>
          <p>{t('ChooseASubject')}</p>
        </>
      )}
    </div>
  );
}
