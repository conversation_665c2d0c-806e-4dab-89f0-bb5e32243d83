import { GET_CONFIG } from '@/shared/graphql/home.js';
import { GET_MY_GROUPS } from '@/shared/graphql/user';
import { UpdateForfait } from '@/shared/pages/account/forfait/UpdateForfait.jsx';
import { CONFIG_KEYS, DEFAULT_CONFIG_VARIABLES, getPublicSrc } from '@/shared/services/config.js';
import { getValueFromKeyConfigData } from '@/shared/services/home.js';
import { useEffectScrollTop } from '@/shared/utils/hooks/useEffectScrollTop.js';
import { useQuery } from '@apollo/client';
import { Layout } from 'antd';
import React from 'react';

export default function(props) {
  const { customLink } = props.match.params;
  useEffectScrollTop();
  const { data, loading, refetch, error } = useQuery(GET_MY_GROUPS, { fetchPolicy: 'no-cache' });
  const mesGroupes = data && data.me && data.me.groups;
  const mesCredits = data && data.me && data.me.credits;

  const {
    loading: loadingConfig,
    error: errorConfig,
    data: dataConfig,
    refetch: refetchConfig,
  } = useQuery(GET_CONFIG, { fetchPolicy: 'cache-and-network', ...DEFAULT_CONFIG_VARIABLES });
  const config = dataConfig?.config;
  const webSiteName = getValueFromKeyConfigData(config, CONFIG_KEYS.WEBSITE_NAME);
  const logoInMenuBar = getValueFromKeyConfigData(config, CONFIG_KEYS.LOGO_MENUBAR);

  return (
    <Layout>
      {/* Platform logo */}
      <div style={{textAlign: 'center', marginTop: '24px', marginBottom: '40px'}}>
        <img src={getPublicSrc(logoInMenuBar)} alt={'logo'} style={{maxWidth: '64px'}}/>
      </div>
      <br />
      &nbsp;
      {customLink && (
        <UpdateForfait withRegisterForm={!mesGroupes} withCustomLink customLink={customLink} />
      )}
      <Layout.Footer></Layout.Footer>
    </Layout>
  );
}
