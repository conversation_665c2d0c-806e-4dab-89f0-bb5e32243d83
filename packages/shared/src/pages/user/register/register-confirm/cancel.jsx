import { <PERSON><PERSON>, <PERSON>, Col, Layout, Result, Row } from 'antd'
import React from 'react'
import router from 'umi/router'
import { useTranslation } from 'react-i18next';

export default function() {
  const {t} = useTranslation();
  return (
    <Layout>
      <br/>&nbsp;
      <Row justify="center" type="flex" key="1">
        <Col xl={12} lg={12} md={12} sm={22} xs={22}>
          <Card>
            <Result status={'error'} title={'Inscription annulée'}>
              {t('WereNotDebited')}
            </Result>
            <br/>
          </Card>
        </Col>
      </Row>
    </Layout>

  )
}