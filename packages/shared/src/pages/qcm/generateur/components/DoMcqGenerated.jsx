import { GET_CURRENT_SESSION_DETAILS, MUT_GET_NOTE_QCM_GENERATED } from '@/shared/graphql/qcm.js';
import { ChronometreModal } from '@/shared/pages/qcm/components/ChronometreModal.jsx';
import { FloatingTimer } from '@/shared/pages/qcm/components/FloatingTimer.jsx';
import { QuestionWithAnswers } from '@/shared/pages/qcm/components/QcmQuestion.jsx';
import { DoMcqContext } from '@/shared/pages/qcm/context/DoMcqContext.jsx';
import {
  ModalTimesUp,
  ModalTimesUpTypes
} from '@/shared/pages/qcm/doMcq/components/ModalTimesUp.jsx';
import { DoQuestionInSession } from '@/shared/pages/qcm/doMcq/DoQuestionInSession.jsx';
import { DoAllQuestionsInSessionAtOnce } from '@/shared/pages/qcm/generateur/components/DoAllQuestionsInSessionAtOnce.jsx';
import { mapFormToQuestionsModel_V2, setChoixGeneratedQCMData } from '@/shared/services/qcm.js';
import { showGqlErrorsInMessagePopupFromException } from '@/shared/utils/utils.js';
import { useMutation, useQuery } from '@apollo/client';
import { Button, Col, Form, Row } from 'antd';
import React, { useContext, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { router } from 'umi';

export const DoMcqGenerated = ({
  getGlobalTitle,
  coursName,
  getQuestions,
  qcmSessionId,
  contentRef,
  generatedQcm
}) => {
  const { t } = useTranslation();
  const { userChoicesByQuestion } = useContext(DoMcqContext);
  const [isChronometre, setIsChronometre] = useState(false);
  const [timerQuestions, setTimerQuestions] = useState();
  const [isChronometreModalVisible, setChronometreModalVisible] = useState(false);
  const [corrigerQcmGenerated, { loading: loadingMut }] = useMutation(MUT_GET_NOTE_QCM_GENERATED);
  const [modalTimesUpVisible, setModalTimesUpVisible] = useState(false);

  const onFinishTimer = async () => {
    // Mandatory to force submit
    setModalTimesUpVisible(true);
    setIsChronometre(false); // hide floating timer
  };

  useEffect(() => {
    if (generatedQcm) {
      setChronometreModalVisible(generatedQcm?.hasTimer);
      setTimerQuestions(generatedQcm?.secondsPerExercise);
    }
  }, [generatedQcm]);

  const [form] = Form.useForm();

  // TODO: legacy old generator system will be removed
  const handleFinishQcm = (values) => {
    try {
      const reponses = mapFormToQuestionsModel_V2(values, userChoicesByQuestion);
      setChoixGeneratedQCMData(reponses);
      corrigerQcmGenerated({
        variables: {
          questions: getQuestions(),
          reponses
        }
      }).then((result) => {
        const {
          data: {
            corrigerQcmGenerated: { note, maxPoints }
          }
        } = result;
        sessionStorage.setItem('noteQcmGenerated', note);
        sessionStorage.setItem('maxPointsQcmGenerated', maxPoints);
        router.push(`/generateurqcm/result/`);
      });
    } catch (e) {
      console.error(e);
      showGqlErrorsInMessagePopupFromException(e);
    }
  };

  // TODO: legacy old generator system will be removed
  const renderQuestions = () => (
    <Form onFinish={handleFinishQcm} form={form}>
      {getQuestions().map((question, key) => (
        <QuestionWithAnswers
          question={question}
          key={question.id}
          qcmType={[]}
          index={key}
          showYear
          randomizeQuestionsAnswers={generatedQcm?.isAnswerOrderRandom}
          form={form}
          refetch={() => {
            window.location.reload();
          }}
        />
      ))}

      <br />

      <Form.Item style={{ textAlign: 'center' }}>
        <Button htmlType="submit" type="primary" size="large" loading={loadingMut}>
          {t('Correction')}
        </Button>
      </Form.Item>
      <br />
    </Form>
  );

  // TODO: legacy old generator system will be removed
  const renderQcmHeader = () => (
    <div>
      <div>
        <h3>{getGlobalTitle()}</h3>
        <h5>
          {getQuestions().length} {t('general.questions')}
        </h5>
      </div>
      <ModalTimesUp
        isVisible={modalTimesUpVisible}
        type={ModalTimesUpTypes.CLASSIC}
        onFinishOrCancel={() => {
          form.submit();
        }}
      />
      <ChronometreModal
        isVisible={isChronometreModalVisible}
        closeModalHandler={() => {
          setChronometreModalVisible(false);
        }}
        isChronometre={false}
        launchChrono={() => {
          setChronometreModalVisible(false);
          setTimerQuestions(timerQuestions * parseInt(getQuestions().length, 10));
          setIsChronometre(true);
        }}
      />
      {isChronometre && (
        <FloatingTimer
          timerDelay={timerQuestions}
          shouldStartTimer={isChronometre}
          onFinishTimer={onFinishTimer}
          handleSavingCurrentSelectionAndTimer={() => {
            // Do nothing
          }}
        />
      )}
    </div>
  );

  /* Query current session data to know session type */
  const {
    loading: loadingSession,
    error: errorSession,
    data: dataSession,
    refetch: refetchCurrentSession
  } = useQuery(GET_CURRENT_SESSION_DETAILS, {
    fetchPolicy: 'cache-and-network',
    variables: { id: qcmSessionId },
    skip: !qcmSessionId
  });
  // MCQ current session
  const session = dataSession?.sessionQcm;
  const infiniteQuestionByQuestion = session?.settings?.infiniteQuestionByQuestion === true;

  return (
    <>
      {/* If we have qcmSessionId and infiniteQuestionByQuestion we launch one by one (DoQuestionInSession) */}
      {qcmSessionId && infiniteQuestionByQuestion ? (
        <DoQuestionInSession
          sessionId={qcmSessionId}
          correctionInSamePage
          setCurrentSessionId={() => {}}
          withIgnoreButton
          withFinishButton
        />
      ) : (
        <>
          {/* In a session but all exercices at once */}
          {qcmSessionId && !infiniteQuestionByQuestion ? (
            <DoAllQuestionsInSessionAtOnce
              qcmSessionId={qcmSessionId}
              contentRef={contentRef}
              form={form}
            />
          ) : (
            <Row justify="center" key="1" ref={contentRef}>
              <Col xl={16} lg={20} md={24} sm={24} xs={24}>
                {getQuestions() && renderQcmHeader()}
                <br />
                {/* LEGACY generator */}
                {getQuestions() && renderQuestions()}
              </Col>
            </Row>
          )}
        </>
      )}
    </>
  );
};
