import ExoteachLayout from '@/shared/components/ExoteachLayout.jsx';
import { ExerciseGeneratorChoice } from '@/shared/pages/qcm/generateur/components/ExerciseGeneratorChoice.jsx';
import { setGeneratedQCMData } from '@/shared/services/qcm.js';
import { useEffectScrollTop } from '@/shared/utils/hooks/useEffectScrollTop.js';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { router } from 'umi';

export function McqGenerator() {
  useEffectScrollTop();
  const { t } = useTranslation();
  return (
    <React.Fragment>
      <ExoteachLayout>
        <div>
          <ExerciseGeneratorChoice
            onGetQuestions={async (generatedResult) => {
              if (generatedResult.qcmSessionId) {
                setGeneratedQCMData(generatedResult);
                router.push(`/generateurqcm/do/${generatedResult.qcmSessionId}`);
              } else {
                setGeneratedQCMData(generatedResult);
                router.push('/generateurqcm/do');
              }
            }}
          />
        </div>
      </ExoteachLayout>
    </React.Fragment>
  );
}
