import categoryPlaceHolder from '@/shared/assets/CategoryPlaceholder.svg';
import subjectPlaceholder from '@/shared/assets/SubjectPlaceholder.svg';
import { FadeTransition } from '@/shared/assets/transitions/FadeTransition';
import ExoteachLayout from '@/shared/components/ExoteachLayout.jsx';
import FullMediParticlesBreadCrumb from '@/shared/components/FullMediParticlesBreadCrumb';
import useMediaQuery from '@/shared/hooks/useMediaQuery';
import { McqTypeSelector } from '@/shared/pages/qcm/components/McqTypeSelector/McqTypeSelector';
import { PremadeMcqSearch } from '@/shared/pages/qcm/components/PremadeMcqSearch/PremadeMcqSearch';
import { McqGenerator } from '@/shared/pages/qcm/generateur/McqGenerator';
import { getUrlProtectedRessource, GlobalConfig, isMobile } from '@/shared/utils/utils.js';
import { AppstoreOutlined, BuildOutlined } from '@ant-design/icons';
import { Segmented } from 'antd';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

// TODO share
export const renderIcon = (image, type = null, isFolder = false, style = {}) => {
  let defaultImage = null;
  if (type) {
    if (type === 'ue') {
      defaultImage = subjectPlaceholder;
      if (isFolder) {
        defaultImage = categoryPlaceHolder;
      }
    } else if (type === 'category') {
      defaultImage = categoryPlaceHolder;
    }
  }
  return (
    <img
      src={image ? getUrlProtectedRessource(GlobalConfig.get().FILES_URL + image) : defaultImage}
      style={{ maxWidth: '22px', marginRight: '5px', ...style }}
      alt=""
    />
  );
};

export default function (props) {
  const [selectedExerciceType, setSelectedExerciceType] = useState('premade');
  const { t } = useTranslation();

  const isSmallScreen = useMediaQuery('(max-width: 539px)');

  const ueId = props.match.params.ue;
  const sousCategorieId = props.match.params.categorie;
  //const urlMcqId = props.match.params.qcm;

  const [selectedMcqId, setSelectedMcqId] = useState(null);

  return (
    <>
      <FullMediParticlesBreadCrumb title={t('ExercisesSeries')} textCenter />

      {isMobile || isSmallScreen ? (
        <div
          style={{
            width: '100%',
            margin: '24px 0 12px 0',
            display: 'flex',
            justifyContent: 'center'
          }}
        >
          <Segmented
            style={{ width: 'fit-content' }}
            options={[
              {
                label: (
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      maxWidth: 150,
                      flexDirection: 'column',
                      justifyContent: 'center',
                      gap: 4
                    }}
                  >
                    <AppstoreOutlined style={{ fontSize: 40 }} />
                    <span style={{ textWrap: 'wrap' }}>{t('PremadeTraining')}</span>
                  </div>
                ),
                value: 'premade'
              },
              {
                label: (
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      maxWidth: 150,
                      flexDirection: 'column',
                      justifyContent: 'center',
                      gap: 4
                    }}
                  >
                    <BuildOutlined style={{ fontSize: 40 }} />
                    <span style={{ textWrap: 'wrap' }}>{t('Personalized')}</span>
                  </div>
                ),
                value: 'personalized'
              }
            ]}
            value={selectedExerciceType}
            onChange={setSelectedExerciceType}
          />
        </div>
      ) : (
        <McqTypeSelector
          selectedExerciceType={selectedExerciceType}
          setSelectedExerciceType={setSelectedExerciceType}
        />
      )}

      <ExoteachLayout>
        <FadeTransition displayCondition={selectedExerciceType === 'premade'}>
          <PremadeMcqSearch
            sousCategorieId={sousCategorieId}
            ueId={ueId}
            selectedMcqId={selectedMcqId}
            setSelectedMcqId={setSelectedMcqId}
          />
        </FadeTransition>
        <FadeTransition displayCondition={selectedExerciceType === 'personalized'}>
          <McqGenerator />
        </FadeTransition>
      </ExoteachLayout>
    </>
  );
}
