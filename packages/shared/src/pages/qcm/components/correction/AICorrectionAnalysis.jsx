import { QUERY_AI_ANALYSIS_CORRECTION } from '@/shared/graphql/qcm';
import { isAdmin } from '@/shared/utils/authority';
import { displayDirectHtml } from '@/shared/utils/utils';
import { ReloadOutlined } from '@ant-design/icons';
import { useQuery } from '@apollo/client';
import React from 'react';
import { Button, Card, Typography } from 'antd';
import { useTranslation } from 'react-i18next';

export function AICorrectionAnalysis({ qcmId, userId, sessionId, statId }) {
  const { t } = useTranslation();

  // QUERY
  const {
    loading,
    data: { getAiAnalysis = null } = {},
    error,
    refetch
  } = useQuery(QUERY_AI_ANALYSIS_CORRECTION, {
    fetchPolicy: 'no-cache',
    notifyOnNetworkStatusChange: true,
    variables: {
      qcmId,
      userId,
      sessionId,
      statId
    },
    skip: !qcmId
  });

  const cardStyleStrongPoints = {
    backgroundColor: loading ? '#00FF0010' : 'auto',
    flex: 1,
    minWidth: 326,
    display: 'flex',
    alignItems: 'center'
    //paddingLeft: 10
  };
  const cardStyleWeakPoints = {
    backgroundColor: loading ? '#FF000010' : 'auto',
    flex: 1,
    minWidth: 326,
    display: 'flex',
    alignItems: 'center'
    //paddingLeft: 10
  };

  if (!loading && getAiAnalysis?.synthesis === undefined) {
    return null; // No AI analysis
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: 24 }}>
      <Typography.Title level={3}>
        {t('AIAnalysis')}&nbsp;
        {isAdmin() && (
          <Button
            type="text"
            loading={loading}
            shape="circle"
            onClick={() =>
              refetch({
                qcmId,
                userId,
                sessionId,
                statId,
                force: true
              })
            }
          >
            <ReloadOutlined />
          </Button>
        )}
      </Typography.Title>
      <Card
        style={{ backgroundColor: '#0000AA10', minWidth: 326 }}
        loading={loading && !getAiAnalysis}
      >
        {displayDirectHtml(getAiAnalysis?.synthesis)}
      </Card>

      <div style={{ display: 'flex', gap: 24, flexWrap: 'wrap' }}>
        <Card
          style={cardStyleStrongPoints}
          loading={loading && !getAiAnalysis}
          bodyStyle={{
            paddingLeft: 1
          }}
        >
          {displayDirectHtml(getAiAnalysis?.strengths)}
        </Card>
        <Card
          style={cardStyleWeakPoints}
          loading={loading && !getAiAnalysis}
          bodyStyle={{
            paddingLeft: 1
          }}
        >
          {displayDirectHtml(getAiAnalysis?.weaknesses)}
        </Card>
      </div>
    </div>
  );
}
