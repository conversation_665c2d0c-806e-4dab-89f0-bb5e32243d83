import ErrorBoundaryExerciseCorrection from '@/shared/pages/qcm/components/error/ErrorBoundaryExerciseCorrection.jsx';
import { CorrectionQuestionWithAnswers } from '@/shared/pages/qcm/components/QcmQuestion.jsx';
import { getQcmResultsGeneralQcm, mapQuestionsModelToForm } from '@/shared/services/qcm.js';
import { useMathJaxScript } from '@/shared/utils/hooks/useScript.js';
import { IS_DEV } from '@/shared/utils/utils.js';
import { DownOutlined, MenuOutlined } from '@ant-design/icons';
import { Button, Card, Dropdown, Form, Menu } from 'antd';
import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';

export default function ({
  qcm,
  questions,
  hasRedoneMCQ, // legacy, should be always false
  fontSize = 14,
  lineHeight = 1.5,
  setTargetOffset,
  isInSession,
  printable = false, // if true, the correction is displayed in a printable format
  showTags = true,
  refetch,
  showEditionButton = false,
  showCardWrapper = true
}) {
  const [form] = Form.useForm();
  const { t } = useTranslation();
  const getQcm = () => qcm;
  const asDejaFaitQcm = () => qcm?.resultat && qcm?.resultat?.note !== null;
  const inExerciseSerie = !!qcm;

  if (IS_DEV) {
    console.log({ questions });
  }

  const launchMathJax = useMathJaxScript();

  const mapMyAnswers = () => {
    if (hasRedoneMCQ && !isInSession) {
      // Il a déjà fait QCM, récupère dernières réponses (qcm normaux)
      return getQcmResultsGeneralQcm();
    }
    return mapQuestionsModelToForm(questions);
  };

  useEffect(() => {
    if (questions && questions.length > 0) {
      launchMathJax();
    }
  }, [questions]);

  useEffect(() => {
    if (questions) {
      form.setFieldsValue(mapMyAnswers());
      setTargetOffset(window.innerHeight / 6);
    }
  }, [form, hasRedoneMCQ, questions]);

  const content = (
    <Form form={form} initialValues={(asDejaFaitQcm() && mapMyAnswers()) || undefined} size="large">
      {questions?.map((question, key) => (
        <ErrorBoundaryExerciseCorrection
          key={question.id_question}
          idQuestion={question.id_question}
        >
          <CorrectionQuestionWithAnswers
            question={question}
            showYear={!inExerciseSerie}
            key={question.id_question}
            qcmType={getQcm()?.type}
            index={key}
            userAnswersRaw={mapMyAnswers()[question.id_question] || []}
            hasRedoneMCQ={hasRedoneMCQ}
            printable={printable}
            showTags={showTags}
            refetch={refetch}
            showEditionButton={showEditionButton}
          />
        </ErrorBoundaryExerciseCorrection>
      ))}
    </Form>
  );
  return showCardWrapper ? (
    <Card
      title={printable ? null : 'Correction'}
      bodyStyle={{ padding: 0 }}
      extra={
        !printable && [
          <Dropdown
            key="1"
            overlay={() => {
              return (
                <Menu
                  onClick={(e) => {
                    // e.key
                    // setQuestionFiltered(QuestionOrder.DEFAULT)
                  }}
                >
                  <Menu.Item key="1">Par défaut</Menu.Item>
                </Menu>
              );
            }}
            icon={<MenuOutlined />}
          >
            <Button icon={<DownOutlined />}>Ordre</Button>
          </Dropdown>
        ]
      }
    >
      {content}
    </Card>
  ) : (
    content
  );
}
