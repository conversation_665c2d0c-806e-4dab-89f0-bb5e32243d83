import AbstractGroupsManager from '@/shared/pages/admin/permissions/AbstractGroupsManager';
import IndividualPermissionsManager from '@/shared/pages/admin/permissions/IndividualPermissionsManager';
import { isAdmin, isTuteur, isUser } from '@/shared/utils/authority';
import React, { useContext } from 'react';
import { Button, Divider, Popover, Typography } from 'antd';
import { useTranslation } from 'react-i18next';
import { McqSingleStat } from '@/shared/pages/qcm/components/McqStats/McqSingleStat';
import { getMaxPointsFromQcm } from '@/shared/services/qcm';
import { Cog, Hourglass, LockKeyhole } from 'lucide-react';
import { GlobalContext } from '@/shared/layouts/BlankLayout';

export function ComparisonStatistics({
  serie,
  gradeOutOf,
  dataRanking,
  setSelectedGroupIds,
  selectedGroupIds,
  stats,
  canSeeAverage,
  canSeeGrade
}) {
  const { t } = useTranslation();

  const { appearance } = useContext(GlobalContext);
  const primaryColor = appearance?.primaryColor;
  const maxGrade = getMaxPointsFromQcm(serie);

  const [selectedUserIds, setSelectedUserIds] = React.useState([]);

  const onChangeUniqueGroup = (toAdd, groupId) => {
    if (toAdd) {
      const temp = [...new Set([...selectedGroupIds, groupId])];
      setSelectedGroupIds(temp);
    } else if (toAdd === false) {
      setSelectedGroupIds(
        selectedGroupIds.filter((item) => !(groupId === item || groupId === parseInt(item)))
      );
    }
  };

  // À voir si on garde (possibilité de check dossiers de groupes entiers)
  /*
  const onChangeMultipleGroups = (groupArray, toAdd) => {
    if (toAdd) {
      const temp = [...new Set([...groupsToCompare, ...groupArray])];
      setGroupsToCompare(temp);
    } else if (toAdd === false) {
      setGroupsToCompare(
        groupsToCompare.filter(
          (item) => !(groupArray.includes(item) || groupArray.includes(parseInt(item)))
        )
      );
    } else {
      throw new Error(`toAdd ni true ni false, : ${JSON.stringify(toAdd)}`);
    }
  };
  */

  const onAddIndividualGroup = async (individualGroupId, userId) => {
    const obj = {
      id: individualGroupId,
      name: userId,
      isIndividual: true
    };
    setSelectedUserIds([...selectedUserIds, obj]);
    // ajout groupe
    setSelectedGroupIds([...selectedGroupIds, individualGroupId]);
  };
  const onRemoveIndividualGroup = async (individualGroupId) => {
    setSelectedUserIds(selectedUserIds.filter((g) => g.id !== individualGroupId));
    // suppression groupe
    setSelectedGroupIds(selectedGroupIds.filter((g) => g !== individualGroupId));
  };

  const getAverageValue = () => {
    if (!canSeeAverage) {
      return <LockKeyhole color={primaryColor} />;
    }
    return `${gradeOutOf ? (gradeOutOf * stats?.moyenne) / maxGrade : stats?.moyenne}/${gradeOutOf ? gradeOutOf : maxGrade}`;
  };

  const getMinGradeValue = () => {
    if (!canSeeAverage || !canSeeGrade) {
      return <LockKeyhole color={primaryColor} />;
    }
    return `${gradeOutOf ? (gradeOutOf * stats?.minGrade) / maxGrade : stats?.minGrade}/${gradeOutOf ? gradeOutOf : maxGrade}`;
  };

  const getMaxGradeValue = () => {
    if (!canSeeAverage || !canSeeGrade) {
      return <LockKeyhole color={primaryColor} />;
    }
    return `${gradeOutOf ? (gradeOutOf * stats?.maxGrade) / maxGrade : stats?.maxGrade}/${gradeOutOf ? gradeOutOf : maxGrade}`;
  };

  return (
    <div
      style={{
        display: 'grid',
        gridTemplateColumns: '1fr 1fr',
        height: '100%',
        gap: 24
      }}
    >
      <Typography.Title
        level={4}
        style={{
          gridColumn: 'span 2',
          display: 'flex',
          alignItems: 'center',
          gap: 16,
          margin: 0
        }}
      >
        {t('general.Statistics')}
        {(isTuteur() || isAdmin()) && (
          <Popover
            trigger="click"
            content={
              <>
                {/* Groupes */}
                <h2>{t('general.Groups')}</h2>
                <AbstractGroupsManager
                  //groupes={allGroupes?.filter((g) => selectedGroupsIds?.includes(g?.id))}
                  enableFolderChecking={false}
                  onChange={onChangeUniqueGroup}
                />
                <Divider />
                <h2>{t('Users')}</h2>
                {/* Groupes individuels */}
                <IndividualPermissionsManager
                  showText={false}
                  enableAddGroupButton={false}
                  individualGroups={selectedUserIds}
                  onAdd={onAddIndividualGroup}
                  onRemove={onRemoveIndividualGroup}
                />
              </>
            }
          >
            <Button type="text" shape="circle">
              <Cog style={{ fontSize: 26 }} />
            </Button>
          </Popover>
        )}
      </Typography.Title>
      <div>
        <McqSingleStat
          label={t('general.Average')}
          value={getAverageValue()}
          type={canSeeAverage ? 'outOf' : 'none'}
          index={1}
          decimals={2}
        />
      </div>
      <div>
        <McqSingleStat
          label={t('Participants')}
          value={dataRanking?.monClassementQcm?.total}
          index={2}
          decimals={2}
        />
      </div>
      <div>
        <McqSingleStat
          label={t('Completions')}
          value={serie?.countResultatsEleves}
          index={3}
          statExplanation={t('CompletionsExplanation')}
        />
      </div>
      <div>
        <McqSingleStat
          label={t('AverageDuration')}
          value={
            stats?.averageSeconds !== null ? (
              stats?.averageSeconds
            ) : (
              <Hourglass color={primaryColor} />
            )
          }
          type={stats?.averageSeconds !== null ? 'time' : 'none'}
          index={4}
        />
      </div>
      <div>
        <McqSingleStat
          label={t('MinGrade')}
          value={getMinGradeValue()}
          type={canSeeAverage ? 'outOf' : 'none'}
          index={5}
          decimals={2}
        />
      </div>
      <div>
        <McqSingleStat
          label={t('MaxGrade')}
          value={getMaxGradeValue()}
          type={canSeeAverage ? 'outOf' : 'none'}
          index={6}
          decimals={2}
        />
      </div>
    </div>
  );
}
