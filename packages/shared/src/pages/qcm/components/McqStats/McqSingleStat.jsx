import React from 'react';
import { Card, Popover, Statistic } from 'antd';
import CountUp from 'react-countup';
import InfoCircleOutlined from '@ant-design/icons/lib/icons/InfoCircleOutlined';
import { Trophy } from 'lucide-react';

export function McqSingleStat({
  style,
  label,
  value,
  type,
  index,
  children,
  decimals = 0,
  statExplanation = null
}) {
  const displayedDecimals = (num) => {
    if (decimals !== 0) {
      if (num % 1 === 0) {
        return 0;
      }
      const decimalString = num.toString().split('.')[1] || '';
      const decimalCount = decimalString.length;
      return decimalCount >= 2 ? 2 : decimalCount;
    }

    return 0;
  };

  const countUp = (value, decimals) => {
    return (
      <CountUp delay={0.3 * index} duration={3} end={value} separator=" " decimals={decimals} />
    );
  };

  const timeFormatter = (value) => {
    const hours = Math.floor(value / 3600);
    const minutes = Math.floor((value % 3600) / 60);
    const seconds = (value % 3600) % 60;

    return (
      <>
        {hours > 0 && <span>{countUp(hours, displayedDecimals(hours))}h</span>}
        {minutes > 0 && <span>{countUp(minutes, displayedDecimals(minutes))}m</span>}
        {seconds > 0 && <span>{countUp(seconds, displayedDecimals(seconds))}s</span>}
      </>
    );
  };

  const outOfFormatter = (str) => {
    const elements = str.split('/');
    return (
      <span>
        {countUp(elements[0], displayedDecimals(elements[0]))}/{elements[1]}
      </span>
    );
  };

  const rankingFormatter = (str) => {
    const elements = str.split('/');

    const regex = /^(\d+)(.*)$/;
    const match = elements[0].match(regex);
    if (match) {
      const rank = match[1];
      const particle = match[2];
      const trophyColor = rank === '1' ? '#ffcc00' : rank === '2' ? '#bfbebe' : '#cc6e00';

      return (
        <span style={{ fontSize: '22px', display: 'flex', alignItems: 'center' }}>
          {Number(rank) <= 3 && <Trophy style={{ marginRight: '8px', color: trophyColor }} />}
          {countUp(rank, displayedDecimals(rank))}
          <sup style={{ fontSize: '14px' }}>{particle}</sup>/{elements[1]}
        </span>
      );
    } else return null;
  };

  const getStatFormatter = (value) => {
    switch (type) {
      case 'outOf':
        return outOfFormatter(value);
      case 'time':
        return timeFormatter(value);
      case 'rank':
        return rankingFormatter(value);
      case 'none':
        return value;
      default:
        return countUp(value, displayedDecimals(value));
    }
  };

  const getTitle = () => {
    if (statExplanation !== null) {
      return (
        <Popover
          content={
            <div style={{ maxWidth: 300 }}>
              <p style={{ margin: 0 }}>{statExplanation}</p>
            </div>
          }
        >
          {label} <InfoCircleOutlined />
        </Popover>
      );
    }
    return label;
  };

  return (
    <Card style={{ ...style, width: '100%', height: '100%', minWidth: 139 }}>
      <Statistic
        title={getTitle()}
        value={value}
        formatter={getStatFormatter}
        valueStyle={{ fontWeight: 600, color: '#404040' }}
      />
      {children && children}
    </Card>
  );
}
