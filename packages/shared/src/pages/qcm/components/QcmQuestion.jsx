import RenderQuillHtml from '@/shared/components/ExoQuill/RenderQuillHtml';
import { FileImage } from '@/shared/components/FileImage.jsx';
import TextWithNotionsDetection from '@/shared/components/Notion/TextWithNotionsDetection.jsx';
import {
  QUERY_ELEMENTS_IN_QUESTION,
  QUERY_ELEMENTS_IN_QUESTION_FOOTER
} from '@/shared/graphql/cours.js';
import { QuestionAnswerType } from '@/shared/pages/admin/qcm/components/modal/CreateEditQuestionModal.jsx';
import FormationEditableElement from '@/shared/pages/formations/components/FormationEditableElement.jsx';
import { FormationContextProvider } from '@/shared/pages/formations/context/FormationContext.jsx';
import DiscussionsExercise from '@/shared/pages/qcm/components/correction/DiscussionsExercise';
import QuestionEditionWrapper from '@/shared/pages/qcm/components/correction/QuestionEditionWrapper.jsx';
import { DoFillInTheBlanksExercise } from '@/shared/pages/qcm/components/FillInTheBlanks/DoFillInTheBlanksExercise';
import { FillInTheBlanksExerciseCorrection } from '@/shared/pages/qcm/components/FillInTheBlanks/FillInTheBlanksExerciseCorrection';
import { QcmQuestionAnswerCorrection } from '@/shared/pages/qcm/components/QcmQuestionAnswerCorrection.js';
import { DoSchemaExercise } from '@/shared/pages/qcm/components/Schema/DoSchemaExercise';
import SchemaElementsCorrection from '@/shared/pages/qcm/components/Schema/SchemaElementsCorrection';
import SchemaExercise from '@/shared/pages/qcm/components/Schema/SchemaExercise';
import { DoMcqContext, DoMcqContextProvider } from '@/shared/pages/qcm/context/DoMcqContext.jsx';
import {
  McqQuestionContext,
  McqQuestionContextProvider
} from '@/shared/pages/qcm/context/McqQuestionContext.jsx';
import { buildQcmImage } from '@/shared/services/qcm.js';
import {
  renderAnswerNameWithFallback,
  renderCourseNameAndDescriptionWithFallback,
  renderQuestionNameWithFallback,
  tr
} from '@/shared/services/translate.js';
import {
  addReturnToHtmlText,
  alphabetAndNumbersArray,
  alphabetArray,
  displayDirectHtml,
  getNotionsInTextEnabled,
  IS_DEV,
  isAptoria,
  isMobile,
  numericalArrayForCombinations,
  tryParseJSONObject
} from '@/shared/utils/utils.js';
import { InfoCircleTwoTone, LinkOutlined } from '@ant-design/icons';
import { useQuery } from '@apollo/client';
import { Card, Checkbox, Form, Input, Popover, Radio, Select, Space, Spin, Tag } from 'antd';
import React, {
  Fragment,
  memo,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState
} from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'umi';
import { GlobalContext } from '@/shared/layouts/BlankLayout';
import DisplayFlashcardExercise from '@/shared/pages/qcm/components/Flashcard/DisplayFlashcardExercise';

export const isTextWithNotionsEnabled = getNotionsInTextEnabled();

export const renderQuestionCategorie = (question) =>
  question?.linkedCours?.map(
    (cours, i) =>
      (cours?.ueCategory?.name && (
        <Tag key={i} color="geekblue">
          {cours?.ueCategory?.[tr('name')]}
        </Tag>
      )) ||
      ''
  );

export const renderQuestionCategorieGenerated = (question) =>
  (question.name && <Tag color="geekblue">{question?.[tr('name')]}</Tag>) || '';

export const renderLinkedCours = (question) =>
  (question?.linkedCours?.length > 0 && (
    <>
      {question?.linkedCours?.map((c) => {
        const cours = c?.targetCours || c;
        return (
          <Link to={`/cours/${c?.id}`} key={c?.id}>
            <Tag style={{ whiteSpace: 'normal' }} color="#87d068">
              {renderCourseNameAndDescriptionWithFallback(cours)}
            </Tag>
          </Link>
        );
      })}
    </>
  )) ||
  '';

export const renderAnnee = (annee) => `- ${annee}-${parseInt(annee, 10) + 1}`;

export const renderQuestionType = (question, showYear, qcmType) => {
  if (isAptoria) {
    return question?.typeQuestion?.map((tq) => (
      <Tag key={tq?.id} color="purple">
        {tq?.name}
      </Tag>
    ));
  }
  const renderYear = (year) => {
    return <Tag color="purple">{`${year}-${parseInt(year, 10) + 1}`}</Tag>;
  };
  // TODO use new mcq type from parentQcm
  return (
    // eslint-disable-next-line no-undef
    <Space>
      {qcmType?.map((type) => (
        <Tag color={'blue'} key={type?.id}>
          {type?.name}
        </Tag>
      ))}
      {/* (annales) */}
      {showYear && question?.annee && renderYear(question?.annee)}
      {/* Annee entrainement */}
      {showYear && question?.parentQcm && renderYear(question?.parentQcm?.annee)}
    </Space>
  );
};

export const LetterCircle = ({
  letter,
  isSelected,
  correction = false,
  wasRight = undefined,
  displayTrueFalseUndefined = null
}) => {
  const { t } = useTranslation();

  let shouldDisplaySelected = isSelected;
  if (displayTrueFalseUndefined === true) {
    if (isSelected === true) {
      shouldDisplaySelected = true;
    } else if (isSelected === false) {
      shouldDisplaySelected = true;
    } else if (isSelected === undefined) {
      shouldDisplaySelected = undefined;
    }
  }

  /*
    const getBackgroundColor = () => {
      if (wasRight === true) {
        return '#53c519'; // Vert si wasRight est vrai
      } else if (wasRight === false) {
        return '#ff4d4e'; // Rouge si wasRight est faux
      } else {
        return 'white'; // Blanc si wasRight est indéfini
      }
    };
  */

  const getBackgroundColor = () => {
    if (correction) {
      if (wasRight === true) {
        return '#53c519'; // Vert si wasRight est vrai
      }
      if (wasRight === false) {
        return '#ff4d4e'; // Rouge si wasRight est faux
      }
      return 'white'; // Blanc si wasRight est indéfini
    }
    if (displayTrueFalseUndefined === true) {
      if (isSelected) {
        return '#53c519';
      }
      if (isSelected === false) {
        return '#ff4d4e';
      }
      if (isSelected === undefined) {
        return 'white';
      }
    } else {
      if (wasRight !== undefined && isSelected) {
        if (wasRight) {
          return '#53c519';
        }
        return '#ff4d4e';
      }
      return isSelected ? '#1890ff' : 'white';
    }
  };

  return (
    <div
      style={{
        borderRadius: '50%',
        width: '24px',
        height: '24px',
        lineHeight: '24px',
        // border: 'solid black 1px',
        border: correction
          ? '1px solid #eeeeee'
          : shouldDisplaySelected
            ? 'none'
            : '1px solid black',
        position: 'absolute',
        top: correction ? '-15px' : '-10px',
        left: correction ? '-15px' : '-10px',
        textAlign: 'center',
        zIndex: 7,
        backgroundColor: shouldDisplaySelected ? getBackgroundColor() : 'white',
        color: shouldDisplaySelected ? 'white' : 'black'
      }}
    >
      <span style={{ fontWeight: 600 }}>{letter}</span>
    </div>
  );
};

const AnswerWrapper = ({ answer, question, n, form }) => {
  // Context for user answers, used for true/false/undefined
  const { userChoicesByQuestion, setUserChoicesByQuestion } = useContext(DoMcqContext);
  // Déterminez si la question actuelle est dans userChoicesByQuestion
  const currentQuestionChoices = useMemo(() => {
    return userChoicesByQuestion.find((q) => q.id_question === question.id_question);
  }, [userChoicesByQuestion, question]);

  return (
    <Answer
      answer={answer}
      question={question}
      n={n}
      form={form}
      currentQuestionChoices={currentQuestionChoices}
      setUserChoicesByQuestion={setUserChoicesByQuestion}
    />
  );
};

/**
 * Renders a question/exercise item  (A,B,C, etc.)
 *
 * @param answer
 * @param question
 * @param n
 * @param form
 * @returns {JSX.Element}
 * @constructor
 */
const Answer = memo(
  ({ answer, question, n, form, currentQuestionChoices, setUserChoicesByQuestion }) => {
    const { t } = useTranslation();

    const displayTrueFalseUndefined = useMemo(
      () => question?.type === QuestionAnswerType.TRUE_OR_FALSE_OR_UNDEFINED,
      [question?.type]
    );

    if (IS_DEV) {
      console.log('re-render Answer');
    }
    // If answer is selected it's highlighted
    const [isSelected, setIsSelected] = useState(displayTrueFalseUndefined ? undefined : false);
    // Context used for radio buttons only
    const { setCurrentSelection, currentSelection } = useContext(McqQuestionContext);

    // restore user answers if needed (synchronise custom selection design with form values)
    useEffect(() => {
      if (form) {
        const answersSelected = form.getFieldValue(question?.id_question); // array of answers
        //console.log({ answersSelected });
        if (answersSelected && answersSelected?.includes(answer?.id)) {
          setIsSelected(true);
        }
      }
      if (IS_DEV) {
        console.log('useEffect Answer');
      }
    }, [answer]);

    /* For Radio Buttons, highlight the one selected */
    useEffect(() => {
      if (!question?.isCheckbox) {
        if (currentSelection?.find((o) => o.id === answer?.id)) {
          setIsSelected(true);
        } else {
          setIsSelected(false);
        }
      }
    }, [currentSelection]);

    // Determine answer type
    const CheckBoxOrRadioType = question?.isCheckbox ? Checkbox : Radio;

    // Highlighted style Fonction de style optimisée avec useMemo
    const selectedStyle = useMemo(() => {
      if (displayTrueFalseUndefined) {
        if (isSelected === true) {
          return {
            background: '#f2f8ff',
            borderRadius: '10px',
            border: '1px solid #1890ff'
          };
        }
        if (isSelected === false) {
          return {
            background: '#FFDADAFF',
            borderRadius: '10px',
            border: '1px solid rgb(212 0 0)'
          };
        }
      } else if (isSelected) {
        return {
          background: '#f2f8ff',
          borderRadius: '10px',
          border: '1px solid #1890ff'
        };
      }

      return {
        background: 'rgb(249 249 249)',
        borderRadius: '10px',
        border: '1px dashed rgb(183 183 183)'
      };
    }, [isSelected, displayTrueFalseUndefined]);

    const answerImageRender = (
      <div>
        <FileImage
          imageType="QCM"
          image={answer?.url_image}
          useAntdComponent
          style={{ display: 'block', maxHeight: '230px', height: 'auto' }}
        />
      </div>
    );

    const handleChangeValueEverywhere = useCallback(
      (value, onlyLastOneSelected = false) => {
        setIsSelected(value);
        setCurrentSelection([
          {
            id: answer?.id,
            selected: value
          }
        ]);
        // Change in context too
        let currentQuestionObject = currentQuestionChoices;
        if (!currentQuestionObject) {
          currentQuestionObject = {
            id_question: question?.id_question,
            answers: [], // True
            answers_false: [] // False
            // answers_undefined: [], // Undefined
          };
        }
        if (value === true) {
          if (onlyLastOneSelected) {
            // For Radio unique choices
            // Add to true
            currentQuestionObject.answers = [answer?.id];
            // Remove from false
            currentQuestionObject.answers_false = [];
          } else {
            // Add to true
            currentQuestionObject.answers = [
              ...new Set([...currentQuestionObject.answers, answer?.id])
            ];
            // Remove from false
            currentQuestionObject.answers_false = currentQuestionObject.answers_false.filter(
              (a) => String(a) !== String(answer?.id)
            );
          }
        } else if (value === false) {
          // Add to false
          currentQuestionObject.answers_false = [
            ...new Set([...currentQuestionObject.answers_false, answer?.id])
          ];
          // Remove from true
          currentQuestionObject.answers = currentQuestionObject.answers.filter(
            (a) => String(a) !== String(answer?.id)
          );
        } else if (value === undefined) {
          // Remove from true
          currentQuestionObject.answers = currentQuestionObject.answers.filter(
            (a) => String(a) !== String(answer?.id)
          );
          // Remove from false
          currentQuestionObject.answers_false = currentQuestionObject.answers_false.filter(
            (a) => String(a) !== String(answer?.id)
          );
        }
        setUserChoicesByQuestion((u) => [
          ...u.filter((q) => String(q.id_question) !== String(question?.id_question)),
          currentQuestionObject
        ]);
      },
      [answer, question, currentQuestionChoices, setUserChoicesByQuestion, setCurrentSelection]
    );

    const handleClickItem = useCallback(() => {
      if (!question?.isCheckbox) {
        // If radio button, set selected answer id for highlight only
        handleChangeValueEverywhere(true, true);
      } else {
        // Select or unselect other types of answer
        if (displayTrueFalseUndefined) {
          // Switch between true/false/undefined
          if (isSelected === undefined) {
            handleChangeValueEverywhere(true);
          } else if (isSelected === true) {
            handleChangeValueEverywhere(false);
          } else if (isSelected === false) {
            handleChangeValueEverywhere(undefined);
          }
        } else {
          // Basic true/false
          handleChangeValueEverywhere(!isSelected);
        }
      }
    }, [question, displayTrueFalseUndefined, isSelected, handleChangeValueEverywhere]);

    const onChangeRadioGroup = (e) => {
      handleChangeValueEverywhere(e.target.value);
    };

    /*
                                                const onChangeRadioGroup = useCallback(e => {
                                                      handleChangeValueEverywhere(e.target.value);
                                                  }, [handleChangeValueEverywhere]);
                                                */

    return (
      <>
        <Form.Item
          key={answer.id}
          className="qcmAnswerFormItem"
          htmlFor={`selectable-${answer?.id}`}
          style={{ width: '100%', flexGrow: 2 }}
        >
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              flexWrap: isMobile ? 'wrap' : 'nowrap',
              justifyContent: isMobile ? 'center' : 'flex-start',
              // justifyContent: 'space-evenly',
              // whiteSpace: 'pre-line',
              // wordBreak: 'normal',
              gap: '4px'
            }}
          >
            <CheckBoxOrRadioType
              disabled={answer.isHorsConcours}
              value={answer.id}
              className="checkboxExoteach"
              style={{ whiteSpace: 'normal', padding: '8px', width: '100%', ...selectedStyle }}
              id={`selectable-${answer?.id}`}
              onClick={handleClickItem}
            >
              <LetterCircle
                letter={alphabetArray[n]}
                isSelected={isSelected}
                displayTrueFalseUndefined={displayTrueFalseUndefined}
              />

              {/* ICI PROBLEME RENDU EQUATIONS KATEX */}
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  //whiteSpace: 'pre-line',
                  //wordBreak: 'normal',
                  gap: '150px'
                }}
              >
                <div>
                  <RenderQuillHtml>{renderAnswerNameWithFallback(answer)}</RenderQuillHtml>

                  {answer.isHorsConcours ? (
                    <span>
                      &nbsp; <Tag>{t('Outdated')}</Tag>
                    </span>
                  ) : (
                    ''
                  )}
                </div>
                {answer?.url_image && answerImageRender}
              </div>
            </CheckBoxOrRadioType>

            {/* Affichage avec vrai/faux activé */}
            {displayTrueFalseUndefined && (
              <div
                style={{
                  // flexGrow: 1,
                  minWidth: '140px'
                }}
              >
                <Radio.Group
                  buttonStyle="solid"
                  defaultValue={undefined}
                  onChange={onChangeRadioGroup}
                  value={isSelected}
                  // A voir si mieux petit sur mobile, ou carrément masqué puisqu'on peut cliquer
                  // size={isMobile ? 'small' : 'default'}
                >
                  <Radio.Button
                    style={
                      isSelected === true
                        ? {
                            backgroundColor: '#73d13d',
                            borderColor: '#73d13d'
                          }
                        : {}
                    }
                    value={true}
                  >
                    {t('general.TRUE')}
                  </Radio.Button>
                  <Radio.Button
                    style={
                      isSelected === false
                        ? {
                            backgroundColor: '#ff4d4f',
                            borderColor: '#ff4d4f'
                          }
                        : {}
                    }
                    value={false}
                  >
                    {t('general.FALSE')}
                  </Radio.Button>
                </Radio.Group>
              </div>
            )}
          </div>
        </Form.Item>
      </>
    );
  }
);

const shuffleArray = (array) => {
  for (let i = array.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    const temp = array[i];
    array[i] = array[j];
    array[j] = temp;
  }
  return true;
};

/**
 * Render a doable exercise / question with its answers
 *
 * @param question
 * @param showYear
 * @param isGenerated
 * @param index
 * @param inCard
 * @param qcmType
 * @param randomizeQuestionsAnswers
 * @param setLastArrayOrder
 * @param form
 * @returns {JSX.Element}
 */
export const QuestionWithAnswers = memo(
  ({
    question: questionProp,
    showYear = false,
    index,
    inCard = true,
    qcmType,
    randomizeQuestionsAnswers = false, // Randomize answers order
    setLastArrayOrder, // Get array order after randomization
    form,
    showEditionButton = true, // Bouton d'édition de la question (admins/profs only)
    refetch,
    setHasEvaluatedCertainty
  }) => {
    const { appearance } = useContext(GlobalContext);
    const primaryColor = appearance?.primaryColor;

    const { t } = useTranslation();

    const [question, setQuestion] = useState(questionProp);

    // Query elements header
    const { data: dataElements } = useQuery(QUERY_ELEMENTS_IN_QUESTION, {
      fetchPolicy: 'no-cache',
      variables: { id: questionProp?.id_question }
    });

    // Query elements footer for flashcard
    const { data: dataElementsFooter } = useQuery(QUERY_ELEMENTS_IN_QUESTION_FOOTER, {
      fetchPolicy: 'no-cache',
      variables: { id: questionProp?.id_question }
    });

    /* Shuffle */
    const [isShuffled, setIsShuffled] = useState(false);
    useEffect(() => {
      if (!isShuffled && questionProp?.answers.length > 0 && randomizeQuestionsAnswers) {
        let questionToSet = JSON.parse(JSON.stringify(questionProp));
        shuffleArray(questionToSet.answers);
        setQuestion(questionToSet);
        setIsShuffled(true);

        if (setLastArrayOrder) {
          setLastArrayOrder(questionToSet.answers);
        }
      }
    }, [questionProp, isShuffled, randomizeQuestionsAnswers, setLastArrayOrder]);
    /* End Shuffle */

    const isAlphanumericQuestion = useMemo(
      () =>
        question?.type &&
        [QuestionAnswerType.NUMERICAL, QuestionAnswerType.ALPHANUMERICAL].includes(question?.type),
      [question]
    );

    const isSchema = useMemo(
      () =>
        question?.type &&
        [
          QuestionAnswerType.SCHEMA_POINT_AND_CLICK,
          QuestionAnswerType.SCHEMA_FILL_IN_LEGENDS
        ].includes(question?.type),
      [question]
    );

    const FormChildGroupType = useMemo(
      () => (question?.isCheckbox ? Checkbox.Group : Radio.Group),
      [question]
    );

    const isFlashcard = useMemo(
      () => question?.type && [QuestionAnswerType.FLASHCARD].includes(question?.type),
      [question]
    );

    const isCheckbox = question?.isCheckbox;

    const questionNumber = parseInt(index, 10) + 1;
    const renderQuestionNumber = () => `${questionNumber})`;

    /* Question rendering in Form.Item */
    /* Ce qui semble être le lien entre le form et ce que l'user réponds (donc, componant antDesign qui modifie le form) */
    const renderQuestion = useMemo(() => {
      if (question?.isAnswerFreeText) {
        return <Input.TextArea rows={8} type="textarea" placeholder={t('WriteYourAnswer')} />;
      }
      if (isSchema) {
        return <DoSchemaExercise question={question} form={form} refetch={refetch} />;
      }
      if (question?.type && [QuestionAnswerType.FILL_IN_THE_BLANKS].includes(question?.type)) {
        return <DoFillInTheBlanksExercise question={question} form={form} refetch={refetch} />;
      }
      if (isFlashcard) {
        return '';
      }
      if (isAlphanumericQuestion) {
        return (
          <Select
            showArrow
            mode="multiple"
            style={{ minWidth: '350px', width: '100%' }}
            placeholder="Sélectionnez la combinaison de réponse"
            // defaultValue={selectedAlphanumOptions}
            /*
              onChange={(_, options) => {
                const ids = options?.map(o => o.key);
                setSelectedAlphanumOptions(ids);
              }}
            */
            options={(question?.type === QuestionAnswerType.ALPHANUMERICAL
              ? alphabetAndNumbersArray
              : numericalArrayForCombinations
            )?.map((character) => ({
              value: character,
              key: character
            }))}
          />
        );
      }
      if (question?.isAnswerUniqueChoiceInList) {
        return (
          <Select
            showArrow
            style={{ minWidth: '150px', width: '100%' }}
            placeholder={t('SelectAnAnswer')}
            options={question?.answers?.map((answer) => ({
              label: answer.text,
              value: answer.id,
              key: answer.id
            }))}
          />
        );
      }
      if (question?.isAnswerMultipleChoiceInList) {
        return (
          <Select
            showArrow
            tagRender={({ title, label, value, closable, onClose, key }) => (
              <Tag
                value={value}
                key={key}
                color="geekblue"
                closable={closable}
                onClose={onClose}
                style={{ marginRight: 3 }}
              >
                {label}
              </Tag>
            )}
            mode="multiple"
            style={{ minWidth: '150px', width: '100%' }}
            placeholder={t('SelectOneOrMoreAnswers')}
            options={question?.answers?.map((answer) => ({
              label: answer.text,
              value: answer.id,
              key: answer.id
            }))}
          />
        );
      }

      return (
        <FormChildGroupType style={{ width: '100%' }}>
          <McqQuestionContextProvider>
            {question?.answers?.map((answer, k) => (
              <AnswerWrapper
                answer={answer}
                question={question}
                key={answer?.id}
                n={k}
                form={form}
              />
            ))}
          </McqQuestionContextProvider>
        </FormChildGroupType>
      );
    }, [question, form, t, isAlphanumericQuestion]);

    const Wrapper = inCard ? Card : Fragment;

    const questionImage = useMemo(() => {
      return (
        <div
          style={{
            display: 'flex',
            marginTop: 20,
            marginBottom: 20,
            justifyContent: 'center',
            alignContent: 'center'
          }}
        >
          {/* <img style={{ width: 100 }} src={image}/> */}
          <img
            onError={(e) => {
              e.target.style = 'display: none';
            }}
            style={{ display: 'block', maxWidth: '100%', height: 'auto' }}
            src={buildQcmImage(question.url_image_q)}
            alt={t('general.question')}
          />
        </div>
      );
    }, [question]);

    const renderElement = (element, previousElement, nextElement, key, columnPosition) => (
      <FormationEditableElement
        style={{ transition: 'transform .35s ease-in-out' }}
        key={key}
        element={element}
        nextElement={nextElement}
        previousElement={previousElement}
        questionId={question?.id_question}
        columnPosition={columnPosition}
        canEdit={false}
        refetchAll={() => {
          // No refetch needed here
        }}
      />
    );

    const questionElements = useMemo(() => {
      const elements = dataElements?.elementsInQuestion;
      return (
        <FormationContextProvider>
          {elements?.map((elem, k) => (
            <div key={elem?.id}>
              {renderElement(elem, elements[k - 1], elements[k + 1], elem?.id)}
            </div>
          ))}
        </FormationContextProvider>
      );
    }, [dataElements]);

    const questionCorrectionElements = useMemo(() => {
      const elements = dataElementsFooter?.elementsInQuestionFooter;
      return (
        <FormationContextProvider>
          {elements?.map((elem, k) => (
            <div key={elem?.id}>
              {renderElement(elem, elements[k - 1], elements[k + 1], elem?.id)}
            </div>
          ))}
        </FormationContextProvider>
      );
    }, [dataElementsFooter]);

    if (!question) return <Spin />;

    //const flashcardProps={headStyle={},border:'1px solid red'}
    // vérifier l'aspect mobile ici
    const wrapperProps = inCard && isMobile ? { bodyStyle: { padding: 12 } } : {};

    return (
      <div
        style={{
          //...flashcardProps,
          ...(inCard ? { marginBottom: '0.7rem' } : {})
        }}
        key={index}
      >
        {!isFlashcard && (
          <Wrapper {...wrapperProps}>
            {/* Tags au dessus de l'énoncé exercice */}
            <div>
              {renderQuestionType(question, showYear, qcmType)}
              {/*
                  &nbsp;
                  {isGenerated
                    ? renderQuestionCategorieGenerated(question)
                    : renderQuestionCategorie(question)}
                */}
              &nbsp;
              {renderLinkedCours(question)}
              &nbsp;
              {/* ID question */}
              <Popover
                content={<div>ref: {question?.id_question}</div>}
                title={t('QuestionId')}
                trigger="hover"
              >
                <InfoCircleTwoTone twoToneColor={primaryColor} />
              </Popover>
              {/* Edition question pour users autorisés */}
              &nbsp;
              {showEditionButton && (
                <QuestionEditionWrapper
                  refetch={refetch}
                  question={question}
                  questionNumber={questionNumber}
                />
              )}
            </div>
            <div
              style={{
                fontSize: 20, // Default question header size
                marginTop: 10,
                display: 'flex',
                alignItems: 'flex-start',
                justifyContent: 'flex-start',
                gap: 5
              }}
            >
              {renderQuestionNumber()}{' '}
              <RenderQuillHtml renderEquations>
                {renderQuestionNameWithFallback(question)}
              </RenderQuillHtml>
            </div>

            {questionElements}

            {questionImage}

            <Form.Item name={question.id_question}>{renderQuestion}</Form.Item>
          </Wrapper>
        )}

        {isFlashcard && (
          <>
            {/* important de mettre le Form.Item, c'est ce qui insère la questionId dans le form, et qui gère notament l'ordonnancement dans la correction de DoQuestionInSession */}
            <Form.Item name={question.id_question} />
            <DisplayFlashcardExercise
              question={question}
              questionElements={questionElements}
              questionCorrectionElements={questionCorrectionElements}
              isCorrection={false}
              showYear={showYear}
              qcmType={qcmType}
              showEditionButton={showEditionButton}
              questionNumber={questionNumber}
              refetchQuestion={refetch}
              setHasEvaluatedCertainty={setHasEvaluatedCertainty}
            />
          </>
        )}
      </div>
    );
  }
);

// Detect URLs
function urlify(text) {
  const urlRegex = /(https?:\/\/[^\s]+)/g;
  return text?.replace(urlRegex, (url) => `<a href="${url}">${url}</a>`) || text;
  // or alternatively
  // return text.replace(urlRegex, '<a href="$1">$1</a>')
}

/**
 * Render a question with its answers and correction
 *
 * @param question
 * @param qcmType
 * @param showYear
 * @param isGenerated
 * @param index
 * @param compact
 * @param inCard
 * @param userAnswersRaw
 * @param hasRedoneMCQ
 * @param hideComments
 * @param highlightAnswerId
 * @param answerArrayLastOrderRandomized
 * @param printable
 * @param showTags
 * @returns {JSX.Element}
 */
export const CorrectionQuestionWithAnswers = React.memo(
  ({
    question,
    qcmType = [],
    showYear = false,
    isGenerated = false,
    index,
    compact = false,
    inCard = true,
    userAnswersRaw = null, // Used for highlighting user answers
    hasRedoneMCQ = false,
    hideComments = false,
    highlightAnswerId = null,
    answerArrayLastOrderRandomized = [],
    printable = false,
    showTags = true, // If false, will not show tags
    refetch, // refetch all questions
    showEditionButton = false
  }) => {
    const { t } = useTranslation();
    const [errorCount, setErrorCount] = useState(0);
    const [justesCount, setJustesCount] = useState(0);

    const [userAnswerItems, setUserAnswerItems] = useState([]); // ce que user a coché vrai
    const [userFalseAnswerItems, setUserFalseAnswerItems] = useState([]); // ce que user a coché faux (uniquement si affichage true/false/undefined))

    const [goodAnswerItem, setGoodAnswerItem] = useState(null); // correction: les à cocher vraies
    const [falseAnswerItem, setFalseAnswerItem] = useState(null); // correction: les à cocher faux

    const { isCheckbox, notions, answers, mcqScale = null, maxPoints, isAnswerFreeText } = question;
    const FormChildGroupType = isCheckbox ? Checkbox.Group : Radio.Group;

    const isAlphanumericalOrNumerical =
      question?.type &&
      [QuestionAnswerType.NUMERICAL, QuestionAnswerType.ALPHANUMERICAL]?.includes(question?.type);

    const isSchema = useMemo(
      () =>
        question?.type &&
        [
          QuestionAnswerType.SCHEMA_POINT_AND_CLICK,
          QuestionAnswerType.SCHEMA_FILL_IN_LEGENDS
        ].includes(question?.type),
      [question]
    );
    const isFillInTheBlanks = useMemo(
      () => question?.type && [QuestionAnswerType.FILL_IN_THE_BLANKS].includes(question?.type),
      [question]
    );

    const isTrueFalseUndefined = useMemo(
      () =>
        question?.type && [QuestionAnswerType.TRUE_OR_FALSE_OR_UNDEFINED].includes(question?.type),
      [question]
    );

    const isFlashcard = useMemo(
      () => question?.type && [QuestionAnswerType.FLASHCARD].includes(question?.type),
      [question]
    );

    // Query elements header
    const {
      data: dataElements,
      error: errorElements,
      refetch: refetchElements
    } = useQuery(QUERY_ELEMENTS_IN_QUESTION, {
      fetchPolicy: 'no-cache',
      variables: { id: question?.id_question }
    });
    // Query elements footer
    const {
      data: dataElementsFooter,
      error: errorElementsFooter,
      refetch: refetchElementsFooter
    } = useQuery(QUERY_ELEMENTS_IN_QUESTION_FOOTER, {
      fetchPolicy: 'no-cache',
      variables: { id: question?.id_question }
    });

    // Find answer's alphabet letters - chosen by user (userAnswerItems), and correct (goodAnswerItem)
    useEffect(() => {
      if (question) {
        // User answers
        const userAnswerItemsA = [];
        const userFalseAnswerItemsA = [];

        // Correction
        const goodAnswerItemA = [];
        const falseAnswerItemA = [];

        // Answers to loop on
        let answersToLookFor = question?.answers;
        // Here problem order is not the final order
        if (answerArrayLastOrderRandomized?.length > 0) {
          const ids = answerArrayLastOrderRandomized.map((a) => a.id);
          answersToLookFor = question?.answers?.sort(
            (a, b) => ids.indexOf(a.id) - ids.indexOf(b.id)
          );
        }

        console.log({ answersToLookFor, question });

        /* Alphanumerical questions and answers */
        if (isAlphanumericalOrNumerical) {
          const arrayOfUserChoices = tryParseJSONObject(question?.answerHistory?.valueData);
          if (arrayOfUserChoices) {
            userAnswerItemsA.push(tryParseJSONObject(arrayOfUserChoices));
            answersToLookFor?.forEach((answer, key) => {
              const goodCurrentAnswerLetterArray = tryParseJSONObject(answer?.text); // Array of alphanum letters
              // il faut checker par paquets
              if (goodCurrentAnswerLetterArray) {
                goodAnswerItemA.push(goodCurrentAnswerLetterArray); // can have multiple letters
              }
            });
          }
        } else {
          /* All others types of question (QCM, QCU) */
          answersToLookFor?.forEach((answer, key) => {
            const { isTrue } = answer;
            if (
              answer?.id &&
              question?.answerHistory?.answersData &&
              question?.answerHistory?.answersData?.includes(answer?.id)
            ) {
              userAnswerItemsA.push(alphabetArray[key]); // Ce qu'il a coché vrai
            }

            // Cas où on peut cocher ou undefined, recup ce qu'on a coché faux
            if (isTrueFalseUndefined) {
              let correspondingAnswer = question?.answerHistory?.qcmStatsQuestion?.find(
                (a) => a.answerId === answer?.id
              );
              if (correspondingAnswer && correspondingAnswer.value === '0') {
                // a explicitemnt coché faux
                userFalseAnswerItemsA.push(alphabetArray[key]);
              }
            }

            // Correction
            if (isTrue) {
              goodAnswerItemA.push(alphabetArray[key]);
            } else {
              falseAnswerItemA.push(alphabetArray[key]);
            }

            const ilACocheEtIlFallait =
              answer?.id &&
              Array.isArray(question?.answerHistory?.answersData) &&
              question?.answerHistory?.answersData?.includes(answer?.id) &&
              isTrue;
            const ilAPasCocheEtIlFallait =
              answer?.id &&
              Array.isArray(question?.answerHistory?.answersData) &&
              !question?.answerHistory?.answersData?.includes(answer?.id) &&
              !isTrue;
            if (ilACocheEtIlFallait || ilAPasCocheEtIlFallait) {
              setJustesCount(justesCount + 1);
              // ITEM JUSTE
            } else {
              setErrorCount(errorCount + 1);
              // FAUX
            }
          });
        }

        setUserAnswerItems(userAnswerItemsA);
        setUserFalseAnswerItems(userFalseAnswerItemsA);

        setGoodAnswerItem(goodAnswerItemA);
        setFalseAnswerItem(falseAnswerItemA);
      }
      if (IS_DEV) {
        console.log('useEffet questino answers correction');
      }
    }, [question, answers]);

    const questionNumber = parseInt(index, 10) + 1 || '';
    const renderQuestionNumber = () => (questionNumber && `${questionNumber})`) || '';

    const renderLinkToOriginalMcq = () => {
      return (
        <Link to={`/qcm/${question.id_qcm}`}>
          <Tag icon={<LinkOutlined />} color="default">
            {t('SeeOriginalExerciceSerie')}
          </Tag>
        </Link>
      );
    };

    const imageUrl = buildQcmImage(question.url_image_q);

    const imageQuestion = imageUrl ? (
      <div
        style={{
          display: 'flex',
          marginTop: compact ? 10 : 20,
          marginBottom: compact ? 10 : 20,
          justifyContent: 'center',
          alignContent: 'center'
        }}
      >
        <img
          onError={(e) => {
            e.target.style = 'display: none';
          }}
          style={{ display: 'block', maxWidth: '100%', height: 'auto' }}
          src={imageUrl}
          alt={t('general.question')}
        />
      </div>
    ) : null;

    const elements = dataElements?.elementsInQuestion;
    const elementsFooter = dataElementsFooter?.elementsInQuestionFooter;
    const renderElement = (element, previousElement, nextElement, key, columnPosition) => (
      <FormationEditableElement
        style={{ transition: 'transform .35s ease-in-out' }}
        key={key}
        element={element}
        nextElement={nextElement}
        previousElement={previousElement}
        questionId={question?.id_question}
        columnPosition={columnPosition}
        canEdit={false}
        refetchAll={() => {
          // No refetch needed here
        }}
      />
    );
    const questionElements = (
      <FormationContextProvider>
        {elements?.map((elem, k) => (
          <div key={elem?.id}>
            {renderElement(elem, elements[k - 1], elements[k + 1], elem?.id)}
          </div>
        ))}
      </FormationContextProvider>
    );

    const questionElementsFooter = (
      <FormationContextProvider>
        {elementsFooter?.map((elem, k) => (
          <div key={elem?.id}>
            {renderElement(elem, elementsFooter[k - 1], elementsFooter[k + 1], elem?.id)}
          </div>
        ))}
      </FormationContextProvider>
    );

    const Wrapper = inCard ? Card : Fragment;

    const getMaxPointsFromMcqScale = () => {
      if (isAnswerFreeText) {
        return maxPoints;
      }
      if (mcqScale) {
        return mcqScale?.rules?.pointsPerQuestion;
      }
      return 1;
    };

    const pointsObtenus = () => {
      const pointsObtained = question?.answerHistory?.pointsObtained;
      const maxPoint = getMaxPointsFromMcqScale();
      const hasMaxGrade = pointsObtained === maxPoint;
      const yourAnswersText = userAnswerItems?.length === 1 ? t('YourAnswer') : t('YourAnswers');
      const goodAnswersText = goodAnswerItem?.length === 1 ? t('GoodAnswer') : t('GoodAnswers');

      const cardStyle = isMobile ? { minWidth: '100px' } : { width: '150px' };
      const cardProps = {
        color: hasMaxGrade ? 'green' : 'default',
        bodyStyle: { padding: '12px' },
        style: cardStyle
      };
      const divStyleInCard = {
        display: 'flex',
        flexDirection: 'column',
        width: '100%',
        alignItems: 'center'
      };

      const goodBadAnswersSchemaAndFillInBlanks = () => {
        let goodAnswersNumber = 0;
        let badAnswersNumber = 0;
        if (question.type === QuestionAnswerType.SCHEMA_POINT_AND_CLICK) {
          goodAnswersNumber =
            question?.answerHistory?.jsonAnswers?.filter((j) => j.isInLegend)?.length || 0;
          badAnswersNumber =
            question?.answerHistory?.jsonAnswers?.filter((j) => !j.isInLegend)?.length || 0;
        } else if (
          [
            QuestionAnswerType.SCHEMA_FILL_IN_LEGENDS,
            QuestionAnswerType.FILL_IN_THE_BLANKS
          ].includes(question.type)
        ) {
          goodAnswersNumber =
            question?.answerHistory?.jsonAnswers?.filter((j) => j.isCorrect)?.length || 0;
          badAnswersNumber =
            question?.answerHistory?.jsonAnswers?.filter((j) => !j.isCorrect)?.length || 0;
        }

        return (
          <>
            <Card {...cardProps}>
              <div style={divStyleInCard}>
                <div style={{ color: 'grey', fontSize: '12px' }}>{t('GoodAnswers')}</div>
                <div style={{ fontWeight: '400', fontSize: '18px' }}>{goodAnswersNumber}</div>
              </div>
            </Card>
            <Card {...cardProps}>
              <div style={divStyleInCard}>
                <div style={{ color: 'grey', fontSize: '12px' }}>{t('BadAnswers')}</div>
                <div style={{ fontWeight: '400', fontSize: '18px' }}>{badAnswersNumber}</div>
              </div>
            </Card>
          </>
        );
      };

      return (
        <div style={{ textAlign: 'left', marginBottom: '15px' }}>
          <Space direction="horizontal" wrap>
            {/* POINTS OBTAINED */}
            <Card {...cardProps}>
              <div style={divStyleInCard}>
                <div style={{ color: 'grey', fontSize: '12px' }}>{t('Points')}</div>

                <div style={{ fontWeight: '700', fontSize: '18px' }}>
                  {parseFloat(pointsObtained?.toFixed(2)) || '0'}/{maxPoint}
                </div>
              </div>
            </Card>

            {isSchema || isFillInTheBlanks ? (
              goodBadAnswersSchemaAndFillInBlanks()
            ) : (
              <>
                {/* YOUR ANSWERS / Vos réponses */}
                <Card {...cardProps}>
                  <div style={divStyleInCard}>
                    <div style={{ color: 'grey', fontSize: '12px' }}>{yourAnswersText}</div>

                    {isTrueFalseUndefined ? (
                      <div style={{ fontWeight: '700', fontSize: '18px' }}>
                        {userAnswerItems?.map((ua, i) => (
                          <span key={i} style={{ color: 'rgb(82, 196, 26)' }}>
                            {ua}
                          </span>
                        ))}
                        {userFalseAnswerItems?.map((ua, i) => (
                          <span key={i} style={{ color: 'rgb(255, 77, 79)' }}>
                            {ua}
                          </span>
                        ))}
                      </div>
                    ) : (
                      <div style={{ fontWeight: '700', fontSize: '18px' }}>
                        {userAnswerItems?.length === 0 ? (
                          `(${t('None')})`
                        ) : (
                          <>
                            {userAnswerItems?.map((ua, i) => (
                              <span key={i}>
                                {isAlphanumericalOrNumerical ? (
                                  <>{ua && ua?.map((letter) => letter)}</>
                                ) : (
                                  ua
                                )}
                              </span>
                            ))}
                          </>
                        )}
                      </div>
                    )}
                  </div>
                </Card>

                {/* Bonnes réponses (correction) */}
                <Card {...cardProps}>
                  <div style={divStyleInCard}>
                    <div style={{ color: 'grey', fontSize: '12px' }}>{goodAnswersText}</div>

                    <div style={{ fontWeight: '700', fontSize: '18px' }}>
                      {goodAnswerItem?.map((ua, i) => (
                        <span
                          key={i}
                          style={isTrueFalseUndefined ? { color: 'rgb(82, 196, 26)' } : {}}
                        >
                          {isAlphanumericalOrNumerical ? (
                            <>
                              {i > 0 && ' ou '}
                              {ua?.map((letter) => letter)}
                            </>
                          ) : (
                            ua
                          )}
                        </span>
                      ))}

                      {isTrueFalseUndefined &&
                        falseAnswerItem?.length > 0 &&
                        falseAnswerItem?.map((ua, i) => (
                          <span style={{ color: 'rgb(255, 77, 79)' }} key={i}>
                            {ua}
                          </span>
                        ))}
                    </div>
                  </div>
                </Card>
              </>
            )}
          </Space>
        </div>
      );
    };

    const renderQuestionCorrection = () => {
      if (question?.isAnswerFreeText) {
        return (
          <>
            <div style={{ border: '1px solid lightgrey', borderRadius: 10, padding: 15 }}>
              {question?.answerHistory?.qcmStatsQuestion?.[0]?.value}
            </div>
          </>
        );
      } else if (isSchema) {
        return (
          <div>
            <DoMcqContextProvider>
              <SchemaExercise
                question={question}
                refetch={refetch}
                correction
                hideComments={hideComments}
                printable={printable}
                questionNumber={questionNumber}
              />
            </DoMcqContextProvider>
          </div>
        );
      } else if (isFillInTheBlanks) {
        return (
          <FillInTheBlanksExerciseCorrection
            question={question}
            refetch={refetch}
            printable={printable}
            questionNumber={questionNumber}
            hideComments={hideComments}
          />
        );
      } else {
        return (
          <div>
            <FormChildGroupType style={{ width: '100%', display: 'block' }}>
              <>
                {question?.answers?.map((answer, key) => (
                  <QcmQuestionAnswerCorrection
                    answer={answer}
                    key={answer.id}
                    n={key}
                    compact={compact}
                    isCheckbox={isCheckbox}
                    isGenerated={isGenerated}
                    question={question}
                    userAnswersRaw={userAnswersRaw}
                    questionNumber={questionNumber}
                    hideComments={hideComments}
                    highlightAnswerId={highlightAnswerId}
                    printable={printable}
                  />
                ))}
              </>
            </FormChildGroupType>
          </div>
        );
      }
    };

    const mobileBodyStyle = isMobile
      ? { paddingBottom: 0, paddingLeft: 12, paddingRight: 12, paddingTop: 12 }
      : {};
    const wrapperProps = inCard
      ? {
          size: compact ? 'small' : 'default',
          style: compact
            ? { margin: 0, padding: 0 }
            : { marginBottom: '0.7rem', paddingBottom: '0px' },
          bodyStyle: { paddingBottom: '0px', ...mobileBodyStyle }
        }
      : {};
    if (!question) return <div />;
    return (
      <>
        {!isFlashcard && (
          <Wrapper {...wrapperProps}>
            {showTags && (
              <div>
                {renderQuestionType(question, showYear, qcmType)}
                &nbsp;{renderLinkedCours(question)}
                &nbsp;{isGenerated && renderLinkToOriginalMcq()}
                {/* Edition question pour users autorisés */}
                &nbsp;{' '}
                {showEditionButton && (
                  <QuestionEditionWrapper
                    refetch={refetch}
                    question={question}
                    questionNumber={questionNumber}
                  />
                )}
              </div>
            )}
            <div
              style={{
                fontSize: compact ? 13 : 16,
                marginTop: compact ? 2 : 10,
                whiteSpace: 'pre-line',
                wordBreak: 'normal'
              }}
            >
              {/* POINTS OBTENUS */}
              {!printable && <div>{!hasRedoneMCQ && !isGenerated && pointsObtenus()}</div>}

              <div
                style={{
                  //fontSize: 20,
                  marginTop: 10,
                  display: 'flex',
                  alignItems: 'flex-start',
                  justifyContent: 'flex-start',
                  gap: 5
                }}
              >
                {renderQuestionNumber()}{' '}
                <RenderQuillHtml renderEquations>
                  {renderQuestionNameWithFallback(question)}
                </RenderQuillHtml>
              </div>
            </div>
            {imageQuestion}

            {questionElements}

            {/* Exercise CORRECTION */}
            <Form.Item name={question.id_question}>{renderQuestionCorrection()}</Form.Item>

            {/* Elements correction exercice */}
            {questionElementsFooter}

            {/* Elements correction depuis schema library */}
            {isSchema && <SchemaElementsCorrection schemaId={question.schemaLibraryId} />}

            {/* Discussion pour l'exercice */}
            {(isSchema || isFillInTheBlanks) && (
              <DiscussionsExercise
                question={question}
                questionNumber={questionNumber}
                shouldHideComments={hideComments}
                printable={printable}
              />
            )}
          </Wrapper>
        )}

        {isFlashcard && (
          <>
            {/* important de mettre le Form.Item, c'est ce qui insère la questionId dans le form, et qui gère notament l'ordonnancement dans la correction de DoQuestionInSession */}
            <Form.Item name={question.id_question} />
            <DisplayFlashcardExercise
              question={question}
              questionElements={questionElements}
              questionCorrectionElements={questionElementsFooter}
              isCorrection={true}
              showYear={showYear}
              qcmType={qcmType}
              showEditionButton={showEditionButton}
              questionNumber={questionNumber}
              refetchQuestion={refetch}
            />
          </>
        )}
      </>
    );
  }
);

CorrectionQuestionWithAnswers.displayName = 'CorrectionQuestionWithAnswers';
