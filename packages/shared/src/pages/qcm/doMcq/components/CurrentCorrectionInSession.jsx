import { RenderMathJax } from '@/shared/components/ContentWithMathJax.jsx';
import { CorrectionQuestionWithAnswers } from '@/shared/pages/qcm/components/QcmQuestion.jsx';
import { IS_DEV } from '@/shared/utils/utils.js';
import { TrophyTwoTone } from '@ant-design/icons';
import { Alert, Button, Form } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';

/**
 * Affiche correction exercice en cours.
 * @type {React.NamedExoticComponent<{readonly questionFaites?: *, readonly questionCorrection?: *, readonly userAnswersRaw?: *, readonly showYear?: *, readonly answerArrayLastOrderRandomized?: [], readonly refetchQuestionCorrection?: *}>}
 */
const CurrentCorrectionInSession = React.memo(
  ({
    questionCorrection,
    answerArrayLastOrderRandomized = [],
    questionFaites,
    userAnswersRaw,
    refetchQuestionCorrection,
    showYear
  }) => {
    if (IS_DEV) {
      console.log('re render CurrentCorrectionInSession');
    }

    //useMemo all CorrectionQuestionWithAnswers props
    const questionCorrectionMemo = React.useMemo(() => {
      return questionCorrection;
    }, [questionCorrection]);
    const questionFaitesMemo = React.useMemo(() => {
      return questionFaites;
    }, [questionFaites]);
    const userAnswersRawMemo = React.useMemo(() => {
      return userAnswersRaw;
    }, [userAnswersRaw]);
    const refetchQuestionCorrectionMemo = React.useMemo(() => {
      return refetchQuestionCorrection;
    }, [refetchQuestionCorrection]);
    const showYearMemo = React.useMemo(() => {
      return showYear;
    }, [showYear]);

    return (
      <RenderMathJax>
        <CorrectionQuestionWithAnswers
          question={questionCorrectionMemo}
          answerArrayLastOrderRandomized={answerArrayLastOrderRandomized}
          key={questionCorrection.id_question}
          index={questionFaitesMemo}
          userAnswersRaw={userAnswersRawMemo}
          inCard={false}
          showEditionButton={false} // fonctionne mais ne refetch pas car questions dans localstorage: ça va être confusing pour les profs
          refetch={refetchQuestionCorrectionMemo}
          showYear={showYearMemo}
        />
      </RenderMathJax>
    );
  }
);
CurrentCorrectionInSession.displayName = 'CurrentCorrectionInSession';

/**
 * Formulaire + correction exercice en cours + feedback
 * @type {React.NamedExoticComponent<{readonly loadingMut?: *, readonly questionFaites?: *, readonly question?: *, readonly questionCorrection?: *, readonly onNextQuestion?: *, readonly mapMyAnswers?: *, readonly loading?: *, readonly refetchQuestionCorrection?: *, readonly correctionData?: *, readonly form?: *, readonly loadingSession?: *, readonly withFinishButton?: *, readonly answerArrayLastOrderRandomized?: *, readonly isSeeingCorrection?: *}>}
 */
export const CurrentQuestionCorrection = React.memo(
  ({
    loading,
    form,
    mapMyAnswers,
    onNextQuestion,
    questionCorrection,
    answerArrayLastOrderRandomized,
    questionFaites,
    refetchQuestionCorrection,
    isSeeingCorrection,
    loadingMut,
    loadingSession,
    correctionData,
    question,
    withFinishButton,
    onFinishSingleExercise = null
  }) => {
    const { t } = useTranslation();

    if (IS_DEV) {
      console.log('re render CurrentQuestionCorrection');
    }
    const renderFeedback = () => {
      if (question.isAnswerFreeText) {
        return '';
      }
      if (question.isCheckbox) {
        return (
          <h2 style={{ textAlign: 'center' }}>
            {correctionData?.erreurs === 0 && (
              <Alert
                message={
                  <>
                    {t('CongratsNoMistake')} <TrophyTwoTone />
                  </>
                }
                type="success"
              />
            )}
          </h2>
        );
      }
      // Radio choices
      return (
        <h2 style={{ textAlign: 'center' }}>
          {correctionData?.erreurs === 0 && (
            <>
              <TrophyTwoTone /> {t('Congratulations')}
            </>
          )}
        </h2>
      );
    };

    return (
      <Form
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 24 }}
        form={form}
        initialValues={mapMyAnswers() || undefined}
        size="large"
        onFinish={onNextQuestion}
      >
        {questionCorrection && (
          <CurrentCorrectionInSession
            questionCorrection={questionCorrection}
            answerArrayLastOrderRandomized={answerArrayLastOrderRandomized}
            questionFaites={isSeeingCorrection ? questionFaites - 1 : questionFaites}
            userAnswersRaw={mapMyAnswers()[question.id_question] || []}
            refetchQuestionCorrection={refetchQuestionCorrection}
            showYear={!!withFinishButton}
            key={questionCorrection.id_question}
          />
        )}
        {correctionData && renderFeedback()}
        <Button
          htmlType="submit"
          type="primary"
          block
          loading={loadingMut || loadingSession || loading}
        >
          {onFinishSingleExercise === null ? t('Next') : t('Finish')}
        </Button>
      </Form>
    );
  }
);
CurrentQuestionCorrection.displayName = 'CurrentQuestionCorrection';
