import { QuestionWithAnswers } from '@/shared/pages/qcm/components/QcmQuestion.jsx';
import { useMathJaxScript } from '@/shared/utils/hooks/useScript.js';
import { IS_DEV } from '@/shared/utils/utils.js';
import React, { useEffect } from 'react';

export const ExercisesList = React.memo(
  ({ questions, form, refetch, randomizeQuestionsAnswers, additionnalProps = {} }) => {
    if (IS_DEV) {
      console.log('re render ExercisesList');
    }
    const launchMathJax = useMathJaxScript();
    useEffect(() => {
      launchMathJax();
    }, [questions]);
    return questions?.map((question, key) => (
      <QuestionWithAnswers
        question={question}
        key={question.id_question}
        index={key}
        form={form}
        refetch={() => {
          window.location.reload();
        }}
        {...additionnalProps}
      />
    ));
  }
);
