import { RenderMathJax } from '@/shared/components/ContentWithMathJax.jsx';
import {
  MUTATION_FINISH_TRAINING_IN_SESSION,
  MUTATION_IGNORE_EXERCISE_IN_SESSION
} from '@/shared/graphql/qcm.js';
import { QuestionWithAnswers } from '@/shared/pages/qcm/components/QcmQuestion.jsx';
import { FullscreenCoursDivider } from '@/shared/pages/qcm/doMcq/components/FullscreenCoursDivider.jsx';
import {
  ModalTimesUp,
  ModalTimesUpTypes
} from '@/shared/pages/qcm/doMcq/components/ModalTimesUp.jsx';
import { IS_DEV } from '@/shared/utils/utils.js';
import { useMutation } from '@apollo/client';
import { Alert, Button, Form, Radio, Space, message } from 'antd';
import React, { useContext, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { DoMcqContext } from '@/shared/pages/qcm/context/DoMcqContext';
import {confirmModalAsFunction}from '@/shared/components/ConfirmModalAsFunction'
import { QuestionAnswerType } from '@/shared/pages/admin/qcm/components/modal/CreateEditQuestionModal';

export const DICO_CERTAINTY_LABELS = {
  RANDOM: { value: '0', label: 'ICheckedRandom',labelFlashcard:"ICheckedRandomFlashcard" },
  NOT_SURE: { value: '1', label: 'NotSure',labelFlashcard:"NotSureFlashcard" },
  MODERATLY_SURE: { value: '2', label: 'ModeratlySure',labelFlashcard:"ModeratlySureFlashcard" },
  SURE: { value: '3', label: 'Sure',labelFlashcard:"SureFlashcard" }
};

const QuestionWrapper = React.memo(
  ({
    question,
    shouldRandomizeAnswers,
    questionFaites,
    setArrayLastOrderRandomized,
    form,
    qcm,
    showYear = false,
    setHasEvaluatedCertainty
  }) => {
    if (IS_DEV) {
      console.log('re render QuestionWrapper');
    }

    return (
      <RenderMathJax>
        <QuestionWithAnswers
          showYear={showYear}
          randomizeQuestionsAnswers={shouldRandomizeAnswers}
          question={question}
          key={question.id_question}
          qcmType={qcm?.type}
          index={questionFaites}
          inCard={false}
          setLastArrayOrder={setArrayLastOrderRandomized}
          form={form}
          refetch={() => {
            window.location.reload();
          }}
          setHasEvaluatedCertainty={setHasEvaluatedCertainty}
        />
      </RenderMathJax>
    );
  }
);

QuestionWrapper.displayName = 'QuestionWrapper';

const CurrentQuestionInSession = ({
  question,
  isPresentation,
  changeSectionMut,
  refetchCurrentSession,
  refetchCurrentQuestionInSession,
  loadingSectionChange,
  handleTimerNextQuestion,
  handleFinishQuestion,
  form,
  modalTimesUpVisible,
  setModalTimesUpVisible,
  loadingMut,
  isLoading,
  setIsLoading,
  qcm,
  questionFaites,
  shouldRandomizeAnswers,
  setArrayLastOrderRandomized,
  evaluateCertainty,
  hasEvaluatedCertainty,
  setHasEvaluatedCertainty,
  isSeeingCorrection,

  withIgnoreButton,
  withFinishButton,
  warning = false,

  sessionId
}) => {
  const { t } = useTranslation();

  /* Finish training in session */
  const [finishTrainingInSession, { loading: loadingFinishTrainingInSession }] = useMutation(
    MUTATION_FINISH_TRAINING_IN_SESSION
  );
  /* Finish training in session */
  const [ignoreQuestionInSession, { loading: loadingIgnoreQuestion }] = useMutation(
    MUTATION_IGNORE_EXERCISE_IN_SESSION
  );

  if (IS_DEV) {
    // console.log('re render CurrentQuestionInSession');
  }
  const layoutQuestions = {
    labelCol: { span: 4 },
    wrapperCol: { span: 24 }
  };

  const formRef = useRef(null); // Référence au formulaire

  const {
    hookIsUserAnswerFlashcardMetrics, // Je préfère utiliser la metric, parce que avec isAllOk, ça returne true si non flashcard, et si flashcard : false si non remplie, sinon true
    hookIsCertaintyFilledForFlashcardExercices
  } = useContext(DoMcqContext);

  const metrics = hookIsUserAnswerFlashcardMetrics();
  const isCertaintyFilledForFlashcardExercices = hookIsCertaintyFilledForFlashcardExercices({
    questionId: question?.id_question
  });

  const handleFinishTrainingInSession = async () => {
    await finishTrainingInSession({
      variables: {
        sessionId
      }
    });
    await refetchCurrentSession();
  };
  const handleIgnoreQuestion = async () => {
    setIsLoading(true);
    await ignoreQuestionInSession({
      variables: {
        sessionId,
        questionId: question.id_question
      }
    });
    await refetchCurrentQuestionInSession();
    await refetchCurrentSession();
    // Il faut aussi reset timer
    handleTimerNextQuestion();
    setIsLoading(false);
  };

  const handleSubmitForm = () => {
    // Fonction qui submit le form => permet d'être utilisé avec le modal de confirmation
    if (formRef.current) {
      formRef.current.submit();
    }
  };

  const verifAllFilled = () => {
    if (metrics && metrics?.isAllOk===false) {
      confirmModalAsFunction({
        onConfirm: () => {
          handleSubmitForm();
        },
        title: '',
        description: t('SomeExercicesAreUnanswered'),
        useCheckbox: false,
        okText: t('general.Continue')
      });
    } else {
      handleSubmitForm();
    }
  };

  return (
    <>
      {isPresentation ? (
        <FullscreenCoursDivider
          linkedCoursArray={question?.linkedCours}
          loadingSectionChange={loadingSectionChange}
          onValidate={async () => {
            await changeSectionMut();
            await refetchCurrentSession();
            handleTimerNextQuestion();
          }}
        />
      ) : (
        <Form {...layoutQuestions} onFinish={handleFinishQuestion} form={form} ref={formRef}>
          <ModalTimesUp
            isVisible={modalTimesUpVisible}
            type={ModalTimesUpTypes.QUESTION_BY_QUESTION}
            onFinishOrCancel={() => {
              setModalTimesUpVisible(false);
              form.submit();
            }}
          />
          {question && (
            <QuestionWrapper
              question={question}
              shouldRandomizeAnswers={shouldRandomizeAnswers}
              setArrayLastOrderRandomized={setArrayLastOrderRandomized}
              form={form}
              qcm={qcm}
              questionFaites={questionFaites}
              showYear={!!withFinishButton}
              setHasEvaluatedCertainty={setHasEvaluatedCertainty}
            />
          )}
          <br />

          {evaluateCertainty && !(question.type === QuestionAnswerType.FLASHCARD) && (
            <Form.Item name="certainty">
              <Radio.Group buttonStyle="solid" onChange={() => setHasEvaluatedCertainty(true)}>
                <Radio.Button value={DICO_CERTAINTY_LABELS?.RANDOM?.value}>
                  {t(DICO_CERTAINTY_LABELS?.RANDOM?.label)}
                </Radio.Button>
                <Radio.Button value={DICO_CERTAINTY_LABELS?.NOT_SURE?.value}>
                  {t(DICO_CERTAINTY_LABELS?.NOT_SURE?.label)}
                </Radio.Button>
                <Radio.Button value={DICO_CERTAINTY_LABELS?.MODERATLY_SURE?.value}>
                  {t(DICO_CERTAINTY_LABELS?.MODERATLY_SURE?.label)}
                </Radio.Button>
                <Radio.Button value={DICO_CERTAINTY_LABELS?.SURE?.value}>
                  {t(DICO_CERTAINTY_LABELS?.SURE?.label)}
                </Radio.Button>
              </Radio.Group>
            </Form.Item>
          )}

          {warning && (
            <div>
              <Alert
                type="warning"
                showIcon
                message={"Attention, vous n'avez rien sélectionné sur le schéma"}
                style={{ marginBottom: '12px' }}
              />
            </div>
          )}

          <div style={{ display: 'flex', gap: '12px' }}>
            {withIgnoreButton && (
              <Button onClick={handleIgnoreQuestion} block loading={loadingMut || isLoading}>
                {t('IgnoreThisExercise')}
              </Button>
            )}
            <Button
              onClick={verifAllFilled}
              type="primary"
              block
              loading={loadingMut || isLoading}
              disabled={
                evaluateCertainty &&
                !(hasEvaluatedCertainty || isCertaintyFilledForFlashcardExercices)
              }
            >
              {t('Next')}
            </Button>
          </div>
          <div style={{ marginTop: '12px' }}>
            {withFinishButton && (
              <Button
                onClick={handleFinishTrainingInSession}
                type="primary"
                block
                loading={loadingFinishTrainingInSession || isLoading}
                disabled={evaluateCertainty && !hasEvaluatedCertainty}
              >
                {t('FinishTraining')}
              </Button>
            )}
          </div>
        </Form>
      )}
    </>
  );
};

export default CurrentQuestionInSession;
