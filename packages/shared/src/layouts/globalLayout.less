
@topBarHeight: 64px;


.ant-layout {
  min-height: 100vh;
}
.ant-layout-header, .ant-pro-top-nav-header-menu .ant-menu.ant-menu-horizontal, .ant-pro-top-nav-header-logo, .ant-pro-global-header, .ant-pro-global-header-logo, .ant-pro-global-header-logo a {
  height: @topBarHeight !important;
  line-height: @topBarHeight !important;
}
.ant-pro-top-nav-header, .ant-pro-top-nav-header-main {
  height: @topBarHeight !important;
}
/*
.ant-pro-top-nav-header .ant-menu.ant-menu-dark .ant-menu-item-selected {
  background: @primary-color
}
*/

.ant-drawer-content {
//  background-color: #001529 !important;
}
.ant-pro-top-nav-header .ant-menu-submenu.ant-menu-submenu-horizontal {
  line-height: @topBarHeight !important;
}


.ant-layout-content {
  margin: 0 !important;
  padding-block: 0 !important;
  padding-inline: 0 !important;
  padding: 0 !important;
}