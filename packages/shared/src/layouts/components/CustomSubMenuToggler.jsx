import { ChevronDown } from 'lucide-react';
import { Menu } from 'antd';
import React from 'react';

export const CustomSubMenuToggler = ({
  icon,
  title,
  key,
  setShowSubMenu,
  showSubMenu,
  containerCollapsed = false,
  openContainer = () => {}
}) => {
  return (
    <Menu.Item
      key={key}
      style={{ paddingLeft: 26 }}
      icon={icon}
      onClick={() => {
        setShowSubMenu(!showSubMenu);
        if (containerCollapsed) openContainer();
      }}
    >
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <span>{title}</span>
        {!containerCollapsed && (
          <ChevronDown
            style={{
              transition: 'all 0.3s ease',
              transform: `rotate(${showSubMenu ? 0 : -90}deg)`
            }}
          />
        )}
      </div>
    </Menu.Item>
  );
};
