import FullMediParticlesBreadCrumb from '@/shared/components/FullMediParticlesBreadCrumb.jsx';
import { DashboardStats } from '@/shared/components/UserDashboard/DashboardStats.jsx';
import { GET_CONFIG } from '@/shared/graphql/home.js';
import { AvatarView } from '@/shared/pages/account/settings/components/profil.jsx';
import {
  CONFIG_KEYS,
  DEFAULT_CONFIG_VARIABLES,
  getApparenceAttribute
} from '@/shared/services/config.js';
import { getValueFromKeyConfigData } from '@/shared/services/home.js';
import {
  getMyDashboardVisibility,
  GlobalConfig,
  isAntemed,
  isAptoria,
  isMedisupPPS,
  tryParseJSONObject
} from '@/shared/utils/utils.js';
import { Badge, Progress } from 'antd';
import React, { useEffect, useState } from 'react';
import { useQuery } from '@apollo/client';
import { GET_ME_WITH_STATS, GET_ME_WITH_STATS_FOR_PROGRESS_BAR } from '@/shared/models/user.js';
import { useTranslation } from 'react-i18next';
import { SlideDown } from 'react-slidedown';
import { isMobile } from '@/shared/utils/utils.js';
import 'react-slidedown/lib/slidedown.css';

const arr = require('@/shared/assets/divers/quote.json');
const compliments = require('@/shared/assets/divers/compliments.json');
const complimentsAptoria = require('@/shared/assets/divers/compliments_aptoria.json');
const complimentsAntemed = require('@/shared/assets/divers/compliments_antemed.json');

function randomInt(min, max) {
  return Math.floor(Math.random() * (max - min + 1) + min);
}

function getCompliment() {
  let comp;
  if (isMedisupPPS) {
    return "Avoir une réussite d'avance"; // Il est content
  }
  if (isAptoria) {
    comp = complimentsAptoria[randomInt(0, complimentsAptoria.length - 1)];
  } else if (isAntemed) {
    comp = complimentsAntemed[randomInt(0, complimentsAntemed.length - 1)];
  } else {
    comp = compliments[randomInt(0, compliments.length - 1)];
  }
  return comp && comp.text;
}

export function ExoDropdown({ open, children, style = { maxWidth: '950px', width: '100%' } }) {
  return (
    <SlideDown className="exoDropDown" style={style}>
      {open ? children : null}
    </SlideDown>
  );
}

export default function (props) {
  const [isActive, setIsActive] = useState(getMyDashboardVisibility());
  const { data, loading, error, refetch } = useQuery(GET_ME_WITH_STATS_FOR_PROGRESS_BAR, {
    pollInterval: 20000,
    fetchPolicy: 'cache-and-network'
  });
  const { t } = useTranslation();
  const { data: dataConfig } = useQuery(GET_CONFIG, {
    fetchPolicy: 'cache-first',
    ...DEFAULT_CONFIG_VARIABLES
  });
  const config = dataConfig?.config;
  const colors =
    tryParseJSONObject(getValueFromKeyConfigData(config, CONFIG_KEYS.COLORS_BREADCRUMB)) || {};
  const appearance = tryParseJSONObject(getValueFromKeyConfigData(config, CONFIG_KEYS.APPEARANCE));

  const showAvatarStats = getApparenceAttribute(appearance, 'showAvatarStats');
  const showHomepageProgressBar = getApparenceAttribute(appearance, 'showHomepageProgressBar');

  const primaryColor = appearance?.primaryColor || '#1677ff';

  useEffect(() => {
    setIsActive(getMyDashboardVisibility());
  }, []);

  const getMe = () => data && data.me;
  const myStats = () => getMe() && getMe().stats;

  const uniqueSeenClasses = myStats()?.uniqueSeenClasses;
  const totalSeeableClasses = myStats()?.totalSeeableClasses;
  const percent =
    uniqueSeenClasses && totalSeeableClasses
      ? Math.round((uniqueSeenClasses / totalSeeableClasses) * 100)
      : 0;

  const gridStyle = {
    width: '100%',
    textAlign: 'center'
  };

  const myUsername = data && data.me && data.me.username;
  const getWelcomeText = () => {
    return `${t('welcome')} ${loading ? '' : myUsername}`;
  };

  const [compliment, setCompliment] = useState();

  useEffect(() => {
    setCompliment(getCompliment());
  }, []);

  const progressBar = (
    <div
      style={{
        //display: 'flex',
        //justifyContent: 'center',
        background: '#FFFFFF',
        boxShadow: '0px 4px 4px rgba(0, 0, 0, 0.25)',
        borderRadius: '10px',
        padding: '10px',

        marginBottom: '30px',
        width: '345px',
        textAlign: 'center'
      }}
    >
      <h3>{t('Progress')}</h3>
      <Progress strokeColor={primaryColor} size={[300, 20]} status="active" percent={percent} />
    </div>
  );

  return (
    <div>
      <FullMediParticlesBreadCrumb
        title={getWelcomeText()}
        subtitle={compliment}
        height="180px"
        textCenter
      />

      <div
        style={{
          position: 'relative',
          display: 'flex',
          flexWrap: 'wrap',
          alignItems: 'center',
          justifyContent: 'center',
          flexDirection: 'Row',
          width: '100%',
          top: '-80px',
          gap: '20px',
          marginBottom: '-70px'
        }}
      >
        <div>
          <Badge>
            <div
              style={{
                border: 'solid white 5pt',
                borderRadius: 100,
                backgroundColor: 'white',
                boxShadow: '0px 4px 4px rgba(0, 0, 0, 0.25)'
              }}
            >
              <AvatarView
                refetch={refetch}
                showTitle={false}
                showChangeButton={false}
                avatar={GlobalConfig.get().AVATARS_URL + data?.me?.avatar}
              />
            </div>
          </Badge>
        </div>

        {showAvatarStats && (
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              background: '#FFFFFF',
              boxShadow: '0px 4px 4px rgba(0, 0, 0, 0.25)',
              borderRadius: '10px',
              padding: '10px'
            }}
          >
            {myStats() && <DashboardStats userStats={myStats()} />}
          </div>
        )}

        {!isMobile && showHomepageProgressBar && progressBar}
      </div>

      {isMobile && showHomepageProgressBar && (
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          {progressBar}
        </div>
      )}
    </div>
  );
}
