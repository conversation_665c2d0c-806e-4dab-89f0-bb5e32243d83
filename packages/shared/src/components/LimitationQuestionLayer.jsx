import {useQuery} from "@apollo/client";
import {GET_CAN_POST_DISCUSSION} from "@/shared/graphql/posts";
import React, {useEffect, useState} from "react";
import {Spin,Statistic} from "antd";
import {StopOutlined} from "@ant-design/icons";
import {useTranslation} from "react-i18next";
const {Countdown} = Statistic


export const LimitationQuestionLayer=({onLimiteValidation})=>{
  /* Componant qui va au chargement, regarder si l'user en question a le droit de poser une question ou pas. Si oui, va automatiquement avancer les steps pour la sélection de type */

  const { t } = useTranslation();

  // Récupération des données
  const {data : {canPostDiscussion = null} = {}, loading,error,refetch:refetchPostRight} = useQuery(GET_CAN_POST_DISCUSSION,{fetchPolicy:'no-cache'}) // tous les types de QCM
  const [processing,setProcessing]=useState(true)
  const [secondsLefts,setSecondsLeft]=useState(null)

  useEffect(()=>{
    // On vois qu'on a fini de load les données et qu'il y a pas d'erreures
    if(loading === false && canPostDiscussion!==null){
      if (canPostDiscussion===0){onLimiteValidation && onLimiteValidation()}
      else {setSecondsLeft(canPostDiscussion)}
      setProcessing(false)
    }
  },[canPostDiscussion,loading])

  return (
    <>
      {processing ?
        <Spin/>
        :
        <div style={{display:"flex",flexDirection:"column",alignItems:"center"}}>
          <StopOutlined style={{fontSize:"24px"}}/>
          {secondsLefts === -1 ?
            <div>{t('LimitationQuestionLayer.ExplanationUserDeactivated')}</div>
            :
            <>
              <div>{t('LimitationQuestionLayer.ExplanationUserWithLimite')}</div>

              <div style={{display:"flex",flexDirection:"column",alignItems:"center"}}>
                <div>{t('LimitationQuestionLayer.LeftTimeBeforeNewQuestion')}</div>
                <Countdown
                  value={Date.now() + (secondsLefts*1000)}
                  format={`DD [${t('LimitationQuestionLayer.Days')}] HH [${t('LimitationQuestionLayer.Hours')}] mm [${t('LimitationQuestionLayer.Minutes')}]  ss [${t('LimitationQuestionLayer.Seconds')}]`}
                  valueStyle={{fontSize:17}}
                  onFinish={()=>{refetchPostRight()}}
                />
              </div>
            </>
          }
        </div>
      }
    </>
  )
};