import React, { useEffect } from 'react';
import {
  AUTHORIZED_THUMBNAIL_EXTENSION,
  useVidstackContext,
  VIDSTACK_MIME_TYPES
} from '@/shared/components/Vidstack/VidstackContextProvider';
import { Typography } from 'antd';
import { useTranslation } from 'react-i18next';
import ExoFormImage from '@/shared/components/Forms/ExoFormImage';

const { Title } = Typography;

const VidstackEditThumbnail = () => {
  const { t } = useTranslation();
  const { imageFileStructure, setImageFileStructure, imageSrc, setImageSrc, launchApi } =
    useVidstackContext();

  useEffect(() => {
    if (imageFileStructure) {
      setImageSrc(imageFileStructure?.url);
    }
  }, [imageFileStructure]);

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center'
      }}
    >
      <Title level={4} style={{ marginTop: 0 }}>
        {t('VideoFE.ThumbnailSectionTitle')}
      </Title>
      <div style={{ display: 'flex', flexDirection: 'row' }}>
        <ExoFormImage
          name="image"
          onDelete={() => {
            setImageFileStructure(null);
            setImageSrc(null);
          }}
          label="Image (badge)"
          fileType={'resolved'}
          defaultValue={imageSrc}
          beforeUpload={async (file) => {
            const extension = file?.name?.split('.')?.pop().toLowerCase();
            if (AUTHORIZED_THUMBNAIL_EXTENSION.includes(`${extension}`)) {
              const newFile = {
                file: file,
                url: await URL.createObjectURL(file), // Generation de l'URL
                extension,
                mimeType: VIDSTACK_MIME_TYPES?.[extension],
                name: file?.name
              };
              setImageFileStructure(newFile);
            } else {
              launchApi({
                type: 'error',
                description: t('VideoFE.ThumbnailFileFormatNotSupportedExplanation', {
                  authorized: AUTHORIZED_THUMBNAIL_EXTENSION.join(', '),
                  extension: extension
                }),
                message: t('VideoFE.ThumbnailFileFormatNotSupportedTitle')
              });
            }
          }}
        />
      </div>
    </div>
  );
};

export default VidstackEditThumbnail;
