import {
  MUTATION_ADD_TYPE_TO_MODULE_QUICK_ACCESS,
  MUTATION_REMOVE_TYPE_FROM_MODULE_QUICK_ACCESS,
} from '@/shared/graphql/modules_quick_access.js';
import { useMutation } from '@apollo/client';
import { message, Select, Tag } from 'antd'
import React from 'react'
import { useTranslation } from 'react-i18next';

export const QuickAccessTypeManager = ({ typesQcm, moduleQuickAccessId, dataTypeQcm, contentTypeToShow, onChange = null }) => {
  const {t} = useTranslation();
  const [addTypeToModuleQuickAccess, addGroupData] = useMutation(MUTATION_ADD_TYPE_TO_MODULE_QUICK_ACCESS)
  const [removeTypeFromModuleQuickAccess, removeGroupData] = useMutation(MUTATION_REMOVE_TYPE_FROM_MODULE_QUICK_ACCESS)

  const eventTypes = dataTypeQcm?.data?.allTypeQcm?.filter(typeQcm => typeQcm?.contentType === contentTypeToShow)

  const groupeTagRender = ({ label, value, closable, onClose, key }) => (
    <Tag value={value} key={key} color="red" closable={closable} onClose={onClose} style={{ marginRight: 3 }}>
      {label}
    </Tag>
  )

  const handleSelect = async (_, option) => {
    try {
      if (onChange) {
        return
      }
      await addTypeToModuleQuickAccess({ variables: { moduleQuickAccessId, typeQcmId: option.key } })
      message.success(`Type ${option.value} ajouté`)
    } catch (e) {
      message.error(`Type ${option.value} n'a pas été ajouté`)
      console.error(e)
    }
  }
  const handleDeselect = async (_, option) => {
    try {
      if (onChange) {
        return
      }
      await removeTypeFromModuleQuickAccess({ variables: { moduleQuickAccessId, typeQcmId: option.key } })
      message.success(`Type ${option.value} enlevé`)
    } catch (e) {
      message.error(`Type ${option.value} n'a pas été enlevé`)
      console.error(e)
    }
  }

  return (
    <Select
      showArrow
      mode="multiple"
      style={{ maxWidth: '380px', minWidth: '380px' }}
      tagRender={groupeTagRender}
      placeholder={t('ChooseContentType')}
      defaultValue={typesQcm?.map(typeQuestion => typeQuestion.name)}
      loading={dataTypeQcm.loading || addGroupData.loading || removeGroupData.loading}
      options={eventTypes?.map(typeQcm => ({
        value: typeQcm.name,
        key: typeQcm.id,
      }))}
      onChange={onChange}
      onDeselect={handleDeselect}
      onSelect={handleSelect}
    />

  )
}