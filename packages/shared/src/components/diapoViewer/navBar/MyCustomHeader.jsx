import React, { useEffect, useRef, useState } from 'react';
import { Button, Popover, Select, Typography } from 'antd';
import { ChevronFirst, ChevronLeft, ChevronRight, Download } from 'lucide-react';
import { useDiapoContext } from '@/shared/components/diapoViewer/DiapoViewerContextProvider';
import { useTranslation } from 'react-i18next';
import { downloadFile, FILE_TYPE } from '@/shared/services/file';
import styled from 'styled-components';

const { Title } = Typography;

const MyCustomHeader = ({
  titleAndDescriptionChildren, // Le titre et la description children
  children // Le viewer de fichier
}) => {
  const { t } = useTranslation();
  const { viewerInput, setDocumentIndex, documentIndex, activeDocument, launchApi } =
    useDiapoContext();

  const ref = useRef(null);
  const IS_SMALL_CONSTANT = 600; // valeur en px, à laquelle on passe en 'small screen'
  const [isSmall, setIsSmall] = useState(false);
  const [isOpenSlideSelector, setIsOpenSlideSelector] = useState(false);
  const currentItem = viewerInput[documentIndex];

  useEffect(() => {
    if (!ref.current) return;

    const observer = new ResizeObserver(([entry]) => {
      if (entry.contentRect.width < IS_SMALL_CONSTANT) {
        setIsSmall(true);
      } else {
        setIsSmall(false);
      }
    });

    observer.observe(ref.current);

    return () => observer.disconnect();
  }, []);

  const onDownloadFunction = async () => {
    if (!activeDocument?.backname) {
      launchApi({
        type: 'error',
        description: t('DiapoFE.DiapoFeDownloadErrorMessageDescription'),
        message: t('DiapoFE.DiapoFeDownloadErrorMessageTitle')
      });
    } else {
      await downloadFile(FILE_TYPE.FILE, activeDocument?.backname);
    }
  };

  const mobilePreviousItemButton = (
    <Popover content={t('DiapoFE.Previous')}>
      <Button
        shape="circle"
        icon={<ChevronLeft style={{ fontSize: 16 }} />}
        onClick={() => setDocumentIndex((prev) => prev - 1)}
        disabled={documentIndex === 0}
        color="primary"
        variant="filled"
      />
    </Popover>
  );

  const firstItemButton = (
    <Popover content={t('DiapoFE.First')}>
      <Button
        shape="circle"
        icon={<ChevronFirst style={{ fontSize: 16 }} />}
        onClick={() => setDocumentIndex(0)}
        disabled={documentIndex === 0}
        color="primary"
        variant="filled"
      />
    </Popover>
  );

  const downloadButton = (
    <Popover content={t('DiapoFE.Download')}>
      <Button
        shape="circle"
        color="primary"
        variant="filled"
        icon={<Download style={{ fontSize: 16 }} />}
        onClick={async () => {
          await onDownloadFunction();
        }}
      />
    </Popover>
  );

  const displayValue = isOpenSlideSelector
    ? `${currentItem.title} - (${documentIndex + 1}/${viewerInput.length})`
    : `${documentIndex + 1}/${viewerInput.length}`;

  const pageSelecter = (
    <Select
      value={{ value: documentIndex, label: displayValue }}
      onChange={(idx) => setDocumentIndex(idx)}
      onOpenChange={setIsOpenSlideSelector}
      popupMatchSelectWidth={false}
    >
      {viewerInput.map(({ title }, idx) => (
        <Select.Option key={idx} value={idx}>
          <div style={{ display: 'flex' }}>
            <div
              style={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                display: 'flex',
                alignItems: 'center',
                gap: 4
              }}
            >
              {/* @TODO martin seen ? <CircleCheck /> : <Circle /> icones LUCIDE ET PAS ANTD, MERCI*/}
              {title}
            </div>
            &nbsp;- ({idx + 1}/{viewerInput.length})
          </div>
        </Select.Option>
      ))}
    </Select>
  );

  const mobileNextItemButton = (
    <Popover content={t('DiapoFE.Next')}>
      <Button
        shape="circle"
        icon={<ChevronRight style={{ fontSize: 16 }} />}
        onClick={() => setDocumentIndex((prev) => prev + 1)}
        disabled={documentIndex >= viewerInput.length - 1}
        color="primary"
        variant="filled"
      />
    </Popover>
  );

  const NavButtonLeft = styled.div`
    position: absolute;
    height: 100%;
    left: 0;
    width: 100px;
    background: linear-gradient(to left, transparent, #00000033);
    display: flex;
    align-items: center;
    justify-content: start;
    cursor: pointer;
    overflow: hidden;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      width: 100%;
      background: linear-gradient(to left, transparent, #00000066);
      opacity: 0;
      transition: opacity 0.3s ease;
      z-index: 0;
    }

    &:hover::after {
      opacity: 1;
    }

    > * {
      z-index: 1;
    }
  `;

  const NavButtonRight = styled.div`
    position: absolute;
    right: 0;
    height: 100%;
    width: 100px;
    background: linear-gradient(to right, transparent, #00000033);
    display: flex;
    align-items: center;
    justify-content: end;
    cursor: pointer;
    overflow: hidden;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      height: 100%;
      width: 100%;
      background: linear-gradient(to right, transparent, #00000066);
      opacity: 0;
      transition: opacity 0.3s ease;
      z-index: 0;
    }

    &:hover::after {
      opacity: 1;
    }

    > * {
      z-index: 1;
    }
  `;

  const desktopPreviousButton = (
    <Popover content={t('DiapoFE.Previous')} placement="right">
      <NavButtonLeft onClick={() => setDocumentIndex((prev) => prev - 1)}>
        <ChevronLeft style={{ fontSize: 50, color: 'white' }} />
      </NavButtonLeft>
    </Popover>
  );
  const desktopNextButton = (
    <Popover content={t('DiapoFE.Next')} placement="left">
      <NavButtonRight onClick={() => setDocumentIndex((prev) => prev + 1)}>
        <ChevronRight style={{ fontSize: 50, color: 'white' }} />
      </NavButtonRight>
    </Popover>
  );

  const slideTitle = (
    <Title
      level={4}
      style={{
        width: '100%',
        textAlign: 'center',
        color: 'grey',
        margin: '8px 0',
        overflow: 'hidden',
        textOverflow: 'ellipsis'
      }}
    >
      {/* @TODO martin seen ? <CircleCheck /> : <Circle /> icones LUCIDE ET PAS ANTD, MERCI*/}
      {activeDocument?.title}
    </Title>
  );
  return (
    <div ref={ref} style={{ width: '100%' }}>
      {isSmall === true && (
        <div style={{ display: 'flex', flexDirection: 'column', gap: 12 }}>
          {titleAndDescriptionChildren}
          <div
            style={{ display: 'flex', justifyContent: 'space-between', flexDirection: 'column' }}
          >
            <div style={{ flexGrow: 1 }}>{children}</div>

            {slideTitle}

            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                gap: 12,
                padding: '12px 0'
              }}
            >
              <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
                {firstItemButton}
                {mobilePreviousItemButton}
              </div>

              {activeDocument?.allowDownload && downloadButton}
              {pageSelecter}

              <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
                {mobileNextItemButton}
              </div>
            </div>
          </div>
        </div>
      )}

      {isSmall === false && (
        <div style={{ display: 'flex', flexDirection: 'column', gap: 12 }}>
          {titleAndDescriptionChildren}

          <div style={{ display: 'flex', flexDirection: 'column', gap: 12 }}>
            <div style={{ position: 'relative' }}>
              {documentIndex !== 0 && desktopPreviousButton}

              {documentIndex < viewerInput.length - 1 && desktopNextButton}

              {children}
            </div>

            {slideTitle}

            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                width: '100%',
                height: '100%',
                gap: '5px'
              }}
            >
              <div style={{ display: 'flex', alignItems: 'center', gap: 24 }}>
                {firstItemButton}
                {(activeDocument?.allowDownload || true) && downloadButton}
              </div>

              {pageSelecter}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MyCustomHeader;
