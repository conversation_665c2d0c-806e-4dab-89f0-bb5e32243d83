import { useQuery } from '@apollo/client'
import { GET_ME } from '@/shared/models/user'
import { getAvatarSrc } from '@/shared/services/user'
import {Avatar, Card, Col, Row, Spin} from 'antd'
import React from 'react'
import { UserOutlined } from '@ant-design/icons'
import {cardHeadStyleHome} from "@/shared/utils/utils";
import {isAdmin} from "@/shared/utils/authority";
import {adminCardActions} from "@/shared/pages/home";
import TwitterFeed from "@/shared/pages/home/<USER>/TwitterFeed";
import { useTranslation } from 'react-i18next';

export default function () {
  const {t} = useTranslation();
  const { data, loading, error } = useQuery(GET_ME, { fetchPolicy: 'no-cache' })

  return (
    <>
      <div>
        <Card style={{ marginTop: 16, textAlign: 'center' }}
              headStyle={cardHeadStyleHome}
              loading={loading}
              actions={isAdmin() ? adminCardActions(onEditReseauxSociaux) : undefined}
              title={t('general.SocialNetwork')}>
          <div style={{ height: 250, overflow: 'scroll' }}>
            <TwitterFeed
              // plus tard remplacer par getValueFromKeyConfigData(data.config, CONFIG_KEYS.TWITTER_ACCOUNT)
              twitterAccountUrl="https://twitter.com/MediboxMarseill?ref_src=twsrc%5Etfw"
            />
          </div>
        </Card>
      </div>
    </>
  )
}
