import { tr } from '@/shared/services/translate.js';
import {
  BgColorsOutlined,
  CalendarOutlined,
  CheckCircleOutlined,
  FileProtectOutlined,
  FileTextOutlined,
  LockOutlined,
  LogoutOutlined,
  MedicineBoxOutlined,
  MessageOutlined,
  TeamOutlined,
  UserOutlined
} from '@ant-design/icons';
import { Avatar, Menu, Spin } from 'antd';
import React, { useContext } from 'react';
import { useTranslation } from 'react-i18next';
import { router } from 'umi';
import { stringify } from 'querystring';
import styles from '@/components/GlobalHeader/index.less';
import { GET_ME } from '@/shared/models/user';
import { getPageQuery, isMobile } from '@/shared/utils/utils';
import { isAdmin, isTuteur } from '@/shared/utils/authority';
import { useApolloClient, useQuery } from '@apollo/client';
import {
  AdminRoleSimulator2000,
  deleteLocalUserData,
  getAvatarSrc,
  isRoleSimulated,
  isTuteurRoleSimulated,
  isUserRoleSimulated
} from '@/shared/services/user';
import HeaderDropdown from '../HeaderDropdown';
import { GlobalContext } from '@/shared/layouts/BlankLayout';
import { getApparenceAttribute } from '@/shared/services/config';

export const signOut = (client) => {
  deleteLocalUserData(client);
  router.push('/user/login');
};

const AvatarDropdown = (props) => {
  const { menu } = props;
  const client = useApolloClient();

  const { t } = useTranslation();

  const onMenuClick = (event) => {
    const { key } = event;
    if (key === 'admin') {
      router.push('/admin/dashboard');
      return;
    }
    if (key === 'admin-qcm') {
      router.push('/admin-series');
      return;
    }
    if (key === 'admin-questions') {
      router.push('/admin-questions');
      return;
    }
    if (key === 'admin-exams') {
      router.push('/admin-exams');
      return;
    }
    if (key === 'admin-events') {
      router.push('/admin-events');
      return;
    }
    if (key === 'tuteurpanel') {
      router.push('/tuteur-panel');
      return;
    }
    if (key === 'sujets-sans-reponses') {
      router.push('/tuteur-panel/sujets-sans-reponses');
      return;
    }
    if (key === 'messages') {
      router.push('/messages');
      return;
    }
    if (key === 'messages') {
      router.push('/messages');
      return;
    }
    if (key === 'admin-reports') {
      router.push('/admin-reports');
      return;
    }
    if (key === 'admin/schemas') {
      router.push('/admin-schemas');
      return;
    }
    if (key === 'simulTuteur') {
      AdminRoleSimulator2000.simulerCompteTuteur();
      router.push('/');
      return;
    }
    if (key === 'simulEtudiant') {
      AdminRoleSimulator2000.simulerCompteEtudiant();
      router.push('/');
      return;
    }
    if (key === 'backToAdmin') {
      AdminRoleSimulator2000.backToAdmin();
      router.push('/');
      return;
    }
    if (key === 'tuteurs') {
      router.push('/tuteurs');
      return;
    }
    if (key === 'logout') {
      deleteLocalUserData(client);
      const { redirect } = getPageQuery();
      // redirect if needed
      if (window.location.pathname !== '/user/login' && !redirect) {
        router.replace({
          pathname: '/user/login',
          search: stringify({
            redirect: window.location.href
          })
        });
      }
      return;
    }
    if (key === 'terms') {
      router.push('/terms');
      return;
    }
    router.push(`/account/${key}`);
  };

  const { appearance } = useContext(GlobalContext);
  const showSubscription = getApparenceAttribute(appearance, 'menuShowSubscription');
  const showTeam = getApparenceAttribute(appearance, 'navbarShowTeam');

  const navbarLabels = getApparenceAttribute(appearance, 'navbarLabels');

  const menuHeaderDropdown = (
    <Menu className={styles.menu} selectedKeys={[]} onClick={onMenuClick}>
      {menu && (
        <Menu.Item key="settings">
          <UserOutlined />
          {t('MyAccount')}
        </Menu.Item>
      )}

      {menu && isMobile && showTeam && (
        <Menu.Item key="tuteurs">
          <TeamOutlined />
          {navbarLabels?.[tr('Team')] || t('Team')}
        </Menu.Item>
      )}

      {menu && showSubscription && (
        <Menu.Item key="forfait">
          <MedicineBoxOutlined />
          {t('general.Subscription')}
        </Menu.Item>
      )}

      {menu && isAdmin() && <Menu.Divider />}
      {menu && isAdmin() && (
        <Menu.Item key="admin">
          <LockOutlined />
          {t('Administration')}
        </Menu.Item>
      )}

      {/*
      {menu && (isTuteur() || isAdmin()) && (
        <Menu.Item key="admin-qcm">
          <LockOutlined />
          Administration QCMs
        </Menu.Item>
      )}
      */}

      {menu && (isTuteur() || isAdmin()) && (
        <Menu.Item key="tuteurpanel">
          <UserOutlined />
          {t('TeacherPanel')}
        </Menu.Item>
      )}

      {menu && (isTuteur() || isAdmin()) && (
        <Menu.Item key="sujets-sans-reponses">
          <MessageOutlined />
          {t('Questions/Answers')}
        </Menu.Item>
      )}
      {menu && (isTuteur() || isAdmin()) && (
        <Menu.Item key="admin-qcm">
          <CheckCircleOutlined />
          {t('ExercisesSeries')}
        </Menu.Item>
      )}
      {menu && (isTuteur() || isAdmin()) && (
        <Menu.Item key={'admin/schemas'}>
          <BgColorsOutlined />
          {t('Schemas.SchemasLibrary')}
        </Menu.Item>
      )}

      {menu && (isTuteur() || isAdmin()) && (
        <Menu.Item key="admin-exams">
          <FileProtectOutlined />
          {t('Exams')}
        </Menu.Item>
      )}

      {menu && (isTuteur() || isAdmin()) && (
        <Menu.Item key="admin-events">
          <CalendarOutlined />
          {t('Events')}
        </Menu.Item>
      )}
      {menu && isAdmin() && (
        <Menu.Item key="simulTuteur" disabled={isTuteurRoleSimulated()}>
          <UserOutlined style={{ color: 'purple' }} />
          {t('SimulateTeacherAccount')}
        </Menu.Item>
      )}
      {menu && isAdmin() && (
        <Menu.Item key="simulEtudiant" disabled={isUserRoleSimulated()}>
          <UserOutlined style={{ color: 'blue' }} />
          {t('SimulateStudentAccount')}
        </Menu.Item>
      )}
      {menu && isRoleSimulated() && (
        <Menu.Item key="backToAdmin">
          <UserOutlined style={{ color: 'red' }} />
          {t('CancelSimulation')}
        </Menu.Item>
      )}

      {isMobile && (
        <Menu.Item key="terms">
          <FileTextOutlined />
          {t('TermsAndConditions')}
        </Menu.Item>
      )}

      {menu && <Menu.Divider />}
      <Menu.Item key="logout">
        <LogoutOutlined />
        {t('Logout')}
      </Menu.Item>
    </Menu>
  );

  const { loading, data: { me } = {}, error } = useQuery(GET_ME, { fetchPolicy: 'cache-first' });

  if (error) {
    /*
    signOut(client)
    return <Redirect to="/user/login"/>
    */
  }

  if (!loading && !error && me == null) {
    // me is null
    if (window.location.pathname !== '/user/login') {
      //console.log('in avatarDropdown cant get me');
      //return <Redirect to="/user/login"/>;
    }
  }

  return (
    <>
      {loading && (
        <Spin
          size="small"
          style={{
            marginLeft: 8,
            marginRight: 8
          }}
        />
      )}

      {!loading && me && (
        <HeaderDropdown overlay={menuHeaderDropdown}>
          <span className={`${styles.action} ${styles.account}`}>
            <Avatar
              size="small"
              className={styles.avatar}
              src={getAvatarSrc(me?.avatar)}
              alt="avatar"
              style={{ border: 'solid black 1px' }}
              icon={<UserOutlined />}
            />
            <span className={styles.name}>{me?.username}</span>
          </span>
        </HeaderDropdown>
      )}
    </>
  );
};

export default AvatarDropdown;
