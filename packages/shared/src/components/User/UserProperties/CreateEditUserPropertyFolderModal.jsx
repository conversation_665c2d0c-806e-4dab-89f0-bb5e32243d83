import { SmallErrorsAlert } from '@/shared/components/ErrorResult.jsx';
import {
  CREATE_USER_PROPERTY_FOLDER,
  DELETE_USER_PROPERTY_FOLDER,
  UPDATE_USER_PROPERTY_FOLDER,
} from '@/shared/graphql/user/user-properties.js';
import { GlobalContext } from '@/shared/layouts/BlankLayout.jsx';
import { ModalType } from '@/shared/pages/exam/admin/modals/CreateEditExamSessionModal.jsx';
import { getLanguageName, tr } from '@/shared/services/translate.js';
import { showGqlErrorsInMessagePopupFromException } from '@/shared/utils/utils.js';
import { useMutation } from '@apollo/client';
import { Button, Form, Input, message, Modal, Tabs } from 'antd';
import React, { useContext } from 'react';
import { useTranslation } from 'react-i18next';

export const CreateEditUserPropertyFolderModal = (
  {
    modalVisible,
    closeModalHandler,
    folder = null,
    modalType,
  },
) => {

  const getMutationFromModalType = (_modalType) => {
    switch (_modalType) {
      case ModalType.CREATE:
        return CREATE_USER_PROPERTY_FOLDER;
      case ModalType.UPDATE:
        return UPDATE_USER_PROPERTY_FOLDER;
      default:
        return UPDATE_USER_PROPERTY_FOLDER;
    }
  };

  const { t, i18n } = useTranslation();
  const [form] = Form.useForm();
  const [Mutation, { loading: loadingMut, data, error }] = useMutation(getMutationFromModalType(modalType));
  const [deleteMutation, { loading: loadingDelete}] = useMutation(DELETE_USER_PROPERTY_FOLDER);
  const { enabledLanguages } = useContext(GlobalContext);
  const loading = loadingDelete || loadingMut;

  const handleFinish = async (data) => {
    try {
      if (modalType === ModalType.UPDATE) {
        await Mutation({ variables: { id: folder?.id, input: data } });
        message.success(t('Updated'));
      } else {
        // Create
        await Mutation({ variables: { input: data } });
        message.success(t('Created'));
      }
      form.resetFields();
      await closeModalHandler();
    } catch (e) {
      showGqlErrorsInMessagePopupFromException(e);
      console.error(e);
    }
  };

  const handleDelete = async () => {
    try {
      await deleteMutation({ variables: { id: folder?.id } });
      form.resetFields();
      message.success(t('Deleted'));
      await closeModalHandler();
    }catch (e) {
      showGqlErrorsInMessagePopupFromException(e);
    }
  }

  return (
    <Modal
      open={modalVisible}
      onCancel={() => {
        // RESET FIELDS
        form.resetFields();
        closeModalHandler();
      }}
      footer={null}
      closable
    >
      {/* Show small error(s) if needed */}
      <SmallErrorsAlert error={error} loading={loading}/>
      <Form
        layout="vertical"
        onFinish={handleFinish}
        form={form}
        initialValues={{
          name: folder?.name,
          name_en: folder?.name_en,
          name_es: folder?.name_en,
          name_de: folder?.name_en,
          name_it: folder?.name_en,
        }}
      >

        <Tabs defaultActiveKey={i18n.language}>
          {enabledLanguages && enabledLanguages?.map((lang) => (
            <Tabs.TabPane tab={getLanguageName(lang)} key={lang}>
              <Form.Item
                name={tr('name', lang)}
                label={t('Title')}
              >
                <Input style={{ width: '100%' }} type="text"/>
              </Form.Item>
            </Tabs.TabPane>
          ))}
        </Tabs>

        <br />

        <Form.Item>
          <Button htmlType="submit" type="primary" loading={loading}>
            {modalType === ModalType.UPDATE ? t('Update') : t('Create')}
          </Button>
        </Form.Item>
        {modalType === ModalType.UPDATE && (
          <Form.Item>
            <Button danger onClick={handleDelete}>
              {t('Delete')}
            </Button>
          </Form.Item>
        )}

      </Form>

    </Modal>
  );
};