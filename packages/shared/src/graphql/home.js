import { CORE_FORMATION_ELEMENT_FIELDS } from '@/shared/graphql/formations.js';
import { gql } from '@apollo/client'

export const GET_CONFIG = gql`
    query Config($defaultOnly: Boolean) {
        config(defaultOnly: $defaultOnly) {
            id
            key
            value
            domain
        }
    }
`

export const GET_CUSTOM_MOBILE_CONFIG = gql`
    ${CORE_FORMATION_ELEMENT_FIELDS}
    query customMobileConfig($domain: String) {
        customMobileConfig(domain: $domain) {
            id
            backgroundImage
            elements {
                ...CoreFormationElementFields
            }
            type
        }
    }
`

export const MUTATION_CREATE_CONFIG = gql`
    mutation CreateConfig($key: String!, $value: String!, $file: Upload, $domain: String) {
        createConfig(key: $key, value: $value, file: $file, domain: $domain)
    }
`
export const MUTATION_UPDATE_CONFIG = gql`
    mutation UpdateConfig($key: String!, $value: String, $file: Upload, $domain: String) {
        updateConfig(key: $key, value: $value, file: $file, domain: $domain) {
            id
        }
    }
`
export const MUTATION_UPDATE_CONFIG_BY_ID = gql`
    mutation UpdateConfigById($id: ID!, $value: String!, $file: Upload, $domain: String) {
        updateConfigById(id: $id, value: $value, file: $file, domain: $domain) {
            id
        }
    }
`
export const MUTATION_DELETE_CONFIG_BY_ID = gql`
    mutation DeleteConfigById($id: ID!) {
        deleteConfigById(id: $id)
    }
`


export const MUTATION_UPLOAD_FILE = gql`
    mutation uploadFile($file: Upload!, $type: String!) {
        uploadFile(file: $file, type: $type)
    }
`

export const SET_ANNONCE_GENERALE = gql`
    mutation UpdateAnnonceGenerale($value: String) {
        updateAnnonceGenerale(value: $value) {
            key
            value
        }
    }
`

export const GET_STATISTIQUES = gql`
    query GlobalStats {
        statistiques {
            connected
            registered
            messages
            qcms
            cours
            fiches
            notions
        }
    }
`

/* --------- GLOBAL ANNOUNCES ------------------------------ */
export const QUERY_ALL_GLOBAL_ANNOUNCES = gql`
    query allGlobalAnnounces {
        allGlobalAnnounces {
            id
            text
            text_en
            text_es
            text_it
            text_de
            createdAt
            updatedAt
            groups {
                id
                name
                role
            }
        }
    }
`
export const QUERY_MY_GLOBAL_ANNOUNCES = gql`
    query myGlobalAnnounces {
        myGlobalAnnounces {
            id
            text
            text_en
            text_es
            text_it
            text_de
            createdAt
            updatedAt
        }
    }
`
// Global announce cRUD
export const MUTATION_CREATE_GLOBAL_ANNOUNCE = gql`
    mutation createGlobalAnnounce($input: GlobalAnnounceInput) {
        createGlobalAnnounce(input: $input)
    }
`
export const MUTATION_UPDATE_GLOBAL_ANNOUNCE = gql`
    mutation updateGlobalAnnounce($input: GlobalAnnounceInput) {
        updateGlobalAnnounce(input: $input)
    }
`
export const MUTATION_DELETE_GLOBAL_ANNOUNCE = gql`
    mutation deleteGlobalAnnounce($input: GlobalAnnounceInput) {
        deleteGlobalAnnounce(input: $input)
    }
`

export const ADD_GROUP_TO_GLOBAL_ANNOUNCE = gql`
    mutation addGroupToGlobalAnnounce($id: ID, $groupId: ID) {
        addGroupToGlobalAnnounce(id: $id, groupId: $groupId)
    }
`
export const MUTATION_REMOVE_GROUP_GLOBAL_ANNOUNCE = gql`
    mutation removeGroupFromGlobalAnnounce($id: ID, $groupId: ID) {
        removeGroupFromGlobalAnnounce(id: $id, groupId: $groupId)
    }
`
/*-------------------------------------------------------------*/

/* Années */
export const GET_ANNEES = gql`
    query AllAnnees {
        annees {
            id
            annee
        }
    }
`
export const MUTATION_CREATE_ANNEE = gql`
    mutation createAnnee($annee: Int) {
        createAnnee(annee: $annee)
    }
`
export const MUTATION_REMOVE_ANNEE = gql`
    mutation deleteAnnee($id: ID!) {
        deleteAnnee(id: $id)
    }
`
