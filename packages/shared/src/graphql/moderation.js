import { gql } from '@apollo/client';

// ===== QUERIES SIMPLIFIÉES =====

export const GET_MODERATION_QUEUE = gql`
  query GetModerationQueue($filters: ModerationQueueFilters) {
    getModerationQueue(filters: $filters) {
      id
      titre
      contenu
      date_creation
      date_modif
      is_published
      moderation_status
      ai_generated_title
      moderated_by
      moderated_at
      parentId
      context_id
      author: user {
        id
        nom
        email
      }
      parent {
        id
        titre
      }
    }
  }
`;

export const GET_MODERATION_QUEUE_COUNT = gql`
  query GetModerationQueueCount($contextId: ID) {
    getModerationQueueCount(contextId: $contextId)
  }
`;

export const GET_FORUM_PUBLICATION_SETTINGS = gql`
  query GetForumPublicationSettings($contextId: ID!) {
    getForumPublicationSettings(contextId: $contextId) {
      id
      context_id
      moderation_mode
      auto_publish_trusted
      require_approval_new_users
      additional_rules
      created_at
      updated_at
    }
  }
`;

export const GET_MODERATION_HISTORY = gql`
  query GetModerationHistory($postId: ID!) {
    getModerationHistory(postId: $postId) {
      id
      post_id
      moderator_id
      action
      previous_status
      new_status
      reason
      metadata
      created_at
      moderator {
        id
        nom
        email
      }
    }
  }
`;

export const GET_MODERATION_STATS = gql`
  query GetModerationStats($contextId: ID, $dateRange: DateRangeInput) {
    getModerationStats(contextId: $contextId, dateRange: $dateRange) {
      totalActions
      actionBreakdown {
        action
        count
      }
      contextId
      dateRange {
        start
        end
      }
    }
  }
`;

export const GET_VISIBLE_POSTS = gql`
  query GetVisiblePosts($contextId: ID!, $includeComments: Boolean) {
    getVisiblePosts(contextId: $contextId, includeComments: $includeComments) {
      id
      titre
      contenu
      date_creation
      date_modif
      is_published
      moderation_status
      parentId
      context_id
      author: user {
        id
        nom
        email
      }
      parent {
        id
        titre
      }
    }
  }
`;

// ===== MUTATIONS SIMPLIFIÉES =====

export const APPROVE_POST = gql`
  mutation ApprovePost($postId: ID!, $reason: String) {
    approvePost(postId: $postId, reason: $reason) {
      success
      post {
        id
        title
        is_published
        moderation_status
        moderated_by
        moderated_at
      }
      message
    }
  }
`;

export const REJECT_POST = gql`
  mutation RejectPost($postId: ID!, $reason: String) {
    rejectPost(postId: $postId, reason: $reason) {
      success
      post {
        id
        title
        is_published
        moderation_status
        moderated_by
        moderated_at
      }
      message
    }
  }
`;

export const BULK_MODERATE = gql`
  mutation BulkModerate($postIds: [ID!]!, $action: ModerationAction!, $reason: String) {
    bulkModerate(postIds: $postIds, action: $action, reason: $reason) {
      success
      successCount
      errorCount
      results {
        postId
        success
        post {
          id
          titre
          is_published
          moderation_status
        }
        error
      }
    }
  }
`;

export const BULK_PUBLISH_POSTS = gql`
  mutation BulkPublishPosts($postIds: [ID!]!, $reason: String) {
    bulkPublishPosts(postIds: $postIds, reason: $reason) {
      success
      successCount
      errorCount
      results {
        postId
        success
        post {
          id
          title
          is_published
          moderation_status
        }
        error
      }
    }
  }
`;

export const BULK_UNPUBLISH_POSTS = gql`
  mutation BulkUnpublishPosts($postIds: [ID!]!, $reason: String) {
    bulkUnpublishPosts(postIds: $postIds, reason: $reason) {
      success
      successCount
      errorCount
      results {
        postId
        success
        post {
          id
          title
          is_published
          moderation_status
        }
        error
      }
    }
  }
`;

export const BULK_DELETE_POSTS = gql`
  mutation BulkDeletePosts($postIds: [ID!]!, $reason: String) {
    bulkDeletePosts(postIds: $postIds, reason: $reason) {
      success
      deletedPostsCount
      deletedCommentsCount
      errorCount
      results {
        postId
        success
        deletedPosts
        deletedComments
        error
      }
    }
  }
`;

export const DELETE_POST_CASCADE = gql`
  mutation DeletePostCascade($postId: ID!, $reason: String) {
    deletePostCascade(postId: $postId, reason: $reason) {
      success
      deletedPosts
      deletedComments
    }
  }
`;

export const UPDATE_FORUM_PUBLICATION_SETTINGS = gql`
  mutation UpdateForumPublicationSettings($contextId: ID!, $settings: ForumPublicationSettingsInput!) {
    updateForumPublicationSettings(contextId: $contextId, settings: $settings) {
      id
      context_id
      moderation_mode
      auto_publish_trusted
      require_approval_new_users
      additional_rules
      created_at
      updated_at
    }
  }
`;

export const GENERATE_AI_TITLE_FROM_CONTENT = gql`
  mutation GenerateAITitleFromContent($content: String!) {
    generateAITitleFromContent(content: $content) {
      success
      title
      confidence
      error
    }
  }
`;

export default {
  // Queries
  GET_MODERATION_QUEUE,
  GET_MODERATION_QUEUE_COUNT,
  GET_FORUM_PUBLICATION_SETTINGS,
  GET_MODERATION_HISTORY,
  GET_MODERATION_STATS,
  GET_VISIBLE_POSTS,
  
  // Mutations
  APPROVE_POST,
  REJECT_POST,
  BULK_MODERATE,
  BULK_PUBLISH_POSTS,
  BULK_UNPUBLISH_POSTS,
  BULK_DELETE_POSTS,
  DELETE_POST_CASCADE,
  UPDATE_FORUM_PUBLICATION_SETTINGS,
  GENERATE_AI_TITLE_FROM_CONTENT
};
