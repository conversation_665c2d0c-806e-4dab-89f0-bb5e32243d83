'use strict';
const scheduledTasks = (sequelize, DataTypes) => {
  const ScheduledTasks = sequelize.define('scheduled_tasks', {
    status: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'pending' // pending, running, success, failed
    },
    executionDate: {
      type: DataTypes.DATE(6),
      allowNull: false
    },

    name: {
      type: DataTypes.STRING,
      allowNull: true
    },

    state: {
      type: DataTypes.JSON,
      defaultValue: {},
      set(value) {
        try{
          this.setDataValue('state', JSON.stringify(value))
        } catch(e){
          console.error("error set :",e)
        }
      },
      get() {
        try {
          const rawValue = this.getDataValue('state')
          return rawValue ? JSON.parse(rawValue) : {}
        } catch (e) {
          console.error("error :",e)
          return {}
        }
      },
    },

  }, { tableName: 'scheduled_tasks' });

  ScheduledTasks.associate = models => {
    ScheduledTasks.belongsTo(models.Groupe, { foreignKey: 'groupId' });

    ScheduledTasks.belongsToMany(models.Groupe, {
      foreignKey: 'scheduledTaskId',
      through: models.ScheduledTasksGroups,
      as: 'groupsState'
    });

    ScheduledTasks.belongsTo(models.FormsUserCompletion, {
      foreignKey: 'elementId' });
    ScheduledTasks.belongsTo(models.User, { foreignKey: 'userId' });
  };

  return ScheduledTasks;
};

export default scheduledTasks;
