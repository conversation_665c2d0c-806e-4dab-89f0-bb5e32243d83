'use strict'

const mathpix = (sequelize, DataTypes) => {
  const Mathpix = sequelize.define('mathpix', {
    // id qui est la primaryKey
    hash:{type:DataTypes.STRING,allowNull:false}, // Le hash du fichier
    algorithm:{type:DataTypes.STRING,allowNull:false}, // L'algorithme qui a créé le hash
    mathpixFileId:{type:DataTypes.STRING,allowNull:false}, // l'ID de mathpix associé au hash
    file:{type:DataTypes.STRING,allowNull:false}, // le path du fichier
    description:{type:DataTypes.TEXT,allowNull:true}, // une description que peut fournir l'utilisateur
    image:{type:DataTypes.STRING,allowNull:true}, // Une image que peut fournir l'utilisateur
    mathpixConfigId:{type:DataTypes.INTEGER,allowNull:true}, // Le mathpixConfigId => contient le token d'identification // On synchronise dans le modèle config
    fileName:{type:DataTypes.STRING,allowNull:false}, // Le nom du fichier
  },{
    indexes:[
      {
        unique:true,
        fields:["hash","mathpixConfigId"],
      }
    ]
  })

  Mathpix.associate = models => {
    Mathpix.belongsTo(models.Config,{ // Un pdf a uniquement un account mathpix associé
      foreignKey:'mathpixConfigId',
      onUpdate:'CASCADE', // Normalement ne sera pas update
      onDelete:'SET NULL'
    }),
    Mathpix.belongsTo(models.User, { foreignKey: 'authorId',as:"mathpixieAuthor"});
    Mathpix.hasMany(models.Question,{
      foreignKey:"mathpixieId",
      as:"linkedQuestions",
      onUpdate:'CASCADE',
      onDelete:'SET NULL',
    })
  }
  return Mathpix
}

export default mathpix