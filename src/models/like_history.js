'use strict'

export const LIKE_HISTORY_TYPE = {
  POST_LIKE: 'POST_LIKE',
  POST_DISLIKE: 'POST_DISLIKE',
  REVIEW_LIKE:'REVIEW_LIKE',
  REVIEW_DISLIKE:'REVIEW_DISLIKE'
}

const likeHistory = (sequelize, DataTypes) => {
  const LikeHistory = sequelize.define('like_history', {
    type: { type: DataTypes.STRING },
  })

  LikeHistory.associate = models => {
    LikeHistory.belongsTo(models.Post) // postId
    LikeHistory.belongsTo(models.User) // userId
    LikeHistory.belongsTo(models.Review) // reviewId
  }

  return LikeHistory
}

export default likeHistory
