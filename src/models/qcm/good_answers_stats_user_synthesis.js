module.exports = function(sequelize, DataTypes) {
  const GoodAnswersStatsUserSynthesis = sequelize.define('good_answers_stats_user_synthesis', {

    // Stats sur les réponses correctes agrégées par UE, UECategory, Cours, Notion
    goodAnswers: { type: DataTypes.INTEGER, defaultValue: null },
    badAnswers: { type: DataTypes.INTEGER, defaultValue: null },
    isUpdating: { type: DataTypes.BOOLEAN, defaultValue: false },
    pointsObtained: { type: DataTypes.FLOAT, defaultValue: null },
    pointsMax: { type: DataTypes.FLOAT, defaultValue: null },

  }, {
    tableName: 'good_answers_stats_user_synthesis',
  })
  GoodAnswersStatsUserSynthesis.associate = models => {
    GoodAnswersStatsUserSynthesis.belongsTo(models.User, { foreignKey: 'userId' })
    GoodAnswersStatsUserSynthesis.belongsTo(models.UE, { foreignKey: 'ueId' })
    GoodAnswersStatsUserSynthesis.belongsTo(models.UECategory, { foreignKey: 'ueCategoryId' })
    GoodAnswersStatsUserSynthesis.belongsTo(models.Cours, { foreignKey: 'coursId' })
    GoodAnswersStatsUserSynthesis.belongsTo(models.Notion, { foreignKey: 'notionId' })

  }

  return GoodAnswersStatsUserSynthesis
}
