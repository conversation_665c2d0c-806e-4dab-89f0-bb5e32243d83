'use strict';
const userTimeSpentByUE = (sequelize, DataTypes) => {
  const UserTimeSpentByUE = sequelize.define('user_time_spent_by_ue', {
    time: { type: DataTypes.INTEGER, defaultValue: 0 }, // in seconds, time spent

  }, {
    tableName: 'user_time_spent_by_ue',
  });

  UserTimeSpentByUE.associate = models => {
    UserTimeSpentByUE.belongsTo(models.User, { foreignKey: 'userId' });
    UserTimeSpentByUE.belongsTo(models.UE, { foreignKey: 'ueId' });
    UserTimeSpentByUE.belongsTo(models.UECategory, { foreignKey: 'ueCategoryId' });
    UserTimeSpentByUE.belongsTo(models.Cours, { foreignKey: 'coursId' });
  };

  return UserTimeSpentByUE;
};

export default userTimeSpentByUE;
