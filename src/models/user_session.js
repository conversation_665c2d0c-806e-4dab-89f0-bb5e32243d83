import bcrypt from 'bcrypt'
import _ from 'underscore'

'use strict'
const userSession = (sequelize, DataTypes) => {
  const UserSession = sequelize.define('user_session', {
    lastIp: { type: DataTypes.STRING },
    userAgent: { type: DataTypes.TEXT },
    device: { type: DataTypes.TEXT },
    visible: { type: DataTypes.BOOLEAN, defaultValue: true },
    isAdminSession: { type: DataTypes.BOOLEAN, defaultValue: false }
  })

  UserSession.associate = models => {
    UserSession.belongsTo(models.User)
  }

  return UserSession
}

export default userSession
