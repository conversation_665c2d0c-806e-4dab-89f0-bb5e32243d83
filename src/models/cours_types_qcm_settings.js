'use strict'

export const CoursTypesQcmSettings_MODULE_TYPES = {
  qcmSeries: 'qcmSeries',
  training: 'training',
  revision: 'revision',
  events: 'events',
  schemas: 'schemas'
}

export const CoursTypesQcmSettings_MODULE_TYPES_array = [
  CoursTypesQcmSettings_MODULE_TYPES.qcmSeries,
  CoursTypesQcmSettings_MODULE_TYPES.training,
  CoursTypesQcmSettings_MODULE_TYPES.revision,
  CoursTypesQcmSettings_MODULE_TYPES.events,
  CoursTypesQcmSettings_MODULE_TYPES.schemas
]

const coursTypesQcmSettings = (sequelize, DataTypes) => {
  const CoursTypesQcmSettings = sequelize.define('cours_types_qcm_settings', {
    coursModuleType: { type: DataTypes.STRING, defaultValue: null },
  })

  CoursTypesQcmSettings.associate = models => {
    CoursTypesQcmSettings.belongsTo(models.Cours, { foreignKey: 'coursId' })
    CoursTypesQcmSettings.belongsTo(models.TypeQcm, { foreignKey: 'typeQcmId' })
  }

  return CoursTypesQcmSettings
}

export default coursTypesQcmSettings
