
const userPropertiesData = (sequelize, DataTypes) => {
  const UserPropertiesData = sequelize.define('user_properties_data', {
    value: { type: DataTypes.STRING, defaultValue: null },

    values: {
      type: DataTypes.JSON,
      defaultValue: null,
      set(value) {
        this.setDataValue('values', JSON.stringify(value))
      },
      get() {
        try {
          const rawValue = this.getDataValue('values')
          return rawValue ? JSON.parse(rawValue) : []
        } catch (e) {
          return []
        }
      },
    },

  });

  UserPropertiesData.associate = models => {
    UserPropertiesData.belongsTo(models.FormationElement, { foreignKey: 'elementId' });
    UserPropertiesData.belongsTo(models.User, { foreignKey: 'userId' });
    UserPropertiesData.belongsTo(models.Log, { foreignKey: 'logObjectId' });
    UserPropertiesData.belongsTo(models.Forms, { foreignKey: 'formId' });

  };

  return UserPropertiesData;
};

export default userPropertiesData;
