import { platform } from 'node:os'
import { ELEMENTS_TYPE } from '../../models/formation/formation_element.js';
import models from '../../models/index.js';
import { HubspotService } from '../config/hubspot/HubspotService.js';
const { JSDOM } = require('jsdom');
import { Op } from 'sequelize';

export const FormationElementMigrations = {

  async autoCreateHubspotIdsForElements() {
    // Tous les inputs elements
    const elements = await models.FormationElement.findAll({
        where: {
          type: [
            ELEMENTS_TYPE.SHORT_ANSWER,
            ELEMENTS_TYPE.LONG_ANSWER,
            ELEMENTS_TYPE.INTEGER_NUMBER,
            ELEMENTS_TYPE.FLOAT_NUMBER,
            ELEMENTS_TYPE.SINGLE_SELECT,
            ELEMENTS_TYPE.MULTIPLE_SELECT,
            ELEMENTS_TYPE.DATE_PICKER,
            ELEMENTS_TYPE.DATE_AND_TIME_PICKER,
            ELEMENTS_TYPE.RADIO_CHOICE_ANSWER,
            ELEMENTS_TYPE.CHECKBOXES_ANSWER
          ]
        }
      }
    );
    for (const element of elements) {
      let settings = element.settings;
      if (!settings) {
        settings = {};
      }
      settings.hubspotProperty = HubspotService.createSafeHubspotPropertyName(element);
      await element.update({ settings });
    }
    return true;

  },


  async duplicateEmbedToNewVidstackComponant(){
    try {
      // Récupérer les FE de type vidéo
      const elementsToImport = await models.FormationElement.findAll({
        where:{type:"video"}
      })
      console.log("nombre d'éléments vidéo concernés par la migration :",elementsToImport?.length)

      // Fonction helper parsing time vimeo
      function timeToSeconds(raw = '') {
        if (!raw) return 0;                      // chaîne vide
        if (/^\d+$/.test(raw)) return parseInt(raw, 10); // cas « 90 »

        // cas « 1h20m15s », « 3m5s », etc.
        const [, h = '0', m = '0', s = '0'] =
        raw.match(/(?:(\d+)h)?(?:(\d+)m)?(?:(\d+)s)?/) || [];

        return (+h) * 3600 + (+m) * 60 + (+s);
      }


      function getVideoPlatform(hostname) {
        const YT_HOST_RE   = /(^|\.)youtu\.be$|(^|\.)youtube(?:-nocookie)?\.com$/i;
        const VIMEO_HOST_RE = /(^|\.)vimeo\.com$/i;

        if (YT_HOST_RE.test(hostname))   return 'youtube';
        if (VIMEO_HOST_RE.test(hostname)) return 'vimeo';
        return null;
      }

      // Parser import Vimeo
      function parseYoutubeOrVimeoIframe(iframeString){
        const dom = new JSDOM(iframeString || "");
        const iframe = dom.window.document.querySelector("iframe");
        if (!iframe) return null;


        // 2. Récupérer les attributs
        const src = iframe.getAttribute('src') || '';
        const width = iframe.getAttribute('width') || '';
        const title = iframe.getAttribute('title') || '';

        // 3. Vérifier si le fullscreen est autorisé (présence de l'attribut allowfullscreen)
        const fullScreen = iframe.hasAttribute("allowfullscreen");

        // 4. Récupérer les autorisations (autoplay, picture-in-picture, etc.)
        const allowAttr = iframe.getAttribute("allow") || "";

        // Pour le PiP, on vérifie si "picture-in-picture" est présent
        const PiP = allowAttr.includes("picture-in-picture");

        // Pour l'autoplay, on vérifie soit l'attribut allow, soit un éventuel paramètre 'autoplay=1' dans l'URL
        const autoplay = allowAttr.includes("autoplay") || src.includes("autoplay=1");

        // ----------- Difference Youtube/Vimeo => youtube : 'start' / vimeo : 't' -------------
        let start = 0;
        let platform=null;
        try {
          const urlObj = new URL(src, "https://example.com");
          platform = getVideoPlatform(urlObj.hostname);

          // YouTube : ?start=90
          const startParam = urlObj.searchParams.get("start");
          if (startParam !== null){start = parseInt(startParam, 10) || 0;}


          // Vimeo : ?t=90 ou ?t=1m30s
          const tParam = urlObj.searchParams.get("t");
          if (tParam !== null){start = timeToSeconds(tParam);}



          // Vimeo : ...#t=1m30s
          if (!start && urlObj.hash.startsWith("#t=")){start = timeToSeconds(urlObj.hash.slice(3));}

        } catch (_) {
          /* Si erreur, on ignore */
        }
        return {platform, src, width, title, fullScreen, PiP, autoplay, start };
      }

      let count=0
      let importedCount=0
      let notImportedCound=0


      for (const element of elementsToImport) {
        count+=1

        /////// Parse de l'élément, voir si déjà il est compatible avec l'importation
        const parsed = parseYoutubeOrVimeoIframe(element.text || "");
        if (!parsed?.platform){
          console.warn("Plate-forme non trouvée (pas youtube ni viméo), skip de cet élément. => element.text : ",element?.text)
          notImportedCound+=1
          continue;
        }

        ////////// Gestion et creation du backup
        const backupPlainElement = element.get({ plain: true }); // get de la copie
        delete backupPlainElement.id; // modif de l'ID
        delete backupPlainElement.createdAt // Pour le dev, mais à retirer sinon
        delete backupPlainElement.updatedAt // pour le dev mais à retirer sinon
        backupPlainElement.type="migrationVideoToVidstackBackupType"
        backupPlainElement.embedToVidstackBackupId = element.id;
        await models.FormationElement.create(backupPlainElement);

        /////////// Modif de l'original


        // creation du new settings depuis le parsing
        const newSettings = element.settings || {};
        if (parsed?.src) newSettings.url = parsed.src;
        if (parsed?.width) newSettings.vidStackVideoWidth = parseInt(parsed.width, 10);
        if (parsed?.fullScreen) newSettings.vidstackAuthorizeFullScreen = parsed.fullScreen;
        if (parsed?.PiP) newSettings.vidstackAuthorizePictureInPicture = parsed.PiP;
        if (parsed?.autoplay) newSettings.vidstackAuthorizeAutoPlay = false ;//parsed.autoplay;
        if (parsed?.start != null) newSettings.vidstackClipStartTime = parsed.start;
        //if (parsed?.title) newSettings.vidstackTitle = parsed.title;

        ///// Si vimeo
        // => Le titre est customizable donc plus important. Si présence dans l'embed, on le met en titre de l'élément et on concatène le titre+description de l'élément en description
        // => Si pas de titre dans l'embed, alors on laisse le titre et description de l'élément comme référence
        if (parsed?.platform==="vimeo" && (typeof parsed.title === "string" && parsed.title!=="")){
          newSettings.vidstackTitle=parsed.title

          let description=""
          if(element?.name){description=description+element.name}
          if(element?.description){description = description + element.description}
          newSettings.vidstackQuillDescription=description

        } else {
          if(element?.name){newSettings.vidstackTitle=element.name}
          if(element?.description){newSettings.vidstackQuillDescription=element.description}
        }

        // Update de l'original avec le new settings
        await models.FormationElement.update(
          {
            type: ELEMENTS_TYPE.VIDSTACK_VIDEO,
            settings: newSettings,
          },
          { where: { id: element.id } }
        );
        importedCount+=1
      }

      console.log("Eléments totaux à migrer :",elementsToImport.length)
      console.log("Éléments ayant migrés :",importedCount)
      console.log("Éléments skiped : ",notImportedCound)
    } catch(e){
      console.error("error dans duplicateEmbedToNEwVidstackComponant :",e)
    }
  },

  async RevertDuplicateEmbed(){
    try {
      // Get des elements à revert
      const elementsToRevert = await models.FormationElement.findAll({
        where: {
          type: "migrationVideoToVidstackBackupType",
          embedToVidstackBackupId: {
            [Op.ne]: null,
          },
        },
      });

      console.log("Éléments à revert : ",elementsToRevert.length)

      // Pour chaqu'un des éléments :
      for (const backupElement of elementsToRevert){

        // Get de la sauvegarde
        const backupPlain = backupElement.get({ plain: true });
        backupPlain.type=ELEMENTS_TYPE.VIDEO

        // Save des Id, et de embed backup et du id normal
        const oldBackupId=backupPlain.embedToVidstackBackupId
        const oldIdToDelete=backupPlain.id

        // Delete du backup les deux id
        delete backupPlain.embedToVidstackBacupId
        delete backupPlain.id



        await models.FormationElement.update(backupPlain, {
            where: { id: oldBackupId },
        });


        await models.FormationElement.destroy({
          where: { id: oldIdToDelete }
        });
      }

      console.log("Revert terminé !");

      const elements = await models.FormationElement.findAll({
        where:{type:"video"}
      })
      console.log("nombre d'éléments de type vidéo après migration :",elements.length)

    }catch(e){
      console.error("error dans revert duplicate :",e)
    }
  }
};