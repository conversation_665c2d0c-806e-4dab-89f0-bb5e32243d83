import { combineResolvers } from 'graphql-resolvers';
import { isTuteurOrAdmin } from '../authorization';
import { ModerationService } from '../../services/ModerationService.js';

export default {
  Mutation: {
    // ===== INDIVIDUAL ACTIONS =====
    
    approvePost: combineResolvers(
      isTuteurOrAdmin,
      async (parent, { postId, reason }, { me }) => {
        try {
          const result = await ModerationService.approvePost(postId, me.id, reason);
          return {
            success: result.success,
            post: result.post,
            message: 'Post approuvé avec succès'
          };
        } catch (error) {
          console.error('Erreur lors de l\'approbation:', error);
          return {
            success: false,
            post: null,
            message: error.message
          };
        }
      }
    ),

    rejectPost: combineResolvers(
      isTuteurOrAdmin,
      async (parent, { postId, reason }, { me }) => {
        try {
          const result = await ModerationService.rejectPost(postId, me.id, reason);
          return {
            success: result.success,
            post: result.post,
            message: 'Post rejeté avec succès'
          };
        } catch (error) {
          console.error('Erreur lors du rejet:', error);
          return {
            success: false,
            post: null,
            message: error.message
          };
        }
      }
    ),

    // ===== BULK ACTIONS =====
    
    bulkPublishPosts: combineResolvers(
      isTuteurOrAdmin,
      async (parent, { postIds, reason }, { me }) => {
        try {
          const result = await ModerationService.bulkApprove(postIds, me.id, reason);
          return result;
        } catch (error) {
          console.error('Erreur lors de l\'approbation en masse:', error);
          return {
            success: false,
            successCount: 0,
            errorCount: postIds.length,
            results: postIds.map(postId => ({
              postId,
              success: false,
              error: error.message
            }))
          };
        }
      }
    ),

    bulkUnpublishPosts: combineResolvers(
      isTuteurOrAdmin,
      async (parent, { postIds, reason }, { me }) => {
        try {
          const result = await ModerationService.bulkReject(postIds, me.id, reason);
          return result;
        } catch (error) {
          console.error('Erreur lors du rejet en masse:', error);
          return {
            success: false,
            successCount: 0,
            errorCount: postIds.length,
            results: postIds.map(postId => ({
              postId,
              success: false,
              error: error.message
            }))
          };
        }
      }
    ),

    bulkDeletePosts: combineResolvers(
      isTuteurOrAdmin,
      async (parent, { postIds, reason }, { me }) => {
        try {
          const result = await ModerationService.bulkDelete(postIds, me.id, reason);
          return result;
        } catch (error) {
          console.error('Erreur lors de la suppression en masse:', error);
          return {
            success: false,
            deletedPostsCount: 0,
            deletedCommentsCount: 0,
            errorCount: postIds.length,
            results: postIds.map(postId => ({
              postId,
              success: false,
              error: error.message
            }))
          };
        }
      }
    ),

    deletePostCascade: combineResolvers(
      isTuteurOrAdmin,
      async (parent, { postId, reason }, { me }) => {
        try {
          const result = await ModerationService.deletePostCascade(postId, me.id, reason);
          return result;
        } catch (error) {
          console.error('Erreur lors de la suppression cascade:', error);
          return {
            success: false,
            deletedPosts: 0,
            deletedComments: 0
          };
        }
      }
    )
  }
};
