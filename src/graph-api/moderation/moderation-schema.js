import gql from 'graphql-tag';

export default gql`
  # ===== RESULT TYPES =====
  
  type ApprovalResult {
    success: Boolean!
    post: Post
    message: String
  }
  
  type BulkModerationResult {
    success: Boolean!
    successCount: Int!
    errorCount: Int!
    results: [BulkActionResult!]!
  }
  
  type BulkActionResult {
    postId: ID!
    success: Boolean!
    post: Post
    error: String
  }
  
  type BulkDeleteResult {
    success: Boolean!
    deletedPostsCount: Int!
    deletedCommentsCount: Int!
    errorCount: Int!
    results: [DeleteActionResult!]!
  }
  
  type DeleteActionResult {
    postId: ID!
    success: Boolean!
    deletedPosts: Int
    deletedComments: Int
    error: String
  }

  # ===== MUTATIONS =====
  
  extend type Mutation {
    # ===== INDIVIDUAL ACTIONS =====
    approvePost(postId: ID!, reason: String): ApprovalResult!
    rejectPost(postId: ID!, reason: String): ApprovalResult!
    
    # ===== BULK ACTIONS =====
    bulkPublishPosts(postIds: [ID!]!, reason: String): BulkModerationResult!
    bulkUnpublishPosts(postIds: [ID!]!, reason: String): BulkModerationResult!
    bulkDeletePosts(postIds: [ID!]!, reason: String): BulkDeleteResult!
  }
`;
