import gql from 'graphql-tag'

export default gql`

  extend type Query {
    "Permet d'avoir la PostLimitationRule pour l'user en question"
    getMyPostLimitationRule:LimitRule
    
    "Permet d'avoir la PostLimitationRule pour un userId"
    postLimitationRuleForUserId(userId:ID!):LimitRule
    
    "Permet d'avoir la PostLimitationRule pour une ruleId"
    postLimitationRuleWithId(id:ID!):LimitRule
    
    "query to search if the current user can post a discussion.   Retourne le nombre de secondes à attendre avant que l'user puisse de nouveau poser une question. -1 si disabled"
    canPostDiscussion:Int!
    
    "Admin query to search if a user can post a discussion. Retourne le nombre de secondes à attendre avant que l'user puisse de nouveau poser une question. -1 si disabled"
    adminCanUserIdPostDiscussion(userId:ID):Int!
  }

  extend type Mutation {
    # CRUD rules
    createPostLimitationRule(limitRuleInput:LimitRuleInput!):LimitRule
    updatePostLimitationRule(id:ID!,limitRuleInput:LimitRuleInput!):Boolean!
    deletePostLimitationRule(id:ID!):Boolean
  }

  
  
  "Fields of LimitRule"
  type LimitRule{
    id:ID
    userId:ID
    limitNumber:Int
    isActive:Boolean
    timeWindow:Int
    createdAt:Date
    updatedAt:Date
  }
  
  "Input Fields of LimitRuleInput"
  input LimitRuleInput{
    userId:ID
    limitNumber:Int
    isActive:Boolean
    timeWindow:Int
  }
`