import { GraphQLError } from 'graphql';
import moment from 'moment'
import { Op } from 'sequelize'
import models from '../../../models/index.js'
import { LOG_OPERATIONS } from '../../../models/log/log.js'
import { ROLES } from '../../authorization.js'
import LogService from '../../log/log-service.js'


export const PostLimitationRuleService = {

  /// CRUD OPERATIONS :
  createPostLimitationRule:async({limitRuleInput},{me,ip,req})=>{
    try{
      // Verif de si l'user existe
      const user = await models.User.findByPk(limitRuleInput?.userId)
      if (!user){throw new Error(`L'user pour createPostLimitationRule n'est pas reconnu. UserId input : ${limitRuleInput?.userId}`)}

      // Soft vérification de si l'user a déjà des associations
      const previousUserRules = await user.getUserPostLimit()

      if (previousUserRules.length > 0){
        throw new Error(`L'user pour createPostLimitationRule a déjà des associations.  userId : ${user.id}   nb previous : ${previousUserRules.length}   previous : ${JSON.stringify(previousUserRules)}`)
      }

      // creation de l'input
      const rule = await models.PostLimitationRules.create(limitRuleInput)

      // Logging
      await LogService.logAction({
        logOperation:LOG_OPERATIONS.Post.CreateLimiteRule.action,
        foreignIds:{userId:user.id}, // L'ID de qui ça touche
        ip,
        models,
        userId:me?.id, // L'id du créateur de la rule
        logData:{newRule:rule},
        req,
      })
      return rule

    }catch(e){
      console.error("error creation PostLimitationRule.    e :",e)
      throw new GraphQLError("Erreur lors de la création de la règle.")
    }
  },

  deletePostLimitationRule: async({id},{me,ip,req})=>{
    try{

      // Verif de l'existance
      const rule=await models.PostLimitationRules.findByPk(id)
      if (!rule){throw new Error(`La rule à supprimer avec l'ID : ${id} n'existe pas.`)}

      // Suppression
      await models.PostLimitationRules.destroy({where:{id}})

      // Logging
      await LogService.logAction({
        logOperation:LOG_OPERATIONS.Post.DeleteLimiteRule.action,
        foreignIds:{userId:rule.userId}, // L'ID de qui ça touche
        ip,
        models,
        logData:{deletedRule:rule},
        userId:me?.id, // L'id du créateur de la rule
        req,
      })

      return true

    }catch(e){
      console.error("error dans delete PostLimitationRule.    e :",e)
      throw new GraphQLError("Erreur lors de la supression de la règle")
    }
  },

  updatePostLimitationRule: async({id,limitRuleInput},{me,ip,req})=> {
    try {
      // Verif de l'existance de la rule
      const rule = await models.PostLimitationRules.findByPk(id)
      if (!rule) {
        throw new Error(`La rule à mettre à jour avec l'ID : ${id} n'existe pas.`)
      }

      // Verif du new Input sur les foreignFields
      if (limitRuleInput?.userId) {
        const user = await models.User.findByPk(limitRuleInput?.userId)
        if (!user) {
          throw new Error(`L'user pour update PostLimitationRule n'est pas reconnu. UserId input : ${limitRuleInput?.userId}`)
        }
      }

      // update pour les champs sélectionnés
      const newInput = { ...rule, ...limitRuleInput }
      const [numberOfAffectedRows] = await models.PostLimitationRules.update(newInput, { where: { id } })

      // Verif de l'update
      if (!numberOfAffectedRows > 0) {
        throw new Error(`la rule d'ID ${id} n'a pas été mise à jour. NAR : ${numberOfAffectedRows}`)
      }

      // Log de l'update
      await LogService.logAction({
        logOperation: LOG_OPERATIONS.Post.UpdateLimiteRule.action,
        foreignIds: { userId: newInput.userId }, // L'ID de qui ça touche
        ip,
        models,
        logData: { previousRule: rule, ruleInput: limitRuleInput },
        userId: me?.id, // L'id du créateur de la rule
        req,
      })

      return true

    } catch (e) {
      console.error("error dans update PostLimitationRule.    e :", e)
      throw new GraphQLError("Erreur dans la mise à jour de la règle")
    }
  },

  // FIN CRUD OPERATIONS

  postLimitationRuleForUserId :async ({userId},ctx)=>{
    /* Permet de récupérer la rule en fournissant un userId */
    try{
      // Verif de l'existance de la rule
      const rule = await models.PostLimitationRules.findOne({where:{userId}})
      //if (!rule) {throw new Error(`La rule associé à l'userId ${userId} n'existe pas `)}
      return rule
    }catch(e){
      console.error("error dans postLimitationRuleForUserId. UserId :",userId,"    e :",e)
      throw new GraphQLError(`erreur dans la récupération de la règle pour l'utilisateur ${userId} `)
    }
  },

  postLimitationRuleWithId:async({id},ctx)=>{
    try{
      const rule = await models.PostLimitationRules.findByPk(id)
      //if (!rule) {throw new Error(`La rule associé à l'id ${id} n'existe pas `)}
      return rule
    }catch(e){
      console.error("error dans postLimitationRuleWithId. id :",id,"    e :",e)
      throw new GraphQLError(`erreur dans la récupération de la règle pour l'id ${id} `)
    }
  },

  getMyPostLimitationRule:async({ me })=>{
    try {
      const rule = await models.PostLimitationRules.findOne({where:{userId:me.id}})
      return rule
    }
    catch(e){
      console.error("error dans getMyPostLimitationRule.     e:",e)
      throw new GraphQLError("erreur dans la récupération de votre règle de limitation")
    }
  },



  canUserIdPostDiscussion:async({userId},ctx)=>{
    try{
      // Récupération de la rule pour l'user
      const rule=await PostLimitationRuleService.postLimitationRuleForUserId({userId},ctx)

      // Normalement, si on a pas levé d'erreure dans postLimitationRule... C'est que l'user est vérifié.
      const user=await models.User.findByPk(userId)

      // override admin
      if (user?.role === ROLES.ADMIN ){
        return 0
      }

      // si on a pas de rule, alors par default, on return OK
      if (!rule){
        return 0
      }


      ///////// Si on a des rules, alors on query la table post
      if (rule.timeWindow === undefined){throw new Error(`Dans canUserIdPostDiscussion : Les secondes ne sont pas définies dans la rule.   Rule : ${rule} `)}
      if (rule.limitNumber === undefined){throw new Error(`Dans canUserIdPostDiscussion : La limite n'est pas définies dans la rule.   Rule : ${rule} `)}
      if (rule.isActive === undefined){throw new Error(`Dans canUserIdPostDiscussion : isActive n'est pas défini dans la rule.   Rule : ${rule} `)}

      // Check de si la rule est active, si non, alors on returne 0
      if (rule.isActive===false){
        return 0
      }

      // Check de si il y a une limite positive (sinon, ça désactive la possibilité de créer une question. Si limite = 0, returne -1, sinon le temps avant de pouvoir reposer une question:
      if (rule.limitNumber<=0){return -1}

      // get de la date et l'heure actuelle
      const now = new Date(); // Date en UTC+0 inchallah
      const millisecondsToRemove=rule.timeWindow * 1000 ;
      const pastDateUtc=new Date(now.getTime() - millisecondsToRemove)

      const result = await models.Post.findAll({
        where:{
          userId:userId,
          createdAt:{[Op.gt]:pastDateUtc}
        },
        order:[['createdAt','DESC']]
      })

      //////// Maintenant, selon le résultat alors on retourne ce que l'on veut.

      // Si count?.length < limitNumber, alors on retourne 0
      if (result?.length<rule.limitNumber){return 0}

      // Sinon, si on a déjà dépassé la limite, alors on calcule le temps d'attente avant de pouvoir reposer une question.
      const thresholdDate = result[(rule.limitNumber - 1)].createdAt // On récupère la date critique avant laquelle l'user pour ré-envoyer un message.
      const futurThresholdDate= new Date(thresholdDate.getTime() + millisecondsToRemove) // calcul de la date où ça sera OK
      const differenceInSeconds = Math.ceil((futurThresholdDate.getTime() - now.getTime()) / 1000) // Différence entre la date futur et la now => donne ms que l'on /1000 => pour seconds

      return differenceInSeconds
    }catch(e){
      console.error("error dans canUserIdPostDiscussion.    e :",e)
      throw new GraphQLError("Erreur dans la récupération de l'information de votre droit de poster une discussion")
    }
  },
}