import { Graph<PERSON>Error } from 'graphql';
import moment from 'moment';
import Sequelize from 'sequelize';
import striptags from 'striptags';
import { isDev } from '../../index';
import models, { sequelize } from '../../models';
import { LIKE_HISTORY_TYPE } from '../../models/like_history';
import { LOG_OPERATIONS } from '../../models/log/log.js';
import { NotificationType } from '../../models/notification.js';
import { ROLES } from '../authorization.js';
import { AcademicDatesService } from '../config/config-service.js';
import { CoursService } from '../cours/cours-service';
import { EventService } from '../event/event-service.js';
import { UploadService } from '../file/upload-service';
import { GroupeService } from '../groupe/groupe-service.js';
import { EXOQUALIZE } from '../helpers.js';
import LogService from '../log/log-service.js';
import { NotificationService } from '../notification/notification-service.js';
import { PermissionService } from '../permission/permission-service.js';
import { QCMService } from '../qcm/qcm-service.js';
import { QuestionsService } from '../qcm/questions/questions-service.js';
import { UserService } from '../user/user-service.js';
import { UserStatsService } from '../user/userStats-service.js';
import ChatGptServicePost, { AI_POST_FEEDBACK_STATE } from './chat-gpt-service.js';
import { PostLimitationRuleService } from './post_limitation_rules/post-limitation-rule-service';

const { Op } = Sequelize;

export const CommentairesType = {
  QCM: 'QCM',
  COURS: 'COURS',
  EVENT: 'EVENT',
  FORUM: 'FORUM',
  QUESTION_ANSWER: 'QUESTION_ANSWER',
  EXERCISE: 'EXERCISE', // questionId for new exercises
};
export const getTypeIdFromObject = (post, type) => {
  switch (type) {
    case CommentairesType.COURS:
      return parseInt(post.courId);
    case CommentairesType.EVENT:
      return parseInt(post.eventId);
    case CommentairesType.QCM:
      return parseInt(post.qcmIdQcm);
    case CommentairesType.FORUM:
      return parseInt(post.forumId);
    case CommentairesType.QUESTION_ANSWER:
      return parseInt(post.answerId);
    case CommentairesType.EXERCISE:
      return parseInt(post.questionId);
    default:
      return undefined;
  }
};
export const getCommentaireTypeFromObject = (post) => {
  const { courId, qcmIdQcm, forumId, answerId, eventId, questionId } = post;
  if (courId) return CommentairesType.COURS;
  if (qcmIdQcm) return CommentairesType.QCM;
  if (forumId) return CommentairesType.FORUM;
  if (answerId) return CommentairesType.QUESTION_ANSWER;
  if (eventId) return CommentairesType.EVENT;
  if (questionId) return CommentairesType.EXERCISE;
  return undefined;
};

const autoNestPosts = (items, itemId = null, typeId) =>
  items
    .filter(item => item.parentId === itemId)  // Seulement les items fils du parent
    .map(item => ({ ...item, typeId: typeId, children: autoNestPosts(items, item.id, typeId) }));

const getUserIds = (array) => {
  let result = [];
  array.forEach(function(a) {
    result.push(a.userId);
    if (Array.isArray(a.children) && a.children.length > 0) {
      result.push(getUserIds(a.children));
    }
  });
  return result;
};

export const PostService = {
  //////// CRUD Post //////////
  isAnswerPost: async (post) => !!post.parentId,
  createPost: async (post, userId, ip) => {
    try {
      let mentionedUserIds = [];
      if (post.mentionedUserIds) {
        mentionedUserIds = JSON.parse(JSON.stringify(post.mentionedUserIds));
        delete post.mentionedUserIds;
      }
      post.userId = userId;
      post.ip = ip;

      // Verif de la rule de post de l'user
      const canStartNewDiscussion = await PostLimitationRuleService.canUserIdPostDiscussion({ userId }, null);
      if (canStartNewDiscussion !== 0) {
        throw new Error(`l'user ${userId} n'a pas la permission de démarer une nouvelle discussion. TimeWindow : ${canStartNewDiscussion}`);
      }

      let savedPost = await models.Post.create(post);
      if (post.file) {
        let name = await UploadService.uploadFile(post.file);
        await savedPost.createFile({ file: name, type: 'file' });
      }
      if (post.fileImage) {
        let name = await UploadService.uploadFile(post.fileImage);
        await savedPost.createFileImage({ file: name, type: 'image' });
      }
      if (post?.fileList?.length > 0) {
        for (const file of post.fileList) {
          let name = await UploadService.uploadFile(file?.originFileObj);
          await savedPost.createFile({ file: name, type: 'file', mimeType: file?.type });
        }
      }
      await UserStatsService.incrementStat(userId, UserStatsService.OPERATIONS.incrementPostsSent);

      const isAnswer = await PostService.isAnswerPost(post);
      let fromUser = await models.User.findByPk(userId, { attributes: ['id', 'username'] });
      let threadPost;
      if (isAnswer) {
        // IS COMMENT ON A POST
        threadPost = await PostService.getThreadPost(post);
        if (threadPost) {
          // alert thread post guy that there is an answer
          // Notifie tous les participants du thread sauf nous
          const commentaireType = await getCommentaireTypeFromObject(post);
          const typeId = await getTypeIdFromObject(post, commentaireType);
          let where = {};
          switch (commentaireType) {
            case CommentairesType.COURS:
              where = { ...where, courId: typeId };
              break;
            case CommentairesType.QCM:
              where = { ...where, qcmIdQcm: typeId };
              break;
            case CommentairesType.FORUM:
              where = { ...where, forumId: typeId };
              break;
            case CommentairesType.QUESTION_ANSWER:
              where = { ...where, answerId: typeId };
              break;
            case CommentairesType.EVENT:
              where = { ...where, eventId: typeId };
              break;
            case CommentairesType.EXERCISE:
              where = { ...where, questionId: typeId };
              break;
          }
          let allPostUsersInThread = await models.Post.findAll({
            where: where,
            raw: true,
          });
          let uniqueUsersIds = getUserIds(
            autoNestPosts(allPostUsersInThread, threadPost.id, typeId))
            .flat(Infinity)
            .filter((v, i, a) => a.indexOf(v) === i);
          // Add thread post author userId
          uniqueUsersIds.push(threadPost.userId);
          /////
          for (const uId of uniqueUsersIds) {
            if (uId !== userId) {
              await NotificationService.sendNewNotificationForUser({
                text: `${fromUser.username} a commenté le sujet ${striptags(threadPost.title).slice(0, NotificationService.CHARACTER_LIMIT_TITLE)}...`,
                type: NotificationType.NEW_POST,
                value: striptags(post.text).slice(0, NotificationService.CHARACTER_LIMIT_BODY) + '...',
                objectId: threadPost.id,
                parentId: typeId,
                userId: uId, // notificationReceveur
                fromUserId: userId, // message sender
                method: commentaireType,
              });
            }
          }
          // Notify mentionned users
          for (const uId of mentionedUserIds) {
            if (uId !== userId) {
              await NotificationService.sendNewNotificationForUser({
                text: `${fromUser.username} vous a mentionné dans ${striptags(threadPost.title).slice(0, NotificationService.CHARACTER_LIMIT_TITLE)}...`,
                type: NotificationType.NEW_POST,
                value: '👋️ ' + striptags(post.text).slice(0, NotificationService.CHARACTER_LIMIT_BODY) + '...',
                objectId: threadPost.id,
                parentId: typeId,
                userId: uId, // notificationReceveur
                fromUserId: userId, // message sender
                method: commentaireType,
              });
            }
          }
        } else {
          console.error('thread post not found, should be impossible');
        }

        // Not an answer, it's a thread
      } else {
        // IT'S A THREAD POST, notify admin + tuteurs
        const commentaireType = await getCommentaireTypeFromObject(post);
        const typeIdFromObject = await getTypeIdFromObject(post, commentaireType);

        const {
          usersIdsToNotify,
          botsIdsToAutoAnswer,
        } = await PostService.getUserIdsToNotifyAndBotIdsToAutoAnswer({ commentaireType, typeIdFromObject, userId });

        /* AI */
        /* Chat GPT auto-answering without waiting for it to complete */
        if (CommentairesType.FORUM !== commentaireType) {
          ChatGptServicePost.handleThreadReplyByAI(savedPost, commentaireType, typeIdFromObject, botsIdsToAutoAnswer, fromUser);
        }
        /* -- */

        const notifValue = ('✏️ ' + striptags(post.title) + ' : ' + striptags(post.text)).slice(0, NotificationService.CHARACTER_LIMIT_BODY) + '...';
        for (const userIdToNotify of usersIdsToNotify) {
          await NotificationService.sendNewNotificationForUser({
              text: `${fromUser.username} a posté un nouveau sujet`,
              type: NotificationType.NEW_THREAD,
              value: notifValue,
              objectId: savedPost.id,
              parentId: typeIdFromObject,
              userId: userIdToNotify, // notificationReceveur
              fromUserId: userId, // message sender
              method: commentaireType,
            },
          );
        }


        const notifValueMention = ('👋️ ' + striptags(post.title) + ' : ' + striptags(post.text)).slice(0, NotificationService.CHARACTER_LIMIT_BODY) + '...';
        // Notify mentionned users
        for (const uId of mentionedUserIds) {
          if (uId !== userId) {
            await NotificationService.sendNewNotificationForUser({
              text: `${fromUser.username} vous a mentionné dans son nouveau sujet`,
              type: NotificationType.NEW_THREAD,
              value: notifValueMention,
              objectId: savedPost.id,
              parentId: typeIdFromObject,
              userId: uId, // notificationReceveur
              fromUserId: userId, // message sender
              method: commentaireType,
            });
          }
        }
      }

      await LogService.logAction({
        logOperation: LOG_OPERATIONS.Post.Create.action,
        ip,
        logData: {
          isAnswer: isAnswer,
          threadPostId: threadPost?.id || null,
        },
        foreignIds: {
          postId: savedPost.id,
          userId: userId,
        },
        models,
        userId,
      });

      return await savedPost;
    } catch (e) {
      console.error(e);
      throw new GraphQLError('Erreur lors de la création du post');
    }
  },

  /* Update a post, or set as resolved */
  updatePost: async (id, post, userId) => {
    try {
      let mentionedUserIds = [];
      if (post.mentionedUserIds) {
        mentionedUserIds = JSON.parse(JSON.stringify(post.mentionedUserIds));
        delete post.mentionedUserIds;
      }
      let newPost = await models.Post.findByPk(id);
      const isSettingAsResolved = !newPost?.isResolved && post?.isResolved;
      // Do not delete if post has answers and it's not an admin
      let user = await models.User.findByPk(userId);
      if (user && user.role === 'USER') {
        // si c'est un thread post et qu'il n'y a aucune réponse à ce thread, empêche de supprimer
        if (newPost.parentId === null) { // it's a thread
          // get childs of this post
          let childs = await models.Post.findAll({ where: { parentId: newPost.id } });
          if (childs) {
            // pas de childs, empêcher de supprimer
            throw new GraphQLError('Vous ne pouvez pas éditer ce post car il a des réponses');
          }
        }
      }
      const commentaireType = await getCommentaireTypeFromObject(post);
      const typeIdFromObject = await getTypeIdFromObject(post, commentaireType);

      // Notify mentionned users
      for (const uId of mentionedUserIds) {
        if (uId !== userId) {
          await NotificationService.sendNewNotificationForUser({
            text: `${user.username} vous a mentionné dans ${striptags(post.title).slice(0, NotificationService.CHARACTER_LIMIT_TITLE)}...`,
            type: NotificationType.NEW_POST,
            value: '👋️ ' + striptags(post.text).slice(0, NotificationService.CHARACTER_LIMIT_BODY) + '...',
            objectId: newPost.id,
            parentId: typeIdFromObject,
            userId: uId, // notificationReceveur
            fromUserId: userId, // message sender
            method: commentaireType,
          });
        }
      }

      if (post.file) {
        let name = await UploadService.uploadFile(post.file);
        await newPost.createFile({ file: name, type: 'file' });
        delete post.file;
      }
      if (post.fileImage) {
        let name = await UploadService.uploadFile(post.fileImage);
        await newPost.createFileImage({ file: name, type: 'image' });
        delete post.fileImage;
      }
      if (post.title) {
        newPost.title = post.title;
      }
      if (post.text) {
        newPost.text = post.text;
      }
      if (post.tag) {
        newPost.tag = post.tag;
      }
      if (post.hasOwnProperty('isResolved')) {
        newPost.isResolved = post.isResolved;
      }
      if (post.hasOwnProperty('postTypeId')) {
        newPost.postTypeId = post.postTypeId;
      }
      if (isSettingAsResolved) {
        newPost.isAskingForHumanHelp = false;
      }
      await newPost.save();

      await LogService.logAction({
        logOperation: LOG_OPERATIONS.Post.Update.action,
        //ip,
        logData: {
          isAnswer: newPost?.parentId !== null,
          setResolved: isSettingAsResolved,
        },
        foreignIds: {
          postId: newPost?.id,
          userId: userId,
        },
        models,
        userId,
      });

      return true;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },
  deletePost: async (id, userId) => {
    try {
      // Do not delete if post has answers and it's not an admin
      let user = await models.User.findByPk(userId);
      const post = await models.Post.findByPk(id);
      if (user && user.role === 'USER') {
        // si c'est un thread post et qu'il n'y a aucune réponse à ce thread, empêche de supprimer
        if (post.parentId === null) { // it's a thread
          // get childs of this post
          let childs = await models.Post.findAll({ where: { parentId: post.id } });
          if (childs) {
            // pas de childs, empêcher de supprimer
            throw new GraphQLError('Vous ne pouvez pas supprimer ce post car il a des réponses');
          }
        }
      }

      await LogService.logAction({
        logOperation: LOG_OPERATIONS.Post.Delete.action,
        //ip,
        logData: {
          title: post?.title,
          text: post?.text,
        },
        foreignIds: {
          //postId: id,
          userId: userId,
        },
        models,
        userId,
      });

      // Delete older posts logs
      await models.Log.destroy({
        where: {
          postId: id,
        },
      });

      const isDeleted = await models.Post.destroy({ where: { id } });
      // Si c'est un sujet, supprime les notifs associées à ce sujet
      /*
            const notifsToDelete = await models.Notification.findAll({
              where: {
                objectId: id,
                type: NotificationType.NEW_THREAD,
              },
            })
            await models.Notification.bulkDelete(notifsToDelete)
      */
      return isDeleted;
    } catch (e) {
      throw new GraphQLError(e.message);
    }
  },

  getPostLikeHistory: async (postId, userId) => await models.LikeHistory.findOne({
    where: {
      userId: userId,
      postId: postId,
    },
    order: [['createdAt', 'DESC']],
  }),

  likePost: async (id, userId) => {
    try {
      let post = await models.Post.findByPk(id, {
        include: [{ model: models.User, as: 'user' }],
      });
      if (post) {
        let likeHistory = await PostService.getPostLikeHistory(post.id, userId);
        // If last action was dislike
        const hasDislikedInThePast = (likeHistory && likeHistory.type && likeHistory.type === LIKE_HISTORY_TYPE.POST_DISLIKE);
        // If last action was like
        const hasLikedInThePast = (likeHistory && likeHistory.type && likeHistory.type === LIKE_HISTORY_TYPE.POST_LIKE);

        // If user has already disliked this post previously
        if (hasDislikedInThePast) {
          // reset
          post.likes = post.likes + 2;
          likeHistory.type = LIKE_HISTORY_TYPE.POST_LIKE;
          await likeHistory.save();
          //await likeHistory.destroy();
        } else if (!likeHistory) {
          // First action on post ever
          post.likes = post.likes + 1;
          await models.LikeHistory.create({
            type: LIKE_HISTORY_TYPE.POST_LIKE,
            userId: userId,
            postId: id,
          });
          // Peut pas dislike
        } else {
          // If last action was like
          if (hasLikedInThePast) {
            // Wants to un-like
            post.likes = post.likes - 1;
            // Destroy history because it's back to normal
            await likeHistory.destroy();
          }
        }
        await UserStatsService.incrementStat(userId, UserStatsService.OPERATIONS.incrementLikesGiven);
        if (post.user && post.user.id) {
          await UserStatsService.incrementStat(post.user.id, UserStatsService.OPERATIONS.incrementLikesReceived);
        }
        return await post.save({ silent: true });
      }
    } catch (e) {
      throw new GraphQLError(e.message, e.code);
    }
  },
  dislikePost: async (id, userId) => {
    try {
      let post = await models.Post.findByPk(id);
      if (post) {
        let likeHistory = await PostService.getPostLikeHistory(post.id, userId);
        // Disliked dans le passé
        const hasDislikedInThePast = (likeHistory && likeHistory.type && likeHistory.type === LIKE_HISTORY_TYPE.POST_DISLIKE);
        const hasLikedInThePast = (likeHistory && likeHistory.type && likeHistory.type === LIKE_HISTORY_TYPE.POST_LIKE);
        if (hasLikedInThePast) {
          // LIKE DEVIENT DISLIKE, update
          post.likes = post.likes - 2;
          likeHistory.type = LIKE_HISTORY_TYPE.POST_DISLIKE;
          await likeHistory.save();
          //await likeHistory.destroy();
        } else if (!likeHistory) {
          post.likes = post.likes - 1;
          await models.LikeHistory.create({
            type: LIKE_HISTORY_TYPE.POST_DISLIKE,
            userId: userId,
            postId: id,
          });
          // Peut pas dislike
        } else {
          if (hasDislikedInThePast) {
            // Wants to un-dislike
            post.likes = post.likes + 1;
            await likeHistory.destroy(); // Destroy history
          }
        }
        return await post.save({ silent: true });
      }
    } catch (e) {
      throw new GraphQLError(e.message, e.code);
    }
  },

  getLatestsVisiblePosts: async (userId) => {
    try {
      let myself = await models.User.findByPk(userId);
      let posts;

      if (myself.role === ROLES.ADMIN) {
        // 10 last posts
        posts = await models.Post.findAll({
          limit: 10,
          include: [{ model: models.User, as: 'user' }, models.Cours, models.Qcm, models.Forum],
          order: [
            ['createdAt', 'DESC'],
          ],
        });
      } else {
        // Permissions par forum, cours, qcm
        const coursIds = await PermissionService.getAllCoursIdsAvailableForUser(myself);
        const answerIds = await PermissionService.getAvailableMcqAnswerIdsForUser(myself);
        const forumIds = await PermissionService.getAvailableForumIdsForUser(myself);

        // 10 last posts in allowed forums, cours, qcm
        posts = await models.Post.findAll({
          where: {
            [Op.or]: [
              { courId: coursIds },
              { answerId: answerIds },
              { forumId: forumIds },
            ],
          },
          limit: 10,
          include: [{ model: models.User, as: 'user' }, models.Cours, models.Qcm, models.Forum],
          order: [
            ['createdAt', 'DESC'],
          ],
        });
      }

      // add thread ids
      for (const post of posts) {
        if (post.parentId !== null) {
          let tempParentId = post.parentId;
          let threadId = null;
          for (; tempParentId !== null;) {
            let parent = await models.Post.findByPk(tempParentId);
            tempParentId = parent.parentId;
            threadId = parent.id;
          }
          post.text = post.text.slice(0, 600);
          post.threadId = threadId;
        }
        post.cours = post.cour;
      }
      return posts;
    } catch (e) {
      throw new GraphQLError(e.message);
    }
  },

  getThreadId: async (post) => {
    if (post.parentId !== null) {
      let tempParentId = post.parentId;
      let threadId = null;
      for (; tempParentId !== null;) {
        let parent = await models.Post.findByPk(tempParentId);
        tempParentId = parent.parentId;
        threadId = parent.id;
      }
      return threadId;
    }
    return null;
  },
  getThreadPost: async (post) => {
    try {
      let id = await PostService.getThreadId(post);
      if (id) {
        return await models.Post.findByPk(id);
      }
      return null;
    } catch (e) {
      throw new GraphQLError(e.message);
    }
  },

  getNumberOfPostsInUE: async (ueId) => {
    try {
      if (!ueId) {
        return {
          cours: 0,
          qcm: 0,
          annale: 0,
          total: 0,
        };
      }

      const coursIds = await CoursService.getCoursIdsInUe(ueId) || 0;
      const qcmIds = await QCMService.getQcmIdsInUeId(ueId) || 0;
      const qcmIdsAnnales = await QCMService.getQcmIdsInUeId(ueId, true) || 0;

      const cours = await PostService.getPostCountCoursAmongIds(coursIds) || 0;
      const qcm = await PostService.getPostCountQcmAmongIds(qcmIds) || 0;
      const annale = await PostService.getPostCountQcmAmongIds(qcmIdsAnnales) || 0;
      const total = cours + qcm + annale;

      return {
        cours,
        qcm,
        annale,
        total,
      };

    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },

  getNumberOfPostsInUECategory: async (ueCategoryId) => {
    try {
      if (!ueCategoryId) {
        console.error('in getNumberOfPostsInUECategory: ueCategoryId is invalid');
        return 0;
      }

      const coursIds = await CoursService.getCoursIdsInUeCategoryId(ueCategoryId);
      const qcmIds = await QCMService.getQcmIdsLinkedToCoursIds(coursIds);
      return await PostService.getPostCountCoursQcmAmongIds(coursIds, qcmIds);
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },
  getPostCountCoursQcmAmongIds: async (coursIds, qcmIds) => {
    return models.Post.count({
      where: {
        forumId: null, // pas dans forum
        [Op.or]: {
          courId: { [Op.in]: coursIds },
          qcmIdQcm: { [Op.in]: qcmIds },
        },
      },
    });
  },

  getPostCountCoursAmongIds: async (coursIds) => {
    return models.Post.count({
      where: {
        forumId: null, // pas dans forum
        courId: { [Op.in]: coursIds },
      },
    });
  },
  getPostCountQcmAmongIds: async (qcmIds) => {
    return models.Post.count({
      where: {
        forumId: null, // pas dans forum
        qcmIdQcm: { [Op.in]: qcmIds },
      },
    });
  },

  getLastPostInUE: async (ueId) => {
    try {
      if (!ueId) {
        console.error('invalid ueId parameter in getLastPostInUE');
        return null;
      }
      const coursIds = await CoursService.getCoursIdsInUe(ueId);
      const qcmIds = await QCMService.getQcmIdsLinkedToCoursIds(coursIds);
      return await PostService.getLastPostCoursQcmAmongIds(coursIds, qcmIds);
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },

  /*-- QCM COURS ANNALES --*/
  getLastPostInCours: async (ueId) => {
    try {
      const coursIds = await CoursService.getCoursIdsInUe(ueId);
      return models.Post.findOne({
        where: {
          forumId: null, // pas dans forum
          courId: { [Op.in]: coursIds },
        },
        order: [EXOQUALIZE.ORDER_MOST_RECENTLY_UPDATED], // plus récents
      });
    } catch (e) {
      throw new GraphQLError(e.message);
    }
  },
  getLastPostInQcm: async (ueId) => {
    try {
      const qcmIds = await QCMService.getQcmIdsInUeId(ueId);
      return models.Post.findOne({
        where: {
          forumId: null, // pas dans forum
          qcmIdQcm: { [Op.in]: qcmIds },
        },
        order: [EXOQUALIZE.ORDER_MOST_RECENTLY_UPDATED], // plus récents
      });
    } catch (e) {
      throw new GraphQLError(e.message);
    }
  },
  getLastPostInAnnale: async (ueId) => {
    try {
      const qcmIds = await QCMService.getQcmIdsInUeId(ueId, true);
      return models.Post.findOne({
        where: {
          forumId: null, // pas dans forum
          qcmIdQcm: { [Op.in]: qcmIds },
        },
        order: [EXOQUALIZE.ORDER_MOST_RECENTLY_UPDATED], // plus récents
      });
    } catch (e) {
      throw new GraphQLError(e.message);
    }
  },
  /*----------------------*/

  getPostsWithoutAnswerInUe: async (ue) => {
    const { id: ueId } = ue;
    const coursIds = await CoursService.getCoursIdsInUe(ueId);
    const qcmIds = await QCMService.getQcmIdsLinkedToCoursIds(coursIds);

    return await PostService.getPostsCoursQcmWithoutAnswerAmongIds(coursIds, qcmIds);
  },


  /* Posts non résolus dans une UE */
  getUnresolvedPostsInForum: async (userId) => {
    try {
      /* Forum ENTITY POSTS */
      const posts = await models.Post.findAll({
          where: {
            forumId: {
              [Op.ne]: null,
            },
            parentId: null,
            isResolved: false,
          },
          order: [EXOQUALIZE.ORDER_MOST_RECENTLY_UPDATED], // plus récents
        },
      );

      return posts;
    } catch (e) {
      console.error(e);
    }
  },

  /* Posts non résolus dans toutes les UEs */
  getAllUnresolvedPostsUEs: async (user) => {
    const ueIds = await PermissionService.getAvailableUEIdsForUser(user);
    let posts = [];
    for (const ueId of ueIds) {
      posts = [...posts, ...(await PostService.getUnresolvedPostsInUe({ id: ueId }, user?.id))];
    }
    return posts;
  },
  /* Posts non résolus dans une UE */
  getUnresolvedPostsInUe: async (ue, userId) => {
    const { id: ueId } = ue;
    const user = await models.User.findByPk(userId);
    const groupsIdsResponsible = await GroupeService.getAllowedGroupsIdsForUser(user);
    const userIdsResponsible = await GroupeService.getUserIdsInGroupIds(groupsIdsResponsible);
    /*
    Pour voir la question il faut
      - Que l'élève soit dans un groupe dont le tuteur est responsable
      - Que la question soit posée au sujet d'un cours auquel il a accès
        Checker que le cours lié à la question est accessible par le tuteur
        si des tuteurs ont accès que à une moitié de matière,
          l'élève lui peut avoir accès à toute la matière
    */
    let coursIds, qcmIds, questionIds;
    if (user.role === ROLES.ADMIN) {
      // All courses for admin
      coursIds = await CoursService.getCoursIdsInUe(ueId);
      qcmIds = await QCMService.getAllQcmIdsInUeId(ueId);
    } else {
      // Available courses for tuteur or sub admin
      // Linked courses on questions
      coursIds = await PermissionService.getAvailableCoursIdsInUEForUser(ueId, user);
      const questionIdsFromCourses = await QuestionsService.getQuestionsIdsFromCoursIds(coursIds);

      // Allowed Questions Ids in UE
      qcmIds = await QCMService.getAllQcmIdsInUeId(ueId);
      const availableQcmIds = await PermissionService.getAvailableMcqIdsForUser(user);
      qcmIds = qcmIds.filter(q => availableQcmIds.includes(q));
      questionIds = await QuestionsService.getQuestionsIdsForMcq(qcmIds);
      const qc = await models.QuestionCours.findAll({
        where: {
          questionId: questionIds,
        },
        raw: true,
      });
      const allQuestionIdsLinked = qc.map(q => q.questionId);
      // Allowed Questions Ids in UE that have no linked courses
      const allUnlinkedQuestions = questionIds.filter(q => !allQuestionIdsLinked?.includes(q));

      // Unique Questions IDs to look for: accessible courses + unlinked questions
      questionIds = [...new Set([...questionIdsFromCourses, ...allUnlinkedQuestions])];

    }
    if (questionIds) {
      return PostService.getUnresolvedPostsCoursQuestionsAmongIds(coursIds, questionIds, userIdsResponsible);
    }
    return PostService.getUnresolvedPostsCoursQcmAmongIds(coursIds, qcmIds, userIdsResponsible);
  },

  getLastPostInUECategory: async (ueCategoryId) => {
    const coursIds = await CoursService.getCoursIdsInUeCategoryId(ueCategoryId);
    const qcmIds = await QCMService.getQcmIdsLinkedToCoursIds(coursIds);
    return await PostService.getLastPostCoursQcmAmongIds(coursIds, qcmIds);
  },

  getLastPostInUECategoryQcm: async (ueCategoryId) => {
    const coursIds = await CoursService.getCoursIdsInUeCategoryId(ueCategoryId);
    const qcmIds = await QCMService.getQcmIdsLinkedToCoursIds(coursIds);
    return await PostService.getLastPostForQcmAmongIds(qcmIds);
  },

  getLastPostForCours: async (coursId) => {
    try {
      return models.Post.findOne({
        where: {
          forumId: null, // pas dans forum
          courId: coursId,
        },
        order: [EXOQUALIZE.ORDER_MOST_RECENTLY_UPDATED], // plus récents
      });
    } catch (e) {
      throw new GraphQLError(e.message);
    }
  },

  getLastPostForQcm: async (qcmId) => {
    try {
      const answersIds = await QCMService.getAnswersIdsFromQcms(qcmId);
      return models.Post.findOne({
        where: {
          forumId: null, // pas dans forum
          // qcmIdQcm: qcmId,
          answerId: { [Op.in]: answersIds },
        },
        order: [EXOQUALIZE.ORDER_MOST_RECENTLY_UPDATED], // plus récents
      });
    } catch (e) {
      throw new GraphQLError(e.message);
    }
  },

  getLastPostForQcmAmongIds: async (qcmIds) => {
    return models.Post.findOne({
      where: {
        forumId: null, // pas dans forum
        qcmIdQcm: { [Op.in]: qcmIds },
      },
      order: [EXOQUALIZE.ORDER_MOST_RECENTLY_UPDATED], // plus récents
    });
  },

  getLastPostCoursQcmAmongIds: async (coursIds, qcmIds) => {
    return models.Post.findOne({
      where: {
        forumId: null, // pas dans forum
        [Op.or]: {
          courId: { [Op.in]: coursIds },
          qcmIdQcm: { [Op.in]: qcmIds },
        },
      },
      order: [EXOQUALIZE.ORDER_MOST_RECENTLY_UPDATED], // plus récents
    });
  },

  getPostsCoursQcmWithoutAnswerAmongIds: async (coursIds, qcmIds) => {
    // Temporaire
    try {
      let toReturn = [];
      for (const cId of coursIds) {
        let threadPostsCours = await models.Post.findAll({
            where: {
              courId: cId,
              parentId: null,
            },
            order: [EXOQUALIZE.ORDER_MOST_RECENTLY_UPDATED], // plus récents
          },
        );
        for (const threadPost of threadPostsCours) {
          let answersCours = await models.Post.findAll({
              attributes: ['id'],
              where: {
                courId: cId,
                parentId: threadPost.id,
              },
            },
          );
          if (answersCours === undefined || answersCours.length === 0) {
            toReturn = [...toReturn, threadPost];
          }
        }
      }
      for (const qId of qcmIds) {
        let postsQcm = await models.Post.findAll({
            where: {
              qcmIdQcm: qId,
              parentId: null,
            },
            order: [EXOQUALIZE.ORDER_MOST_RECENTLY_UPDATED], // plus récents
          },
        );
        for (const threadPost of postsQcm) {
          let answersQcm = await models.Post.findAll({
              attributes: ['id'],
              where: {
                qcmIdQcm: qId,
                parentId: threadPost.id,
              },
            },
          );
          if (answersQcm === undefined || answersQcm.length === 0) {
            toReturn = [...toReturn, threadPost];
          }
        }
      }
      return toReturn;
    } catch (e) {
      console.error(e);
    }
  },

  getUnresolvedPostsCoursQcmAmongIds: async (coursIds, qcmIds, userIds) => {
    try {
      /* COURSES POSTS */
      let threadPostsCours = await models.Post.findAll({
          where: {
            courId: { [Op.in]: coursIds },
            parentId: null,
            isResolved: false,
            userId: userIds,
          },
          order: [EXOQUALIZE.ORDER_MOST_RECENTLY_UPDATED], // plus récents
        },
      );
      // get answer IDs from QCM
      const answersIds = await QCMService.getAnswersIdsFromQcms(qcmIds);
      /* MCQ ANSWERS POSTS */
      let postsAnswersQcm = await models.Post.findAll({
          where: {
            answerId: { [Op.in]: answersIds },
            parentId: null,
            isResolved: false,
            userId: userIds,
          },
          order: [EXOQUALIZE.ORDER_MOST_RECENTLY_UPDATED], // plus récents
        },
      );
      /* MCQ ENTITY POSTS */
      let postsQcm = await models.Post.findAll({
          where: {
            qcmIdQcm: { [Op.in]: qcmIds },
            parentId: null,
            isResolved: false,
            userId: userIds,
          },
          order: [EXOQUALIZE.ORDER_MOST_RECENTLY_UPDATED], // plus récents
        },
      );
      return [...threadPostsCours, ...postsAnswersQcm, ...postsQcm];
    } catch (e) {
      console.error(e);
    }
  },

  getUnresolvedPostsCoursQuestionsAmongIds: async (coursIds, questionIds, userIds) => {
    try {
      /* COURSES POSTS */
      let threadPostsCours = await models.Post.findAll({
          where: {
            courId: { [Op.in]: coursIds },
            parentId: null,
            isResolved: false,
            userId: userIds,
          },
          order: [EXOQUALIZE.ORDER_MOST_RECENTLY_UPDATED], // plus récents
        },
      );
      // get answer IDs from QCM
      const answersIds = await QCMService.getAnswersIdsFromQuestionIds(questionIds);
      /* MCQ ANSWERS POSTS */
      let postsAnswersQcm = await models.Post.findAll({
          where: {
            answerId: { [Op.in]: answersIds },
            parentId: null,
            isResolved: false,
            userId: userIds,
          },
          order: [EXOQUALIZE.ORDER_MOST_RECENTLY_UPDATED], // plus récents
        },
      );
      return [...threadPostsCours, ...postsAnswersQcm];
    } catch (e) {
      console.error(e);
    }
  },

  async fillWhereObjectWithTypeId(typeId, postType, sequelizeWhere = {}) {
    let where = sequelizeWhere;
    switch (postType) {
      case 'cours':
        where = { ...where, courId: typeId };
        break;
      case 'qcm':
        where = { ...where, qcmIdQcm: typeId };
        break;
      case 'forum':
        where = { ...where, forumId: typeId };
        break;
      case 'answer':
        where = { ...where, answerId: typeId };
        break;
      case 'answers':
        where = { ...where, answerId: { [Op.in]: typeId } };
        break;
      case 'event':
        where = { ...where, eventId: typeId };
        break;
      case 'question':
        where = { ...where, questionId: typeId };
        break;
    }
    return where;
  },

  async getYearsAvailableForPostsFor(typeId, userId, postType) {
    try {
      let where = await PostService.fillWhereObjectWithTypeId(typeId, postType);

      let posts = await models.Post.findAll({
        attributes: ['createdAt'],
        where: where,
        raw: true,
      });

      let years = new Set();
      for (const post of posts) {
        // Si c'est fin 2020 => année scolaire 2020-2021 => renvoie 2020
        // Si c'est début 2020 => année scolaire 2019-2020 => renvoie 2019
        const currentDate = post?.createdAt;
        const momentDate = moment(currentDate);
        let year = momentDate.year();
        if (momentDate.isBetween(`${year}-07-01`, `${year + 1}-06-30`)) {
          years.add(year);
        } else {
          years.add(year - 1);
        }
      }
      return years;
    } catch (e) {
      console.error(e);
    }
  },


  getUserIdsToNotifyAndBotIdsToAutoAnswer: async ({ commentaireType, typeIdFromObject, userId }) => {
    let botsIdsToAutoAnswer = [];
    let usersIdsToNotify;
    switch (commentaireType) {
      // Dans COURS/QCM notifie ceux qui ont suscribe aux notifs de l'UE
      // Notifier tuteurs / admins ayant accès au cours
      case CommentairesType.COURS:
        usersIdsToNotify = await GroupeService.getTuteursAdminsIdsReponsibleForCoursId(userId, typeIdFromObject);
        botsIdsToAutoAnswer = await GroupeService.getChatGPTUserIdReponsibleForCoursId(userId, typeIdFromObject);
        break;
      case CommentairesType.QCM:
      case CommentairesType.QUESTION_ANSWER:
        usersIdsToNotify = await GroupeService.getTuteursAdminsIdsReponsibleForAnswerId(userId, typeIdFromObject);
        botsIdsToAutoAnswer = await GroupeService.getChatGPTUserIdReponsibleForAnswerId(userId, typeIdFromObject);
        break;
      case CommentairesType.EXERCISE:
        usersIdsToNotify = await GroupeService.getTuteursAdminsIdsReponsibleForQuestionId(userId, typeIdFromObject);
        botsIdsToAutoAnswer = await GroupeService.getChatGPTUserIdReponsibleForQuestionId(userId, typeIdFromObject);
        break;
      case CommentairesType.FORUM:
        // Dans Forum notifie toujours admin + tuteurs
        const adminTuteursF = await UserService.getAdminTuteurs();
        usersIdsToNotify = adminTuteursF.map(u => u.id);
        break;
      case CommentairesType.EVENT:
        // Dans events notifs tuteurs admins ayant accès aux cours liés à l'event
        usersIdsToNotify = await GroupeService.getTuteursAdminsIdsReponsibleForEventId(userId, typeIdFromObject);
        botsIdsToAutoAnswer = await GroupeService.getChatGPTUserIdReponsibleForEventId(userId, typeIdFromObject);
        break;
    }
    usersIdsToNotify = usersIdsToNotify?.filter(uId => uId !== userId); // exclude me if necessary
    return { usersIdsToNotify, botsIdsToAutoAnswer };
  },

  /**
   * Get all posts for current year
   *
   * @param typeId
   * @param userId author
   * @param {string} postType
   * @param filter {{sort: string, year: int}}
   */
  getPostsFor: async (typeId, userId, postType, filter) => {
    try {
      let where = {};
      if (filter?.year) {
        const { begin, end } = await AcademicDatesService.getBeginningAndEndOfScholarYear(filter?.year);
        where = { createdAt: { [Op.between]: [moment(begin).toDate(), moment(end).toDate()] } };
      }
      if (postType === 'answers') {
        // all answers in qcm typeId
        const answersIds = await QCMService.getAnswersIdsFromQcms([typeId]);
        where = await PostService.fillWhereObjectWithTypeId(answersIds, postType, where);
      } else {
        // Tous les autres postTypes
        where = await PostService.fillWhereObjectWithTypeId(typeId, postType, where);
      }
      /* Check if parent type has disabled comments */
      if (postType === 'cours') {
        const cours = await models.Cours.findByPk(typeId);
        if (cours?.settings?.showDiscussions === false) {
          return [];
        }
      }

      if (postType === 'event') {
        const event = await models.Event.findByPk(typeId);
        if (event?.showDiscussions === false) {
          return [];
        }
      }

      // Recherche par texte
      if (filter?.text) {
        where = {
          ...where,
          [Op.or]: [
            { title: { [Op.like]: `%${filter.text}%` } },
            { text: { [Op.like]: `%${filter.text}%` } },
          ],
        };
      }

      let posts, order;
      switch (filter?.sort) {
        case  'MOST_RECENT':
          order = ['createdAt', 'DESC'];
          break;
        case 'TOP':
          order = ['likes', 'DESC'];
          break;
        default:
          order = ['likes', 'DESC'];
      }
      posts = await models.Post.findAll({
        where: where,
        include: models.LikeHistory,
        order: [order],
      });

      // Parmi les posts, récupérer les miens, et parmi les miens, ceux que j'ai like/dislike
      const myLikeHistories = await posts.filter((post) => post.userId === userId).map(p => p.like_histories);
      if (myLikeHistories) {
        for (let post of posts) {
          if (post.like_histories) {
            const myHistory = await post.like_histories.filter(likeH => (likeH.userId === userId)); // get only my likes/dislikes
            if (myHistory && myHistory.length === 1) {
              post.myLastLikeAction = myHistory[0].type;
            }
          }
        }
      }
      return await posts;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },


  /* MIGRATIONS */

  // Migration
  async updateResolvedIfBeforeDate() {
    const posts = await models.Post.findAll({
      where: {
        createdAt: {
          [Op.lt]: moment('2021-07-01').toDate(),
        },
      },
    });
    for (const post of posts) {
      post.isResolved = true;
      await post.save();
    }
    return true;
  }
  ,
// Migration
  async setUpdatdAtSameAsCreatedAtIfBeforeDate() {
    const posts = await models.Post.findAll({
      where: {
        createdAt: {
          [Op.lt]: moment('2021-07-01').toDate(),
        },
      },
    });
    for (const post of posts) {
      post.updatedAt = post.createdAt;
      post.changed('updatedAt', true);
      await post.save({ silent: true });
    }
    return true;
  },

  /* User reports a post */
  async reportPost(input, id) {
    try {
      const reason = input?.reason;
      if (input.postId && !input.userId) {
        // Report POST
        const post = await models.Post.findByPk(input?.postId);
        await LogService.logAction({
          logOperation: LOG_OPERATIONS.Report.Content.action,
          logData: {
            reason,
            contentType: 'post',
            isTreated: false,
            postId: input?.postId,
          },
          models,
          userId: id,
        });
      } else if (input.userId && !input.postId) {
        // Report USER
        const user = await models.User.findByPk(input?.userId);
        if (!user) {
          throw new GraphQLError('User not found');
          return;
        }
        await LogService.logAction({
          logOperation: LOG_OPERATIONS.Report.User.action,
          logData: {
            reason,
            contentType: 'user',
            isTreated: false,
            userId: input?.userId,
          },
          models,
          userId: id,
        });
      }
      return true;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },

  async getUserThreadPosts({ userId, offset, limit }, { me }) {
    try {
      const userIdToFetch = userId || me.id; // si userId est pas défini on prend l'id de l'utilisateur connecté
      return {
        posts: await models.Post.findAll({
          where: {
            userId: userIdToFetch,
            parentId: null,
          },
          limit,
          offset,
          include: [models.Cours, models.Qcm, models.Forum],
          order: [['createdAt', 'DESC']], // Most recent firsts
        }),
        count: await models.Post.count({
          where: {
            userId: userIdToFetch,
            parentId: null,
          },
        }),
      };
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },
  async getUserAnswerPosts({ userId, offset, limit }, { me }) {
    try {
      const userIdToFetch = userId || me.id; // si userId est pas défini on prend l'id de l'utilisateur connecté
      return {
        posts: await models.Post.findAll({
          where: {
            userId,
            parentId: {
              [Op.ne]: null,
            },
          },
          limit,
          offset,
          order: [['createdAt', 'DESC']], // Most recent firsts
        }),
        count: await models.Post.count({
          where: {
            userId: userIdToFetch,
            parentId: {
              [Op.ne]: null,
            },
          },
        }),
      };
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },

  /**
   * Quand le user donne son feedback à une réponse IA
   * @param input
   * @param userId
   * @returns {Promise<boolean>}
   */
  async giveFeedbackToAiAnswer(input, userId) {
    try {
      const postId = input?.postId;
      const feedback = input?.feedback;
      const message = input?.message; // Seulement si feedback === 'refused'
      const post = await models.Post.findByPk(postId);
      const originalThreadPostId = input?.originalThreadPostId;

      if (!post) {
        throw new Error('Post not found');
      }
      if (post.userIdForAiFeedback !== userId) {
        throw new Error('User not allowed to give feedback to this post');
      }
      if (!post.state) {
        throw new Error('Post state not found');
      }
      // AI_POST_FEEDBACK_STATE to array with values only
      const AI_POST_FEEDBACK_STATE_ARRAY = Object.values(AI_POST_FEEDBACK_STATE);
      if (!AI_POST_FEEDBACK_STATE_ARRAY.includes(feedback)) {
        throw new Error('Invalid feedback');
      }
      post.state = feedback;

      isDev && console.log({feedback});

      let parentPostThread = await models.Post.findByPk(post.parentId); // pas le bon post
      let originalThreadPost = await models.Post.findByPk(originalThreadPostId);
      if (feedback === AI_POST_FEEDBACK_STATE.ACCEPTED) { // Accepte la réponse IA
        // Mettre en résolu
        parentPostThread.isResolved = true; // marquer résolu généralement
        parentPostThread.isResolvedByAi = true; // marquer résolu par IA spécifiquement
        parentPostThread.isAskingForHumanHelp = false; // Pas besoin d'aide humaine

        originalThreadPost.isResolved = true; // marquer résolu généralement
        originalThreadPost.isResolvedByAi = true; // marquer résolu généralement
        originalThreadPost.isAskingForHumanHelp = false; // marquer résolu généralement
        await originalThreadPost.save();
      }
      if (feedback === 'refused') {
        parentPostThread.isResolvedByAi = false; // marquer non résolu par IA spécifiquement
        parentPostThread.isAskingForHumanHelp = true;
      }

      if (feedback === AI_POST_FEEDBACK_STATE.REFUSED_NEED_HUMAN_HELP) {
        // Créer nouveau message à la suite du thread
        parentPostThread.isResolvedByAi = false; // marquer non résolu par IA spécifiquement
        parentPostThread.isAskingForHumanHelp = true;
        const newPost = await models.Post.create({
          text: message,
          parentId: postId, // En réponse au message IA
          userId: userId,
          courId: post.courId,
          qcmIdQcm: post.qcmIdQcm,
          forumId: post.forumId,
          eventId: post.eventId,
          questionId: post.questionId,
          answerId: post.answerId,
        });

      }
      if (feedback === AI_POST_FEEDBACK_STATE.REFUSED_NEED_AI_HELP) {
        parentPostThread.isResolvedByAi = false; // marquer non résolu par IA spécifiquement
        parentPostThread.isAskingForHumanHelp = false;
        const newPost = await models.Post.create({
          text: message,
          parentId: postId, // En réponse au message IA
          userId: userId,
          courId: post.courId,
          qcmIdQcm: post.qcmIdQcm,
          forumId: post.forumId,
          eventId: post.eventId,
          questionId: post.questionId,
          answerId: post.answerId,
        });
        const threadPost = await models.Post.findByPk(originalThreadPostId);
        if (!threadPost) {
          console.error('Thread post not found (REFUSED_NEED_AI_HELP), ai answer cant be generated');
        } else {
          // Genere nouvelle réponse IA avec historique conversation
          await this.handleAIPostReplyWithHistory(threadPost, newPost);
        }
      }

      await parentPostThread.save();
      await post.save();

      return true;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },

  /**
   *
   * @param threadPost
   * @param post
   * @param aiUserId force to use this AI user (if null, auto determine which will be used)
   * @returns {Promise<boolean>}
   */
  async handleAIPostReplyWithHistory(threadPost, post, aiUserId = null) {
    if (!threadPost || !post) {
      return;
    }
    const userId = post.userId;
    const commentaireType = await getCommentaireTypeFromObject(threadPost);
    const typeId = getTypeIdFromObject(threadPost, commentaireType);
    let where = {};

    switch (commentaireType) {
      case CommentairesType.COURS:
        where = { ...where, courId: typeId };
        break;
      case CommentairesType.QCM:
        where = { ...where, qcmIdQcm: typeId };
        break;
      case CommentairesType.QUESTION_ANSWER:
        where = { ...where, answerId: typeId };
        break;
      case CommentairesType.FORUM:
        where = { ...where, forumId: typeId };
        break;
      case CommentairesType.EVENT:
        where = { ...where, eventId: typeId };
        break;
      case CommentairesType.EXERCISE:
        where = { ...where, questionId: typeId };
        break;
    }
    let allPostUsersInThread = await models.Post.findAll({
      where: where,
      include: {
        model: models.User,
        as: 'user',
        attributes: ['id', 'username', 'role', 'bot'],
      },
      raw: true,
    });

    const nested = autoNestPosts(allPostUsersInThread, threadPost.id, typeId);

    function flattenArray(arr) {
      const result = [];
      arr.forEach(item => {
        // Clone l'objet sans la propriété children
        const { children, ...rest } = item;
        result.push(rest);
        // Si l'objet a des enfants, les aplatir récursivement
        if (children && children.length > 0) {
          result.push(...flattenArray(children));
        }
      });
      return result;
    }

    let allDiscussionFlat = flattenArray(nested);
    const threadPostWithUser = await models.Post.findByPk(threadPost.id, {
      include: {
        model: models.User,
        as: 'user',
        attributes: ['id', 'username', 'role', 'bot'],
      },
      raw: true,
    });
    // Ajout du thread post manquant
    allDiscussionFlat = [threadPostWithUser, ...allDiscussionFlat];

    const userRoleStringGpt = (post) => {
      switch (post['user.role']) {
        case ROLES.ADMIN:
        case ROLES.SUB_ADMIN:
          return 'Administrateur';
        case ROLES.TUTEUR:
          return 'Professeur';
        case ROLES.USER:
          if (post['user.bot'] === true) {
            return 'IA';
          }
          return 'Étudiant';
        case ROLES.PARENT:
          return 'Parent d\'élève';
        default:
          return 'Inconnu';
      }
    };

    const getUserTag = (post) => {
      if (post['user.bot'] === 1) {
        return '';
      }
      return `[${post['user.username']} - ${userRoleStringGpt(post)}]`;
    };

    const conversationHistoryOpenAi = allDiscussionFlat.map(p => {
      let role = 'user';
      if (p['user.bot'] === 1) {
        role = 'assistant';
      }
      return {
        role,
        content: `${getUserTag(p)} ${p.text}`,
      };
    });

    const fromUser = await models.User.findByPk(userId);

    if (aiUserId === null) {
      const { botsIdsToAutoAnswer } = await PostService.getUserIdsToNotifyAndBotIdsToAutoAnswer(
        { commentaireType, typeIdFromObject: typeId, userId });
      await ChatGptServicePost.handleThreadReplyByAI(post, commentaireType, typeId, botsIdsToAutoAnswer, fromUser, '', true, conversationHistoryOpenAi);
    } else {
      await ChatGptServicePost.handleThreadReplyByAI(post, commentaireType, typeId, [aiUserId], fromUser, '', true, conversationHistoryOpenAi);
    }

    return true;
  },

  async getPostDataForAiFeedback({ postId }, ctx) {
    const post = await models.Post.findByPk(postId);
    const commentaireType = await getCommentaireTypeFromObject(post);
    const typeIdFromObject = await getTypeIdFromObject(post, commentaireType);
    let fromUser = await models.User.findByPk(post.userId, { attributes: ['id', 'username'] });
    const userId = fromUser.id;
    return {
      post, commentaireType, typeIdFromObject, fromUser, userId,
    };
  },

  async getDefaultBotIdToAutoAnswerForPost({ postId }, ctx) {
    const postDataForAiFeedback = await PostService.getPostDataForAiFeedback({ postId }, ctx);
    const botsIdsToAutoAnswer = await PostService.getAllBotsIdsToAutoAnswer(postDataForAiFeedback, ctx);
    return botsIdsToAutoAnswer?.[0] || null;
  },

  async getAllBotsIdsToAutoAnswer({ userId, typeIdFromObject, commentaireType }, ctx) {
    let botsIdsToAutoAnswer = [];
    switch (commentaireType) {
      // Dans COURS/QCM notifie ceux qui ont suscribe aux notifs de l'UE
      // Notifier tuteurs / admins ayant accès au cours
      case CommentairesType.COURS:
        botsIdsToAutoAnswer = await GroupeService.getChatGPTUserIdReponsibleForCoursId(userId, typeIdFromObject);
        break;
      case CommentairesType.QCM:
      case CommentairesType.QUESTION_ANSWER:
        botsIdsToAutoAnswer = await GroupeService.getChatGPTUserIdReponsibleForAnswerId(userId, typeIdFromObject);
        break;
      case CommentairesType.EXERCISE:
        botsIdsToAutoAnswer = await GroupeService.getChatGPTUserIdReponsibleForQuestionId(userId, typeIdFromObject);
        break;
      case CommentairesType.FORUM:
        // Dans Forum notifie toujours admin + tuteurs
        const adminTuteursF = await UserService.getAdminTuteurs();
        break;
      case CommentairesType.EVENT:
        // Dans events notifs tuteurs admins ayant accès aux cours liés à l'event
        botsIdsToAutoAnswer = await GroupeService.getChatGPTUserIdReponsibleForEventId(userId, typeIdFromObject);
        break;
    }
    return botsIdsToAutoAnswer;
  },

  /**
   * Called when admin asks to generate/regenerate an AI answer for a post
   * @param postId
   * @param aiUserId
   * @param additionnalPrompt
   * @param ctx
   * @returns {Promise<boolean>}
   */
  async generateAiAnswerForPost({ postId, aiUserId, additionnalPrompt, originalThreadPostId }, ctx) {
    let botsIdsToAutoAnswer = [aiUserId];

    const threadPost = await models.Post.findByPk(originalThreadPostId);
    if (!threadPost) {
      console.error('Thread post not found (generateAiAnswerForPost), ai answer cant be generated');
    } else {
      // Génère nouvelle réponse IA avec historique conversation
      const post = await models.Post.findByPk(postId);
      await this.handleAIPostReplyWithHistory(threadPost, post, aiUserId);
    }
    /*
      await this.handleAIPostReplyWithHistory(threadPost, newPost);
    */
    //await ChatGptServicePost.handleThreadReplyByAI(post, commentaireType, typeIdFromObject, botsIdsToAutoAnswer, fromUser, additionnalPrompt, true);

    //TODO notify ?
    return true;
  },

  async setAiAnswerVerified(postId, userId) {
    try {
      const post = await models.Post.findByPk(postId);
      if (post.verifiedBy) {
        post.verifiedBy = null; // nonvérifié
      } else {
        post.verifiedBy = userId;  // vérifié
      }
      await post.save();
      return true;
    } catch (e) {
      console.error(e);
    }
  },


  adminSearchPosts: async (_,
                           {
                             filter: {

                               isQuestion = null, // Check si c'est une question en regardant si possède un parentId
                               isReply = null, // Check si c'est une réponse en regardant si possèble PAS un parentId

                               generalCoursIds, // Recherche large sur les cours => On va inclure les posts associés aux [EVENT, QCM] liés à ces cours
                               coursIds, // Recherche stricte sur les cours => On va inclure uniquement les posts associés aux coursId
                               category, // Recherche stricte sur les Catégories ([EVENT, COURS, QCM_ANSWER,EVENT, etc...]
                               categoryExerciceAdvancedFilter, // Recherche stricte sur les Type de contenu pour les 'Série d'exercice'
                               categoryEventAdvancedFilter, // Recherche stricte sur les Type de contenu pour les 'EVENTS'

                               typeIds, //  Recherche stricte sur les PostTypeId, ex : 'Je n'ai pas compris la question', 'Hors sujet', etc...

                               userIds, // Recherche stricte sur les userId créateur des posts
                               participantsUserIds, // Recherche les participants par userId

                               startDateCreationFilter, // Recherche sur la date d'émission
                               endDateCreationFilter,

                               titreFilter, // Recherche sur le titre du post
                               contentFilter, // Recherche sur le contenu du post

                               isResolved, // Check si résolu
                               isAskingForHumanHelp, // Check si le post demande l'aide d'un humain
                               isResolvedByAi, // Check si le post a été RESOLU par une IA
                               iaAnswered, // Check si le post a été REPONDU par une IA

                               // ===== MODERATION FILTERS =====
                               moderationStatus, // Filter par statut de modération (pending/approved/rejected)
                               contentType, // Filter par type de contenu (posts/comments)

                               // Pagination options
                               offset,
                               limit,
                             },
                           }, {
                             me,
                           },
  ) => {

    try {
      // Init du filter
      let where = {};

      // Remplissage du filter pour toutes les conditions indépendantes les unes des autres
      if (isResolved !== null) {
        where = { ...where, isResolved };
      }
      if (iaAnswered !== null) {
        where = { ...where, answeredByAi: iaAnswered };
      }
      if (isAskingForHumanHelp !== null) {
        where = { ...where, isAskingForHumanHelp };
      }
      if (isResolvedByAi != null) {
        where = { ...where, isResolvedByAi };
      }
      if (typeIds) {
        where = { ...where, postTypeId: { [Op.in]: typeIds } };
      }
      if (userIds && userIds.length > 0) {
        where = { ...where, userId: { [Op.in]: userIds } };
      }
      if (startDateCreationFilter && endDateCreationFilter) {
        where = {
          ...where,
          createdAt: { [Sequelize.Op.between]: [new Date(startDateCreationFilter), new Date(endDateCreationFilter)] },
        };
      }
      if (titreFilter && titreFilter !== '') {
        where = {
          ...where,
          titre: sequelize.where(sequelize.fn('LOWER', sequelize.col('title')), 'LIKE', `%${titreFilter?.toLowerCase()}%`),
        };
      }
      if (contentFilter && contentFilter !== '') {
        where = {
          ...where,
          titre: sequelize.where(sequelize.fn('LOWER', sequelize.col('text')), 'LIKE', `%${contentFilter?.toLowerCase()}%`),
        };
      }

      // ===== MODERATION FILTERS =====
      if (moderationStatus) {
        where = { ...where, moderation_status: moderationStatus };
      }

      if (contentType) {
        if (contentType === 'posts') {
          // Posts principaux seulement (parentId = null)
          where = { ...where, parentId: null };
        } else if (contentType === 'comments') {
          // Commentaires seulement (parentId != null)
          where = { ...where, parentId: { [Op.ne]: null } };
        }
        // Si contentType n'est pas spécifié, on inclut tout (posts + commentaires)
      }


      // TODO Si y'a partipantIds faut récup tout sans parentId,
      //  puis sur ces datas il faut recup le threadId original
      //  pour ceux dont parentId est pas null, et enfin retourner
      //  tous ces posts en éliminant tous les parentId non null.

      // gestion de isQuestion et isReply => Les deux conditions sont conditionnelles l'unes à l'autre et s'annulent. Il faut donc gérer leurs cas
      // COTE FRONT PAR DEFAUT IS QUESTION EST TOUJOURS TRUE
      if (isQuestion !== null || isReply !== null) {

        // Soit les deuc sont definies et elles s'opposent et on renvoit rien
        if (isQuestion !== null && isReply !== null && isQuestion === isReply) {
          return [];
        }

        // Si ici, soit les deux sont définies et donnent le même résultat, soit seule une est définie et.
        else if (isQuestion !== null && isQuestion) {
          where = { ...where, parentId: { [Op.eq]: null } }; // Cas par défaut
        } else if (isQuestion !== null && !isQuestion) {
          where = { ...where, parentId: { [Op.ne]: null } };
        } else if (isReply != null && !isReply) {
          where = { ...where, parentId: { [Op.eq]: null } };
        } else if (isReply != null && isReply) {
          where = { ...where, parentId: { [Op.ne]: null } };
        } else {
          throw new Error('isQuestion ou isReply non valide');
        }
      }

      /////////////////////////// GESTION DYNAMIQUE DES IDS. On va filtrer en fonction des différents paramètre, les id des cours / QCMAnswer / Event / Forum / Post

      // Déclaration des PostIds accessible pour les droits de l'user
      let dynamicPostIdFilter = await PermissionService.getAvailablePostIdsForUser(me);

      // Déclaration des placeholders dynamique sur les Ids
      let dynamicCoursIdsFilter = [];
      let dynamicQcmAnswerIdsFilter = [];
      let dynamicEventIdFilter = [];
      let dynamicForumIdFilter = [];


      // Pour chaque type à rechercher, on va init avec toutes les possibilitées que l'on restreindra ensuite
      for (let value of Object.values(CommentairesType)) {
        if (category?.includes(value)) {
          switch (value) {
            case 'COURS':
              dynamicCoursIdsFilter = await models.Cours.findAll({
                attributes: ['id'],
                raw: true,
              }).then(data => data.map(value => value.id));
              break;
            case 'EVENT':
              dynamicEventIdFilter = await models.Event.findAll({
                attributes: ['id'],
                raw: true,
              }).then(data => data.map(value => value.id));
              break;
            case 'FORUM':
              dynamicForumIdFilter = await models.Forum.findAll({
                attributes: ['id'],
                raw: true,
              }).then(data => data.map(value => value.id));
              break;
            case 'QUESTION_ANSWER':
              dynamicQcmAnswerIdsFilter = await models.QuestionAnswers.findAll({
                attributes: ['id'],
                raw: true,
              }).then(data => data.map(value => value.id));
              break;
            default:
              break;
          }
        }
      }

      // Restriction sur les generalCoursIds.
      /* GeneralCoursId => récupération des AnswersId et EventId lié aux cours + la filtration sur les sous-catégories. (on Ingore les forums qui sont delink des cours + qcmIdQcm est outdated) */
      if (generalCoursIds) {
        dynamicCoursIdsFilter = generalCoursIds;
        dynamicEventIdFilter = await EventService.filterEventIdGivenCoursIdAndQcmTypes(categoryEventAdvancedFilter, generalCoursIds);
        dynamicQcmAnswerIdsFilter = await QuestionsService.filterQuestionsAnswerIdGivenCoursIdAndQcmTypes(categoryExerciceAdvancedFilter, generalCoursIds);
      }

      // Si on a une liste strict de cours ID définie, alors on override les autres paramètres
      if (coursIds) {
        dynamicCoursIdsFilter = coursIds;
        dynamicEventIdFilter = [];
        dynamicForumIdFilter = [];
        dynamicQcmAnswerIdsFilter = [];
      }


      // Pour les différentes catégories, on itère sur les types de commentaire (si c'est un COURS, EVENT, FORUM, QUESTION) et dynamiquement on delet :

      // Dans chaque type, y a des types en plus : check Config => type de contenu => exemple
      for (let value of Object.values(CommentairesType)) {
        if (!category?.includes(value)) {
          switch (value) {
            case 'COURS':
              dynamicCoursIdsFilter = [];
              break;
            case 'EVENT':
              dynamicEventIdFilter = [];
              break;
            case 'FORUM':
              dynamicForumIdFilter = [];
              break;
            case 'QUESTION_ANSWER':
              dynamicQcmAnswerIdsFilter = [];
              break;
            default:
              break;
          }
        }
      }

      // Update du filter avec les différents élémentsIds filtrés. Tout ça en condition OU
      where = {
        ...where,
        [Op.or]: [
          { courId: { [Op.in]: dynamicCoursIdsFilter } },
          { answerId: { [Op.in]: dynamicQcmAnswerIdsFilter } },
          { eventId: { [Op.in]: dynamicEventIdFilter } },
          { forumId: { [Op.in]: dynamicForumIdFilter } },
        ],
      };

      /* Ajout du filter sur les IDS  /!\ => Permet de faire la restriction sur les permissions des users*/
      where = { ...where, id: dynamicPostIdFilter };

      const postResults = await models.Post.findAll({
        where: where,
        offset,
        limit,
      });
      //const postWithParentId = postResults.filter(p => p.parentId);
      //console.log({postWithParentId});

      // Réalisation de la requête paginée
      return {
        count:await models.Post.count({
          where:where
        }),
        postResults: postResults
      }

    } catch (e) {
      console.error('error dans adminPostSearchFilter :', e);
      throw new GraphQLError(e);
    }

  },


  async getPostAdditionalInfos(_, { postId }) {
    /* Fonction qui va récupérer :
      - le type de contenu pour les Post de catégorie 'QUESTION_ANSWER' ou 'EVENT'. Returne null pour les autres catégories
      - le nom du cours associé
    */
    try {
      const post = await models.Post.findByPk(postId);

      // Définition de la catégorie du post :
      const category = getCommentaireTypeFromObject(post);

      // Placeholder
      let returnData = { postType: category };

      switch (category) {

        /// Les question Answer
        case CommentairesType.QUESTION_ANSWER:
          const resultQA = await models.QuestionAnswers.findByPk(post.answerId, {
            include: [{
              model: models.Question,
              as: 'question',
              include: [{
                model: models.TypeQcm,
                as: 'type_qcms',
                attributes: ['name'],
              }, {
                model: models.Cours,
                as: 'cours',
                attributes: ['name'],
              }],
              attributes: [],
            }],
            attributes: [],
            raw: true,
          });
          if (resultQA) {
            if (resultQA['question.type_qcms.name']) {
              returnData.postCategoryType = resultQA['question.type_qcms.name'];
            }
            if (resultQA['question.cours.name']) {
              returnData.coursName = resultQA['question.cours.name'];
            }
            if (resultQA['question.cours.id']) {
              returnData.id = resultQA['question.cours.id'];
            }
          }
          break;

        /// Les Events
        case CommentairesType.EVENT:
          const resultEvent = await models.Event.findByPk(post.eventId, {
            include: [{
              model: models.TypeQcm,
              as: 'type_qcms',
              attributes: ['name'],
            }, {
              model: models.Cours,
              as: 'cours',
              attributes: ['name'],
            }],
            attributes: [],
            raw: true,
          });

          if (resultEvent) {
            if (resultEvent['type_qcms.name']) {
              returnData.postCategoryType = resultEvent['type_qcms.name'];
            }
            if (resultEvent['cours.name']) {
              returnData.coursName = resultEvent['cours.name'];
            }
            if (resultEvent['cours.event_cours.coursId']) {
              returnData.coursId = resultEvent['cours.event_cours.coursId'];
            }
          }
          break;


        /// Les Cours
        case CommentairesType.COURS:
          const resultCours = await models.Cours.findByPk(post.courId);

          if (resultCours?.name) {
            returnData.coursName = resultCours.name;
          }
          if (resultCours?.id) {
            returnData.coursId = resultCours.id;
          }

        /// Les Forums
        case CommentairesType.FORUM:
          const resultForum = await models.Forum.findByPk(post.forumId);

          if (resultForum?.name) {
            returnData.forumName = resultForum.name;
          }
      }


      return (returnData);
    } catch (e) {
      console.error('error dans getPostCategoryType :', e);
      return ({});
    }
  },

  async lastReponseFromNonUserOrBot(_, { postId }) {
    try {

      // Check de si il y a des posts répondu avec AI
      const result = await models.Post.findAll({
        where: { parentId: postId },
        include: [{
          model: models.User,
          as: 'user',
          attributes: ['id', 'role', 'bot'],
          where: {
            [Op.or]: [
              { bot: 1 },
              { role: { [Op.in]: ['ADMIN', 'TUTEUR', 'SUB_ADMIN'] } },
            ],
          },
          required: true,
        }],
        order: [['createdAt', 'DESC']],
      });

      if (result.length === 0) {
        return { post: null, role: null };
      } else {
        if (result.length > 1) {
          // console.log('warning : dans lastReponseFromNonUserOrBot, on a plusieurs bot answers et on retourne uniquement la plus récente.')
        }
        const post = result[0];
        let role;

        if (post.user.bot === true || post.user.bot === 1) {
          role = 'AI';
        } else {
          role = ROLES[post.user.role];
        }

        return { post, role };
      }

      return null;
    } catch (e) {
      console.error('error dans lastReponseFromNonUserOrBot, on return null, error :', e);
      return { post: null, role: null };
    }

  },


  async getFiles(post, ctx) {
    try {
      return await models.File.findAll({
        where: {
          fileId: post.id,
        },
      });
    } catch (e) {
      console.error(e);
    }
  },

  // REACTIONS //////////////////////
  async postReaction({postId, emoji}, ctx) {
    try {
      const me = ctx.me;
      const userId = me?.id;
      // Vérifier si l'utilisateur a déjà réagi au post
      const existingSameReaction = await models.PostReactions.findOne({
        where: { postId, userId,
          // emoji
          emoji: emoji
        }, // il a du mal avec les emojis qui se ressemblent
      });

      if (existingSameReaction) {
        const inDb = existingSameReaction.emoji;
        // Si même reaction, enlever reaction
        existingSameReaction.destroy();
        return true;
      }
      // Si c'est pas la même reaction on l'ajoute
      await models.PostReactions.create({ postId, userId, emoji });
      return true;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },
  async getPostReactions({ postId }, ctx) {
    try {
      const userId = ctx?.me?.id;
      const reactions = await sequelize.query(
        `SELECT emoji, COUNT(*) AS count,
            MAX(CASE WHEN userId = :userId THEN 1 ELSE 0 END) AS isUserReaction
          FROM post_reactions
          WHERE postId = :postId
          GROUP BY emoji`,
              {
                replacements: { postId, userId },
                type: Sequelize.QueryTypes.SELECT,
              }
            );
      //console.log(reactions);
      return reactions;
    } catch (e) {
      console.error(e);
    }
  }
};
