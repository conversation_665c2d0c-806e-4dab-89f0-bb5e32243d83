import gql from 'graphql-tag'

export default gql`
    extend type Query {
        posts: [Post!]
        post(id: ID!): Post
        adminSearchPosts(filter:AdminPostSearchFilter):PaginatedPostResults
        latestPosts: [Post]
      
        getYearsAvailableForPostsForCours(coursId: ID): [Int]
        getYearsAvailableForPostsForEvent(eventId: ID): [Int]
        getYearsAvailableForPostsForQcm(qcmId: ID!): [Int]
        getYearsAvailableForPostsForForum(forumId: ID!): [Int]

        "Posts for Cours"
        postsForCours(coursId: ID!, filter: PostFilter): [Post]
        "Posts for Event"
        postsForEvent(eventId: ID!, filter: PostFilter): [Post]
        "Posts for qcm object"
        postsForQcm(qcmId: ID!, filter: PostFilter): [Post]
        "Posts for forum"
        postsForForum(forumId: ID!, filter: PostFilter): [Post]
        "Posts for specific answer"
        postsForAnswer(answerId: ID!, filter: PostFilter): [Post]
        "Posts for question"
        postsForExercise(questionId: ID!, filter: PostFilter): [Post]
        "All answers posts in Mcq"
        postsForAnswersInQcm(qcmId: ID!, filter: PostFilter): [Post]

        "Returns default bot id to auto answer for a post according to admin configuration"
        getDefaultBotIdToAutoAnswerForPost(postId: ID!): ID

        userThreadPosts(userId: ID, offset: Int, limit: Int): PaginatedPostsResponse
        userAnswerPosts(userId: ID, offset: Int, limit: Int): PaginatedPostsResponse
        
        unresolvedForumPosts: [Post]
        
        allUEsUnresolvedPosts: [Post]

        postTypes: [PostType!]
      
        "Permet de récupérer des informations associées au post"
        getPostAdditionalInfos(postId:ID!):AdditionalInfos
        
        "Get post reactions"
        getPostReactions(postId: ID!): [PostReaction]
    }

    extend type Mutation {
        # POSTS
        "Create a post"
        createPost(post: PostInput!): Post!
        updatePost(id: ID!, post: PostInput!): Boolean
        deletePost(id: ID!): Boolean!

        # POST TYPE
        createPostType(postType: PostTypeInput!): PostType!
        updatePostType(id: ID!, postType: PostTypeInput!): Boolean
        deletePostType(id: ID!): Boolean!

        "Like a post"
        likePost(id: ID!): Post!
        "Dislike a post"
        dislikePost(id: ID!): Post!

        "Report a post or a user"
        reportPost(input: ReportPostInput): Boolean
        
        "User gives feedback to AI answer"
        giveFeedbackToAiAnswer(input: FeedbackToAiAnswerInput): Boolean

        "Admin/Professor set AI answer as verified/unverified by him"
        setAiAnswerVerified(postId: ID!): Boolean
        
        "Generate AI answer for a post (manual)"
        generateAiAnswerForPost(postId: ID!, aiUserId: ID, additionnalPrompt: String, originalThreadPostId: ID): Boolean
        
        "User post reaction"
        postReaction(postId: ID!, emoji: String!): Boolean
    }
    
    input FeedbackToAiAnswerInput {
        postId: ID!
        feedback: String!
        "message optionnel si refus"
        message: String
        originalThreadPostId: ID
    }

    type PaginatedPostsResponse {
        posts: [Post]
        count: Int
    }
    
    type AdditionalInfos{
        coursId:ID
        coursName:String
        postType:String
        postCategoryType:String
        forumName:String
    }
    
    enum PostSortModeType {
        TOP
        MOST_RECENT
    }
    input PostFilter {
        year: Int
        sort: PostSortModeType
        text: String
    }
    
    input ReportPostInput {
        postId: ID
        userId: ID
        reason: String!
    }

    input PostInput {
        title: String
        text: String
        tag: String
        views: Int
        likes: Int
        "Mentioned user ids to notify"
        mentionedUserIds: [ID]
        userId: ID
        courId: ID
        qcmId: ID
        qcmIdQcm: ID
        parentId: ID
        forumId: ID
        answerId: ID
        eventId: ID
        postTypeId: ID
        questionId: ID
        "legacy file"
        file: Upload
        "legacy file image"
        fileImage: Upload
        isResolved: Boolean
        isAskingForHumanHelp: Boolean
        answeredByAi: Boolean
        isResolvedByAi: Boolean
        verifiedBy: ID
        state: String

        "Multi fichiers"
        fileList: [Upload]
    }
    type Post {
        id: ID
        title: String
        text: String
        tag: String
        views: Int
        likes: Int
        user: User
        cours: Cours
        "legacy file"
        file: File
        "legacy file"
        fileImage: File
        "Multi fichiers"
        fileList: [File]
        
        qcm: Qcm
        forum: Forum
        parent: Post
        # nécessaire pour auto nesting frontend (réponses de réponses de réponses)
        parentId: ID
        # valeurs: 'POST_LIKE' ou 'POST_DISLIKE' 
        myLastLikeAction: String
        like_histories: [LikeHistory]
        createdAt: Date
        updatedAt: Date
        threadId: ID
        threadPost: Post
        courId: ID
        qcmIdQcm: ID
        forumId: ID
        answerId: ID
        userId: ID
        eventId: ID
        questionId: ID
        answer: Answer
        type: PostType
        isResolved: Boolean
        ip: String
        "only for AI answers, state of the answer: 'pending', 'accepted', 'rejected'"
        state: String
        "User to whom AI replied to"
        userIdForAiFeedback: ID
        isAskingForHumanHelp: Boolean
        answeredByAi: Boolean
        isResolvedByAi: Boolean
        "Ai answer verified by"
        verifiedBy: ID
        lastAnswer:Int,
        "infère et récupère la réponse 'validée' (du bot / tuteur/super admin) "
        approuvedResponse:ApprouvedResponse
    }
    
    type ApprouvedResponse {
        lastResponseFromNonUserOrBot:Post
        lastResponseRole:String
    }
    
    type PostType {
        id: ID
        name: String
        image: String
        "comment type: class, mcq, answer, etc"
        type: String
        
        precisionPromptForChatGPT: String
        firstAnswerByChatGPT: Boolean
        otherAnswersByChatGPT: Boolean
    }
    input PostTypeInput {
        name: String
        image: Upload
        type: String

        precisionPromptForChatGPT: String
        firstAnswerByChatGPT: Boolean
        otherAnswersByChatGPT: Boolean
    }
    type LikeHistory {
        id: ID
        userId: ID
        postId: ID
        type: String
        updatedAt: Date
    }
    
    "Le type de filter à fournir pour avoir les Posts"
    input AdminPostSearchFilter {
        "Si le thread est indiqué comme résolved"
        isResolved:Boolean
      
        "Si il s'agit de la première question"
        isQuestion:Boolean 
      
        "Si il s'agit d'autre chose que la première question" 
        isReply:Boolean
      
        "Filtre inclusif sur les cours : On va chercher les QcmAnswerID / EventId reliés à ces cours là"
        generalCoursIds:[ID]
      
        "Filtre strict sur les cours Id"
        coursIds:[ID]
      
        "Filtre sur le type de commantaire ('erreur sur la question',   'La correction est ambigue'   , 'La Question est hors concours')"
        typeIds:[ID]
      
        "Filtre sur la catégorie à laquelle est relié le Post (Cours, QcmAnswer, Event, Forum)"
        category:[String]
      
        "Filtre la catégorie Exercice sur leur type de contenu d'exercice (Annale, Médibox, Galien)"
        categoryExerciceAdvancedFilter:[ID]
      
        "Filtre la catégorie Event sur leur type de contenu d'exercice (évènements Médibox, évènements Galien)"
        categoryEventAdvancedFilter:[ID]  
      
        "Filtration pour les UserIds ayant écrit les Posts"
        userIds:[ID]
        "User ids participants"
        participantsUserIds:[ID]
      
        "Boolean pour filtrer si répondu par l'IA ou pas"
        iaAnswered:Boolean
        
        "Les deux dates de filtre"
        startDateCreationFilter:Date
        endDateCreationFilter:Date      
      
        "Filtre pour que la string 'titreFilter' soit contenu dans le titre du Post"
        titreFilter:String
      
        "Filtre pour que la string 'contentFilter' soit contenu dans le text du Post"
        contentFilter:String
      
        "Boolean pour filtrer les questions nécessitant une aide humaine"
        isAskingForHumanHelp:Boolean
        isResolvedByAi:Boolean

        "Filter by moderation status"
        moderationStatus:String

        "Filter by content type"
        contentType:String

        "Pagination Options"
        offset:Int!
        limit:Int!
    }
    
    type PaginatedPostResults{
      count: Int
      postResults:[Post]
    }
    
    type PostReaction {
        emoji: String
        count: Int
        isUserReaction: Boolean
    }
    
`
