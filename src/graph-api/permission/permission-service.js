import {GraphQLError} from 'graphql';
import Sequelize from 'sequelize'
import models, { sequelize } from '../../models';
import { CONFIG_KEYS } from '../../models/config.js'
import {LOG_OPERATIONS} from '../../models/log/log.js';
import {RedisService} from '../../service/redis-service.js';
import {ROLES} from '../authorization.js';
import {UploadService} from '../file/upload-service.js';
import {GroupeService} from '../groupe/groupe-service.js';
import LogService from '../log/log-service.js';
import {QCMService} from '../qcm/qcm-service.js';
import {QuestionsService} from '../qcm/questions/questions-service.js';
import {QcmTypeService} from '../qcm/type/qcm-type-service.js';
import {UEService} from '../ue/ue-service';
import {recaptcha<PERSON>ri<PERSON><PERSON><PERSON>, recaptchaValidationUrl} from '../../util/utils.js';
import axios from 'axios'

const { Op } = Sequelize;

const Groupes = {
  async getUserGroups(user) {
    /*
    const REDIS_KEY = `user-groups-${user.id}`
    let cachedValue = await RedisService.get(REDIS_KEY)
    if(cachedValue) {
      return cachedValue
    }
    */
    const userGroupes = await models.UserGroups.findAll({
      where: {
        userId: user.id,
      },
      attributes: ['groupeId'],
      raw: true,
    });
    const groups = await models.Groupe.findAll({
      where: {
        id: userGroupes.map(u => u.groupeId),
      },
    });
    return groups;
  },
};

const CoursTypeQcmSettings = {
  async getTypeqcmIdsForCoursModule({ coursId, coursModuleType }) {
    const settings = await models.CoursTypesQcmSettings.findAll({
        where: {
          coursId,
          coursModuleType,
        },
      },
    );
    return settings.map(s => s.typeQcmId);
  },

};

const FormationElement = {

  // TODO optimize + cache
  async getAllowedFormationElementsForUser(elements, userId) {
    // Check if user has access to elements
    let elementsToReturn = [];
    let user = await models.User.findByPk(userId);
    if (user.role === ROLES.ADMIN) {
      // Admins are seeing all elements
      elementsToReturn = elements;
    } else {
      const userGroups = await PermissionService.Groupe.getUserGroups({ id: userId });
      for (const element of elements) {
        if (element.isAccessible) {
          elementsToReturn.push(element);
        } else {
          /////////
          const elementGroups = await models.FormationElementGroups.findAll({
            where: {
              formationElementId: element.id,
            },
          });
          const elementsGroupsIds = elementGroups.map(g => g.groupeId);
          const userGroupIds = userGroups.map(g => g.id);
          const hasAccess = userGroupIds.some(g => elementsGroupsIds.includes(g));
          if (hasAccess) {
            elementsToReturn.push(element);
          }
        }
      }
    }

    return elementsToReturn;
  },
};

const PermissionIdsGetters = {

  async getAvailableQuestionIdsByTypeQcmAndUe() {

  },

  /*
  * @param {models.User} user , we need only the id and role
  * */
  async getAvailableUEIdsForUser(user) {
    try {
      const REDIS_KEY = `getAvailableUEIdsForUser-${user.id}`;
      let cachedValue = await RedisService.get(REDIS_KEY);
      if (cachedValue) {
        return cachedValue;
      }

      if (user.role === ROLES.ADMIN) {
        // Super admin
        const ues = await models.UE.findAll({ attributes: ['id'] });
        const ueIds = ues.map(ue => ue.id);
        await RedisService.set(REDIS_KEY, ueIds);
        return ueIds;
      }
      const groupes = await PermissionService.Groupe.getUserGroups(user);
      const ueGroups = await models.UEGroups.findAll({
        attributes: ['ueId'],
        where: {
          groupeId: groupes.map(g => g.id),
        },
      });

      const ueIds = ueGroups.map(u => u.ueId);
      await RedisService.set(REDIS_KEY, ueIds);
      return ueIds;
    } catch (e) {
      console.error(e);
    }
  },
  async getAvailableCategoryIdsInUEForUser(ueId, user) {
    try {
      const REDIS_KEY = `AvailableCategIdsInUE-${ueId}-forUser-${user.id}`;
      let cachedValue = await RedisService.get(REDIS_KEY);
      if (cachedValue) {
        return cachedValue;
      }
      const groupes = await PermissionService.Groupe.getUserGroups(user);
      const categsInUe = await models.UECategory.findAll({
        attributes: ['id'],
        where: {
          ueId: ueId,
        },
        //order: [['order', 'ASC']],
      });
      let ueCateg;
      if (user.role === ROLES.ADMIN) {
        const categIds = categsInUe?.map(c => c.id);
        await RedisService.set(REDIS_KEY, categIds);
        return categIds;
      } else {
        ueCateg = await models.UECategoryGroups.findAll({
          attributes: ['uecategory_id'],
          where: {
            groupeId: groupes.map(g => g.id),
            uecategory_id: categsInUe.map(c => c.id),
          },
        });
      }
      const categIds = ueCateg.map(u => u.uecategory_id);
      await RedisService.set(REDIS_KEY, categIds);
      return categIds;
    } catch (e) {
      console.error(e);
    }
  },
  // ALL Categories
  /**
   * @param {models.User} user
   */
  async getAvailableCategoryIdsForUser(user) {
    try {
      const REDIS_KEY = `AvailableCategoryIdsForUser-${user?.id}`;
      let cachedValue = await RedisService.get(REDIS_KEY);
      if (cachedValue) {
        return cachedValue;
      }
      let toReturn, groupes, ueCateg;
      if (user?.role === ROLES.ADMIN) {
        // Super admin
        const ueCategss = await models.UECategory.findAll({ attributes: ['id'], raw: true });
        toReturn = ueCategss?.map(e => e.id);
      } else {
        groupes = await PermissionService.Groupe.getUserGroups(user);
        ueCateg = await models.UECategoryGroups.findAll({
          attributes: ['uecategory_id'],
          where: {
            groupeId: groupes.map(g => g.id),
          },
          raw: true,
        });
        toReturn = ueCateg.map(u => u.uecategory_id);
      }
      await RedisService.set(REDIS_KEY, toReturn);
      return toReturn;
    } catch (e) {
      console.error(e);
    }
  },

  /**
   * Just in current category
   *
   * @param {number|array} ueCategoryId
   * @param {models.User} user
   */
  async getAvailableCoursIdsInUECategoryForUser(ueCategoryId, user) {
    try {
      const REDIS_KEY = `AvailCoursIdsInUECateg-${ueCategoryId}-ForUser-${user.id}`;
      let cachedValue = await RedisService.get(REDIS_KEY);
      if (cachedValue) {
        return cachedValue;
      }
      const groupes = await PermissionService.Groupe.getUserGroups(user);
      const coursInCateg = await models.Cours.findAll({
        attributes: ['id'],
        where: {
          uecategoryId: ueCategoryId,
          deleted: false,
        },
      });
      if (user?.role === ROLES.ADMIN) {
        // Super admin
        const coursIds = coursInCateg.map(c => c.id);
        await RedisService.set(REDIS_KEY, coursIds);
        return coursIds;
      } else {
        const ueCateg = await models.CoursGroups.findAll({
          attributes: ['coursId'],
          where: {
            groupeId: groupes.map(g => g.id),
            coursId: coursInCateg.map(c => c.id),
          },
        });
        const coursIds = ueCateg.map(u => u.coursId);
        await RedisService.set(REDIS_KEY, coursIds);
        return coursIds;
      }
    } catch (e) {
      console.error(e);
    }
  },

  /**
   * In current category and child categories
   *
   * @param {number|array} ueCategoryId
   * @param {models.User} user
   */
  async getAvailableCoursIdsInUECategoryForUserRecursive(ueCategoryId, user) {
    try {
      const REDIS_KEY = `AvailCoursIdsRecursiveInUECateg-${ueCategoryId}-ForUser-${user.id}`;
      let cachedValue = await RedisService.get(REDIS_KEY); // parfois entraine un miss => Si on crée un dossier/cours , ça miss
      if (cachedValue) {
        return cachedValue;
      }
      const groupes = await PermissionService.Groupe.getUserGroups(user);
      const coursInCateg = await models.Cours.findAll({
        attributes: ['id'],
        where: {
          uecategoryId: ueCategoryId,
          deleted: false,
        },
      });

      let coursIdsInCateg = coursInCateg.map(c => c.id);

      const childCategs = await models.UECategory.findAll({
        attributes: ['id'],
        where: {
          parentId: ueCategoryId,
        },
        raw: true,
      });

      const childCategsIds = childCategs.map(c => c.id);
      for (const childCategId of childCategsIds) {
        const coursIdsInChildCateg = await PermissionService.getAvailableCoursIdsInUECategoryForUserRecursive(childCategId, user);
        coursIdsInCateg = [...coursIdsInCateg, ...coursIdsInChildCateg];
      }

      if (user?.role === ROLES.ADMIN) {
        // Super admin
        await RedisService.set(REDIS_KEY, coursIdsInCateg);
        return coursIdsInCateg;
      } else {
        const coursGroups = await models.CoursGroups.findAll({
          attributes: ['coursId'],
          where: {
            groupeId: groupes.map(g => g.id),
            coursId: coursIdsInCateg,
          },
        });
        const coursIds = coursGroups.map(u => u.coursId);
        await RedisService.set(REDIS_KEY, coursIds);
        return coursIds;
      }
    } catch (e) {
      console.error(e);
    }
  },

  /**
   * @param {number|array} ueCategoryId
   * @param {models.User} user
   */
  async getAvailableCoursIdsInUECategoryForUserLegacy(ueCategoryId, user) {
    try {
      const REDIS_KEY = `AvailCoursIdsInUECateg-${ueCategoryId}-ForUser-${user.id}`;
      let cachedValue = await RedisService.get(REDIS_KEY);
      if (cachedValue) {
        return cachedValue;
      }
      const groupes = await PermissionService.Groupe.getUserGroups(user);
      const coursInCateg = await models.Cours.findAll({
        attributes: ['id'],
        where: {
          uecategoryId: ueCategoryId,
          deleted: false,
        },
      });
      if (user.role === ROLES.ADMIN) {
        // Super admin
        const coursIds = coursInCateg.map(c => c.id);
        await RedisService.set(REDIS_KEY, coursIds);
        return coursIds;
      } else {
        const ueCateg = await models.CoursGroups.findAll({
          attributes: ['coursId'],
          where: {
            groupeId: groupes.map(g => g.id),
            coursId: coursInCateg.map(c => c.id),
          },
        });
        const coursIds = ueCateg.map(u => u.coursId);
        await RedisService.set(REDIS_KEY, coursIds);
        return coursIds;
      }
    } catch (e) {
      console.error(e);
    }
  },

  // COURS IDS IN ALL UE legacy
  async getAvailableCoursIdsInUEForUserLegacy(ueId, user) {
    try {
      const REDIS_KEY = `AvailableCoursIdsInUEForUser-${ueId}-ForUser-${user.id}`;
      let cachedValue = await RedisService.get(REDIS_KEY);
      if (cachedValue) {
        return cachedValue;
      }
      const ueCategIds = await PermissionService.getAvailableCategoryIdsInUEForUser(ueId, user);
      let coursId = await PermissionService.getAvailableCoursIdsInUECategoryForUser(ueCategIds, user);

      await RedisService.set(REDIS_KEY, coursId);
      return coursId;
    } catch (e) {
      console.error(e);
    }
  },

  async getAvailableCategoryIdsInCategoryForUser(ueCategoryId,user){
    /* Fonction qui recupère les categoryId available pour un user à partir d'une categoryId */
    try {

      const REDIS_KEY=`AvailableCategoryIdInCategoryId-${ueCategoryId}-ForUser-${user.id}`;
      let cachedValue = await RedisService.get(REDIS_KEY);
      if (cachedValue) {
        return cachedValue;
      }

      // Récupération des categories Authorisées
      const authorizedCategoryId = await PermissionService.getAvailableCategoryIdsForUser(user);

      // Filtration des category avec la categorie Parente et les categories Authorisées
      const availableCategoryId = await models.UECategory.findAll({
        attributes: ['id'],
        where: {
          id: authorizedCategoryId,
          parentId:ueCategoryId,
          isVisible: true,
        },
        raw: true,
      }).then(values => values?.map(value => value.id));

      await RedisService.set(REDIS_KEY,availableCategoryId)
      return availableCategoryId
    }catch(e){
      console.error(e)
    }
  },

  async getAllAvailableCategoryIdsInCategoryForUserRecursively(ueCategoryId,user){
    /* Fonction qui récupère récursivement toutes les categoryId authorisés pour un user liés à une categoryId */
    /* Gaffe à une boucle infinie */
    try {

      // Gestion du cache
      const REDIS_KEY=`AllAvailableCategoryIdForCategoryId-${ueCategoryId}-ForUser-${user.id}`
      let cachedValue = await RedisService.get(REDIS_KEY);
      if (cachedValue) {return cachedValue;}

      // gestion de la récursion
      let searchedIds=[]
      let toSearchIds=[ueCategoryId]
      let depth=0
      const MAX_DEPTH=20

      // Init de la récursion
      while (toSearchIds.length!==0 && depth<MAX_DEPTH){

        // Lancer la récursion en paralèlle
        const tempToSearchIds = await Promise.all(
          toSearchIds.map(searchId => {
            return PermissionService.getAvailableCategoryIdsInCategoryForUser(searchId, user);
          })
        );

        // Flatten de la récursion
        const flattenedTempToSearchIds = [].concat(...tempToSearchIds);

        searchedIds = [...new Set([...searchedIds, ...flattenedTempToSearchIds])];
        toSearchIds = flattenedTempToSearchIds;
        depth++
      }

      await RedisService.set(REDIS_KEY,searchedIds)
      return searchedIds

    }catch(e){
      console.error(e)
    }
  },

  // New version infinite hierarchy
  async getAvailableCoursIdsInUEForUser(ueId, user) {
    try {
      const REDIS_KEY = `AvailableCoursIdsInUEForUser-${ueId}-ForUser-${user.id}`;
      let cachedValue = await RedisService.get(REDIS_KEY);
      if (cachedValue) {
        return cachedValue;
      }
      // Get cours ids directly parent to UE if any
      const coursIdsDirectlyInUE = await PermissionService.getCoursesIdsInUE(ueId, user) || [];

      /////////////////////////////////////
      // Get cours ids in childs UEs if any
      const childsUEs = await models.UE.findAll({
        attributes: ['id'],
        where: {
          parentId: ueId,
        },
        raw: true,
      });
      const childsUEsIds = childsUEs?.map(c => c.id) || [];
      let coursIdsInChildsUEsAndCategs = [];
      for (const childUEId of childsUEsIds) {
        const coursIdsInChildUE = await PermissionService.getAvailableCoursIdsInUEForUser(childUEId, user) || [];
        coursIdsInChildsUEsAndCategs = [...coursIdsInChildsUEsAndCategs, ...coursIdsInChildUE];
      }
      /////////////////////////////////////
      // Pareil pour les categories

      // Récupération des catégories ayant l'ueID en parent.
      const ueCategIdsDirectlyInUE = await PermissionService.getAvailableCategoryIdsInUEForUser(ueId, user);

      // Récupération récursives en parralèle des categoriesUE étant liées aux categoriesUE de l'UE
      const ueCategIdsInUeCategIdsDirectlyInUe = await Promise.all(

        ueCategIdsDirectlyInUE.map(id => PermissionService.getAllAvailableCategoryIdsInCategoryForUserRecursively(id, user))
      );

      // Réalisation des promises et formatage du retour
      const flattenedUeCategIdsInUeCategIdsDirectlyInUe = [].concat(...ueCategIdsInUeCategIdsDirectlyInUe);

      // Concaténation des categories des UE, et des catégories des catégories
      const allUeCategIds=[...new Set([...ueCategIdsDirectlyInUE,...flattenedUeCategIdsInUeCategIdsDirectlyInUe])]

      for (const ueCategId of allUeCategIds) {
        const coursIdsInChildCateg = await PermissionService.getAvailableCoursIdsInUECategoryForUserRecursive(ueCategId, user) || [];
        coursIdsInChildsUEsAndCategs = [...coursIdsInChildsUEsAndCategs, ...coursIdsInChildCateg];
      }

      /////////////////////////////////////
      // Make it unique
      const uniqueCoursesIds = [...new Set([...coursIdsDirectlyInUE, ...coursIdsInChildsUEsAndCategs])];

      await RedisService.set(REDIS_KEY, uniqueCoursesIds);
      return uniqueCoursesIds;
    } catch (e) {
      console.error(e);
    }
  },
  /* Courses IDs in ue without childs */
  async getCoursesIdsInUE(ueId, me) {
    const coursIds = await PermissionService.getAllCoursIdsAvailableForUser(me);
    const cours = await models.Cours.findAll({
      attributes: ['id'],
      where: {
        id: coursIds,
        ueId: ueId,
        deleted: false,
      },
      raw: true,
    });
    return cours?.map(c => c.id);
  },

  async getLinkedCoursesToShowForUser(coursLinked, me) {
    const debug = false;
    if(debug) {
      return coursLinked;
    }

    if([ROLES.USER].includes(me.role)) {
      // Pour cours liés afficher que ceux auxquels le user a accès
      const authorizedCoursIds = await PermissionService.getAllCoursIdsAvailableForUser(me);
      coursLinked = coursLinked.filter(c => authorizedCoursIds.includes(c.id));
      coursLinked = coursLinked.slice(0, 1); // limiter à un
    } else if([ROLES.ADMIN, ROLES.SUB_ADMIN, ROLES.TUTEUR].includes(me.role)) {
      const authorizedCoursIds = await PermissionService.getAllCoursIdsAvailableForUser(me);
      // Séparer les cours en originaux et importés
      const allAccessibleCourses = coursLinked.filter(c => authorizedCoursIds.includes(c.id));
      const accessibleOriginalCourses = allAccessibleCourses.filter(c => c.targetCoursId === null);
      const accessibleImportedCourses = allAccessibleCourses.filter(c => c.targetCoursId !== null);
      let accessibleCoursesToShow = [];
      // Map pour les cours originaux et leur cours importés
      const originalCoursesMap = new Map();
      accessibleOriginalCourses.forEach(course => {
        originalCoursesMap.set(course.id, { original: course, imported: [] });
      });
      accessibleImportedCourses.forEach(course => {
        const originalCourseId = course.targetCoursId;
        if (originalCoursesMap.has(originalCourseId)) {
          originalCoursesMap.get(originalCourseId).imported.push(course);
        } else {
          // Si l'original n'est pas accessible, on considère les importés directement
          originalCoursesMap.set(originalCourseId, { original: null, imported: [course] });
        }
      });
      // Construire la liste des cours à retourner
      originalCoursesMap.forEach(({ original, imported }) => {
        if (original) {
          accessibleCoursesToShow.push(original);
        } else if (imported.length > 0) {
          accessibleCoursesToShow.push(imported[0]); // Ajouter le premier cours importé
        }
      });
      coursLinked = accessibleCoursesToShow;
    }
    return coursLinked;
  },

  /* Just all cours ID available to user */
  async getAllCoursIdsAvailableForUser(user,adminOverride=true) {
    /* Admin override permet de savoir si on considère l'user comme admin pour la récupération des cours
    * Cela est important car un Admin va avoir accès à des cours qui ne sont plus liés à des groupes, et parfois, on veut avoir tous les cours accessibles aux users lambda
    */
    try {
      const REDIS_KEY = `AvailableCoursIds-ForUser-${adminOverride}-${user.id}`;
      let cachedValue = await RedisService.get(REDIS_KEY);
      if (cachedValue) {
        return cachedValue;
      }
      if (adminOverride===true && user.role === ROLES.ADMIN) {
        const allCourses = await models.Cours.findAll({ attributes: ['id'], raw: true });
        const coursIds = allCourses.map(c => c.id);
        await RedisService.set(REDIS_KEY, coursIds);
        return coursIds;
      }
      const groupes = await PermissionService.Groupe.getUserGroups(user);
      const coursGroups = await models.CoursGroups.findAll({
        attributes: ['coursId'],
        where: {
          groupeId: groupes.map(g => g.id),
        },
      });
      const coursIds = [...new Set(coursGroups.map(c => c.coursId))];
      await RedisService.set(REDIS_KEY, coursIds);
      return coursIds;
    } catch (e) {
      console.error(e);
    }
  },

  async getAvailablePostIdsForUser(user) {
    try {
        const REDIS_KEY = `AvailablePostIds-ForUser-${user.id}`; // Définition dynamique d'une key reddis Unique
        let cachedValue=await RedisService.get(REDIS_KEY);
        if (cachedValue){return cachedValue}


        if (user.role === ROLES.ADMIN){
          /// SuperAdmin => On retourne tous les ID de post
          const allPosts = await models.Post.findAll({attributes:['id'],raw:true});
          const postIds= allPosts.map(c=>c.id);
          await RedisService.set(REDIS_KEY,postIds)
          return postIds
        } else {

          // On obtient chaque ID lié aux posts Id
          const allCoursIds = await PermissionService.getAllCoursIdsAvailableForUser(user);
          const allMcqIds = await PermissionService.getAvailableMcqIdsForUser(user);
          const allForumIds = await PermissionService.getAvailableForumIdsForUser(user);
          const allEventIds = await EventPermissions.getEventIdsAuthorizedForUser(user.id);
          const allMcqAnswerIds = await PermissionService.getAvailableMcqAnswerIdsForUser(user)

          // Utilisez les opérateurs OR et IN dans la clause WHERE
            const allPosts = await models.Post.findAll({
              where: {
                [Op.or]: [
                  { courId: { [Op.in]: allCoursIds } },
                  { qcmIdQcm: { [Op.in]: allMcqIds } },
                  { forumId: { [Op.in]: allForumIds } },
                  { eventId: { [Op.in]: allEventIds } },
                  { answerId: { [Op.in]: allMcqAnswerIds } }
                ]
              }
            });
            const allPostIds = allPosts.map(c=>c.id);

          await RedisService.set(REDIS_KEY,allPostIds)
          return allPostIds
        }

    } catch (e) {
      console.error(e);
    }
  },

  // Individual object permissions
  /*
  * returns Boolean
  * */
  async isCoursAvailableForUser(coursId, user) {
    const REDIS_KEY = `cours-${coursId}availableFor-${user.id}`;
    let cachedValue = await RedisService.get(REDIS_KEY);
    if (cachedValue) {
      return cachedValue;
    }
    if (user.role === ROLES.ADMIN) {
      return true;
    }
    const groupes = await PermissionService.Groupe.getUserGroups(user);
    const cp = await models.CoursGroups.findOne({
      where: {
        groupeId: groupes.map(g => g.id),
        coursId: coursId,
      },
    });
    await RedisService.set(REDIS_KEY, !!cp);
    return !!cp;
  },

  // NOT USED YET
  async isUEAvailableForUser(ueId, user) {
    if (user.role === ROLES.ADMIN) {
      return true;
    }
    const groupes = await PermissionService.Groupe.getUserGroups(user);
    const cp = await models.UEGroups.findOne({
      where: {
        groupeId: groupes.map(g => g.id),
        ueId: ueId,
      },
    });
    return !!cp;
  },

  async isUECategoryAvailableForUser(ueCategoryId, user) {
    const REDIS_KEY = `UECategory-${ueCategoryId}availableForUser-${user.id}`;
    if (user.role === ROLES.ADMIN) {
      return true;
    }
    let cachedValue = await RedisService.get(REDIS_KEY);
    if (cachedValue) {
      return cachedValue;
    }
    const groupes = await PermissionService.Groupe.getUserGroups(user);
    const ueCateg = await models.UECategoryGroups.findOne({
      where: {
        groupeId: groupes.map(g => g.id),
        uecategory_id: ueCategoryId,
      },
    });
    const isAuthorized = !!ueCateg;

    await RedisService.set(REDIS_KEY, isAuthorized);
    return isAuthorized;
  },

  // TYPES QCM AVAILABLE FOR USER
  async getAvailableTypeQcmsForUser(user) {
    try {
      const REDIS_KEY = `typesQcmUser-${user.id}`;
      if (user.role === ROLES.ADMIN) {
        // Super admin
        const allTypes = await models.TypeQcm.findAll();
        return allTypes;
        // or return empty array for optimization ?
      } else {
        let cachedValue = await RedisService.get(REDIS_KEY);
        if (cachedValue) {
          return cachedValue;
        }
        const groupes = await PermissionService.Groupe.getUserGroups(user);
        const typeQcmGroup = await models.TypeQcmGroups.findAll({
          attributes: ['typeQcmId'],
          where: { groupeId: groupes.map(g => g.id) },
        });
        const allTypes = await models.TypeQcm.findAll({
          where: {
            id: typeQcmGroup.map(t => t.typeQcmId),
          },
        });
        await RedisService.set(REDIS_KEY, allTypes);
        return allTypes;
      }
    } catch (e) {
      console.error(e);
    }
  },

  // Séries disponibles pour utilisateur, par type de série accessibile
  async getAvailableMcqIdsForUser(user) {
    try {
      const REDIS_KEY = `availableMcqIdsForUser-${user.id}`;
      let cachedValue = await RedisService.get(REDIS_KEY);
      if (cachedValue) {
        return cachedValue;
      }
      if (user.role === ROLES.ADMIN) {
        // Super admin
        const mcq = await models.Qcm.findAll({
          attributes: ['id_qcm'],
        });
        const mcqIds = mcq?.map(q => q.id_qcm);
        await RedisService.set(REDIS_KEY, mcqIds);
        return mcqIds;
        // or return empty array for optimization ?
      } else {
        const groupes = await PermissionService.Groupe.getUserGroups(user);
        const typeQcmGroup = await models.TypeQcmGroups.findAll({
          attributes: ['typeQcmId'],
          where: { groupeId: groupes.map(g => g.id) },
        });
        const qcmTypeQcm = await models.QcmTypeQcm.findAll({
          where: {
            typeQcmId: typeQcmGroup.map(t => t.typeQcmId),
          },
        });
        const mcqIds = qcmTypeQcm.map(u => u.qcmId);
        await RedisService.set(REDIS_KEY, mcqIds);
        return mcqIds;
      }
    } catch (e) {
      console.error(e);
    }
  },

  // BY TYPE QCM
  async getAvailableQuestionsIdsByTypeQcmForUser(user) {
    try {
      const REDIS_KEY = `availableQuestionsIdsForUser-${user.id}`;
      let cachedValue = await RedisService.get(REDIS_KEY);
      if (cachedValue) {
        return cachedValue;
      }
      if (!user.role) {
        user = await models.User.findByPk(user?.id, { raw: true });
      }
      if (user.role === ROLES.ADMIN) {
        // Super admin, all questions
        const questions = await models.Question.findAll({
          attributes: ['id_question'],
          raw: true,
        });
        const questionIds = questions?.map(q => q.id_question);
        await RedisService.set(REDIS_KEY, questionIds);
        return questionIds;
        // or return empty array for optimization ?
      } else {
        // Not admin
        const groupes = await PermissionService.Groupe.getUserGroups(user);
        const typeQcmGroup = await models.TypeQcmGroups.findAll({
          attributes: ['typeQcmId'],
          where: { groupeId: groupes.map(g => g.id) },
        });
        const qcmTypeQcm = await models.QuestionTypeQcm.findAll({
          where: {
            typeQcmId: typeQcmGroup.map(t => t.typeQcmId),
          },
        });
        const questionIds = qcmTypeQcm.map(u => u.questionId);
        await RedisService.set(REDIS_KEY, questionIds);
        return questionIds;
      }
    } catch (e) {
      console.error(e);
    }
  },


  // TODO unfinished (unused??)
  async getAvailableMcqIdsInUEForUser(ueId, user) {
    try {
      if (user.role === ROLES.ADMIN) {
        // Super admin
        const mcqs = await models.Qcm.findAll({ attributes: ['id_qcm'], where: { UEId: ueId } });
        return mcqs?.map(q => q.id_qcm);
      }
      const groupes = await PermissionService.Groupe.getUserGroups(user);
      const ueGroups = await models.UEGroups.findAll({
        attributes: ['ueId'],
        where: {
          groupeId: groupes.map(g => g.id),
        },
      });
      return ueGroups.map(u => u.ueId);
    } catch (e) {
      console.error(e);
    }
  },


  isUserAuthorizedForMcq: async (user, qcm) => {
    try {
      if (user.role === ROLES.ADMIN) {
        return true;
      }
      const REDIS_KEY = `isUser-${user.id}-authorizedForMcq-${qcm.id_qcm}`;
      let cachedValue = await RedisService.get(REDIS_KEY);
      if (cachedValue) {
        return cachedValue;
      }
      const userGroups = await PermissionService.Groupe.getUserGroups(user);
      // les types du qcm
      const typesQcm = await models.QcmTypeQcm.findAll({
        attributes: ['typeQcmId'],
        where: {
          qcmId: qcm.id_qcm,
        },
      });
      // Est-ce que le user a les groupes requis pour ces type qcm
      // Groupes autorisés pour ce qcm
      const groupesInTypeQcm = await QcmTypeService.getGroupesInTypesQcm(typesQcm.map(t => t.typeQcmId));
      const isAuthorizedByTypeQcm = await PermissionService.isUserAmongGroups(userGroups, groupesInTypeQcm);

      const isAuthorized = !!isAuthorizedByTypeQcm;
      await RedisService.set(REDIS_KEY, isAuthorized);
      return isAuthorized;

      /*
      Avant:
      let mesGroupes = await user.getGroupes() // get ues accessibles pour mes groupes
      let mesUEs = await UEService.getUEsPourLesGroupes(mesGroupes)
      // accès restreint et c'est lui qui l'a créé
      if (await PermissionService.hasRestrictedAccessQcm(user?.id) && qcm?.pseudo_createur === user.username) {
        // Only the one he created
        return !!(await PermissionService.isOneUeInUEId(mesUEs, qcm?.UEId))
      }
      // accès restreint et c'est pas lui qui l'a créé
      else if (await PermissionService.hasRestrictedAccessQcm(user?.id) && qcm?.pseudo_createur !== user.username) {
        return false
      }
      // Si l'UE est dans les UE autorisées
      return !!(await PermissionService.isOneUeInUEId(mesUEs, qcm?.UEId))
       */

    } catch (e) {
      console.error(e);
      throw new GraphQLError(`Erreur lors de la vérification d'autorisation de l'utilisateur pour accéder au QCM ${qcm.id_qcm}`);
    }
  },

  async getAvailableFormationsIdsForUser(user) {
    try {
      const REDIS_KEY = `getAvailableFormationIdsForUser-${user.id}`;
      let cachedValue = await RedisService.get(REDIS_KEY);
      if (cachedValue) {
        return cachedValue;
      }
      if (user.role === ROLES.ADMIN) {
        // Super admin
        const formations = await models.Formation.findAll({ attributes: ['id'] });
        const fids = formations.map(f => f.id);
        await RedisService.set(REDIS_KEY, fids);
        return fids;
      }
      const groupes = await PermissionService.Groupe.getUserGroups(user);
      const formationGroups = await models.FormationGroups.findAll({
        attributes: ['formationId'],
        where: {
          groupeId: groupes.map(g => g.id),
        },
      });
      const formationIds = formationGroups.map(u => u.formationId);
      await RedisService.set(REDIS_KEY, formationIds);
      return formationIds;
    } catch (e) {
      console.error(e);
    }
  },

};

const ExamPermissions = {

  /* By date diffusion groups */
  async getExamSessionIdsAvailableForUser(exam, userId) {
    const user = await models.User.findByPk(userId);
    const myGroups = await PermissionService.Groupe.getUserGroups(user);
    const examSessions = await models.ExamSession.findAll({
      where: {
        examId: exam.id,
      }, raw: 'true',
    });
    const examSessionsIds = examSessions.map(es => es.id);
    // dates diff de l'exam
    const datesDiffs = await models.DateDiffusion.findAll({
      where: {
        examSessionId: examSessionsIds,
      }, raw: 'true',
    });
    const dateDiffGroups = await models.DateDiffusionGroups.findAll({
      where: {
        date_diffusion_id: datesDiffs.map(dd => dd.id),
        groupId: myGroups.map(g => g.id),
      },
    });
    const dateDiffsAllowed = await models.DateDiffusion.findAll({
      where: {
        id: dateDiffGroups.map(ddg => ddg.date_diffusion_id),
      }, raw: true,
    });
    return dateDiffsAllowed.map(dd => dd.examSessionId);
  },

  /* By type */
  async getExamIdsAuthorizedForUser(userId) {
    const user = await models.User.findByPk(userId);
    // Super admin
    if (user.role === ROLES.ADMIN) {
      const exams = await models.Exam.findAll({
        attributes: ['id'],
        raw: true,
      });
      return exams.map(e => e.id);
    }
    const typesQcms = await PermissionService.getAvailableTypeQcmsForUser(user);
    const examTypeQcm = await models.ExamTypeQcm.findAll({
      where: {
        typeQcmId: typesQcms.map(t => t.id),
      },
      raw: true,
    });
    return examTypeQcm?.map(e => e.examId);
  },

  async isUserAuthorizedForExam(userId, examId) {
    const user = await models.User.findByPk(userId);
    if ([ROLES.ADMIN, ROLES.SUB_ADMIN, ROLES.TUTEUR].includes(user.role)) {
      return true;
    }
    const examIds = await PermissionService.Exams.getExamIdsAuthorizedForUser(userId);
    return !!(examIds?.includes(parseInt(examId)));
  },
};

const EventPermissions = {

  /* By type */
  async getEventIdsAuthorizedForUser(userId, adminOverride=true) {
    const REDIS_KEY = `AvailableEventIds-ForUser-${adminOverride}-${userId}`;
    let cachedValue = await RedisService.get(REDIS_KEY);
    if (cachedValue) {
      return cachedValue;
    }
    const user = await models.User.findByPk(userId);
    // Super admin
    if (adminOverride===true && user.role === ROLES.ADMIN) {
      const events = await models.Event.findAll({
        attributes: ['id'],
        raw: true,
      });
      const eventIds = events.map(e => e.id)
      await RedisService.set(REDIS_KEY, eventIds);
      return eventIds;
    }
    const typesQcms = await PermissionService.getAvailableTypeQcmsForUser(user);
    const eventTypeQcm = await models.EventTypeQcm.findAll({
      where: {
        typeQcmId: typesQcms.map(t => t.id),
      },
    });
    // return unique using Set:
    const eventIds = [...new Set(eventTypeQcm?.map(e => e.eventId))];
    await RedisService.set(REDIS_KEY, eventIds);
    return eventIds;
  },

};

export const PermissionService = {

  Groupe: Groupes,

  ...PermissionIdsGetters,

  FormationElement: FormationElement,
  CoursTypeQcmSettings: CoursTypeQcmSettings,

  Exams: ExamPermissions,
  Events: EventPermissions,

  /* Group objects only */
  isUserAmongGroups: (userGroups, comparisonGroups) => {
    const comparisonGroupIds = comparisonGroups.map(g => g.id);
    return userGroups.map(g => g.id).some(r => comparisonGroupIds.includes(r));
  },

  /* TODO virer ça
  async migrateFullUePermissionsToNewSystem() {
    try {
      const ueGroups = await models.UEGroups.findAll();
      //console.log(`${ueGroups.length} ueGroups...`);
      for (const ueGroup of ueGroups) {
        try {
          //const groupe = await models.Groupe.findByPk(ueGroup.groupeId)
          const categoryIds = await CoursService.getCategoriesIdsInUe(ueGroup.ueId);
          const coursIds = await CoursService.getCoursIdsInUe(ueGroup.ueId);
          await PermissionService.addGroupeToUe(ueGroup.ueId, ueGroup.ueId);
          for (const categId of categoryIds) {
            try {
              await PermissionService.addGroupeToUeCategory(
                { groupId: ueGroup.groupeId, ueCategoryId: categId }, {});
            } catch (e) {
              continue;
            }
          }
          for (const courId of coursIds) {
            try {
              await PermissionService.addGroupeToCours({
                groupId: ueGroup.groupeId,
                coursId: courId,
              }, {});
            } catch (e) {
              continue;
            }
          }
        } catch (e) {
          continue;
        }
      }
      console.log('finished');
      return true;
    } catch (e) {
      console.error(e);
    }
  },
  */

  isOneUeInUES: async (mesUEs, ue) => {
    for (const monUE of mesUEs) {
      if (monUE.id === ue.id) { // Si l'UE du cours est dans mes UEs
        return true;
      }
    }
    return false;
  },

  isOneUeInUEId: async (mesUEs, ueId) => {
    for (const monUE of mesUEs) {
      if (monUE.id === ueId) { // Si l'UE du cours est dans mes UEs
        return true;
      }
    }
    return false;
  },

  isUserAuthorizedForCours: async (user, cours) => {
    try {
      if (user?.role === ROLES.ADMIN) {
        return true;
      }
      if (cours?.deleted && user.role === ROLES.USER) {
        return false;
      }
      const REDIS_KEY = `cours-${cours?.id}availableFor-${user.id}`;
      let cachedValue = await RedisService.get(REDIS_KEY);
      if (cachedValue) {
        return cachedValue;
      }
      let mesGroupes = await user.getGroupes(); // get ues accessibles pour mes groupes

      const myGroupsIds = mesGroupes?.map(g => g.id);
      const coursGroups = await models.CoursGroups.findAll({
        where: {
          coursId: cours.id,
        },
        raw: true,
        attributes: ['groupeId'],
      });
      const coursGroupIds = coursGroups?.map(g => g.groupeId);
      let isAuthorized = false;
      if (myGroupsIds.some(g => coursGroupIds.includes(g))) {
        isAuthorized = true;
      }
      await RedisService.set(REDIS_KEY, isAuthorized);
      return isAuthorized;
    } catch (e) {
      console.error(e);
      if (!cours) {
        throw new GraphQLError(`Erreur: le cours n'existe pas`);
      } else {
        throw new GraphQLError(`Erreur lors de la vérification d'autorisation de l'utilisateur pour accéder au cours ${cours.id}`);
      }
    }
  },

  /* Si user a le droit d'éditer la question */
  canEditExerciseByType: async (questionId, me) => {
    try {
      // SUPER ADMIN
      if(me.role === ROLES.ADMIN) {
        return true;
      } else if ([ROLES.SUB_ADMIN, ROLES.TUTEUR].includes(me.role)) {
        const authorizedQuestionIds = await PermissionService.getAvailableQuestionsIdsByTypeQcmForUser(me);
        return authorizedQuestionIds.includes(questionId);
      } else {
        return false;
      }
    } catch (e) {
      console.error(e);
      return false;
    }
  },

  /* Legacy old way */
  isUserAuthorizedForQcm: async (userId, id_lien) => {
    try {
      let user = await models.User.findByPk(userId);
      let mesGroupes = await user.getGroupes(); // get ues accessibles pour mes groupes
      let mesUEs = await UEService.getUEsPourLesGroupes(mesGroupes);
      let qcm = await models.Qcm.findOne({
        attributes: ['UEId'],
        where: {
          id_lien: id_lien,
          //deleted: 0,
        },
      });
      // let UEDuQcm = await CoursService.getUeDuCours(cours) // l'UE du cours
      // Si l'UE du cours est dans les UE autorisées
      return !!(await PermissionService.isOneUeInUEId(mesUEs, qcm.UEId));
    } catch (e) {
      console.error(e);
    }
  },


  createGroupe: async ({ name, role, image, folderId }, me) => {
    try {
      let newGroupe = { name, role, folderId };
      if (image) {
        newGroupe.image = await UploadService.uploadFile(image);
      }
      const groupe = await models.Groupe.create(newGroupe);

      // Pour Admin (non superadmin) on ajoute les permissions par défaut pour qu'il puisse modifier derrière
      const individualGroup = await GroupeService.getIndividualGroupForUser(me?.id);
      await individualGroup.addResponsibleOfGroupe(groupe.id);
      await individualGroup.save();

      return groupe;
    } catch (e) {
      console.error({ exception: e });
      throw new GraphQLError('Error updateGroup: id=' + id);
    }
  },
  updateGroupe: async ({ id, name, role, image, folderId }) => {
    try {
      const groupe = await models.Groupe.findByPk(id);
      if (image) {
        if (image?.shouldDelete) {
          await UploadService.deleteFile(groupe.image);
          image = null;
          groupe.image = null;
        } else {
          groupe.image = await UploadService.uploadFile(image);
        }
      }
      groupe.role = role;
      groupe.name = name;
      groupe.folderId = folderId;
      return await groupe.save();
    } catch (e) {
      console.error({ exception: e });
      throw new GraphQLError('Error updateGroup: id=' + id);
    }
  },
  deleteGroupe: async (id) => {
    try {
      //formation_element_groups
      if(id === null || id === undefined) {
        throw new Error('group id is undefined');
      }
      await models.FormationElementGroups.destroy({
        where: {
          groupeId: id,
        },
      });
      await models.FormationElementGroupUnlock.destroy({
        where: {
          groupeId: id,
        },
      });
      await models.ForumCategoryGroups.destroy({
        where: {
          groupId: id,
        },
      });
      await models.GlobalAnnounceGroups.destroy({
        where: {
          groupeId: id,
        },
      });
      /*
      await models.GroupMathpix.destroy({
        where: {
          groupeId: id,
        },
      });
      */

      const result = await models.Groupe.destroy({ where: { id } });
      return result === 1;
    } catch (e) {
      console.error({ exception: e });
      throw new GraphQLError('Error deleteGroup: id=' + id);
    }
  },

  /* Get group name or username if it's individual group */
  getGroupName: async (groupe) => {
    if (groupe.isIndividual) {
      const user = await models.User.findByPk(groupe?.name);
      if (user) {
        return `(Individuel) ${user?.username}`;
      }
    }
    return groupe?.name;
  },

  /**
   * Add groupe to UE
   *
   * @param ueId
   * @param groupeId
   * @param me
   * @param ip
   * @param req
   * @returns {Promise<*|boolean>}
   */
  addGroupeToUe: async (ueId, groupeId, { me, ip, req }) => {
    try {
      const group = await models.Groupe.findByPk(groupeId);
      let ue = await models.UE.findByPk(ueId);
      if (await models.UEGroups.findOne({ where: { groupeId, ueId } })) {
        // Already added
        return true;
      }
      await LogService.logAction({
        logOperation: LOG_OPERATIONS.Groupe.AddGroupPermission.action,
        logData: {
          text: `${await PermissionService.getGroupName(group)} a accès à matière ${ue?.name}`,
          groupId: groupeId,
          ueId,
        },
        foreignIds: {
          userId: me?.id,
        },
        ip, req, models, userId: me?.id,
      });
      await ue.addGroupe(groupeId);
      return await ue.save();
    } catch (e) {
      console.error(e);
      throw new GraphQLError(`Erreur ajout groupe ${groupeId} de ue ${ueId}`);
    }
  },
  removeGroupeFromUe: async (ueId, groupeId, { me, ip, req }) => {
    try {
      const group = await models.Groupe.findByPk(groupeId);
      let ue = await models.UE.findByPk(ueId);
      if (!await models.UEGroups.findOne({ where: { groupeId, ueId } })) {
        // Already deleted
        return true;
      }
      await LogService.logAction({
        logOperation: LOG_OPERATIONS.Groupe.RemoveGroupPermission.action,
        logData: {
          text: `${await PermissionService.getGroupName(group)} n'a plus accès à matière ${ue?.name}`,
          groupId: groupeId,
          ueId,
        },
        foreignIds: {
          userId: me?.id,
        },
        ip, req, models, userId: me?.id,
      });
      await ue.removeGroupe(groupeId);
      return await ue.save();
    } catch (e) {
      console.error(e);
      throw new GraphQLError(`Erreur suppression groupe ${groupeId} de ue ${ueId}`);
    }
  },

  verifyRecaptcha: async (inputToken) =>{
    // Faire un try / Error
    const googleUrl=recaptchaValidationUrl
    const SECRET_KEY=recaptchaPrivateKey

    const outputPromise = await axios.post(googleUrl, null, {
      params: {
        secret: SECRET_KEY,
        response: inputToken,
      },
    });

    const responseData = outputPromise.data

    return responseData;
  },

  addGroupeToForumCategory: async (forumId, groupeId) => {
    try {
      let forum = await models.ForumCategory.findByPk(forumId);
      await forum.addGroupe(groupeId);
      return await forum.save();
    } catch (e) {
      console.error(e);
      throw new GraphQLError(`Erreur ajout groupe ${groupeId} à forum ${forumId}`);
    }
  },
  removeGroupeFromForumCategory: async (forumId, groupeId) => {
    try {
      let forum = await models.ForumCategory.findByPk(forumId);
      await forum.removeGroupe(groupeId);
      return await forum.save();
    } catch (e) {
      console.error(e);
      throw new GraphQLError(`Erreur ajout groupe ${groupeId} à forum ${forumId}`);
    }
  },


  /**
   * Add user to a group
   * @param {*} userId
   * @param {*} groupeId
   */
  addUserToGroup: async (userId, groupeId, ctx) => {
    try {
      const groupe = await models.Groupe.findByPk(groupeId);
      let user = await models.User.findByPk(userId);
      await user.addGroupe(groupeId);
      await RedisService.delete(`numberOfUserInGroup-${groupeId}`)
      await PermissionService.handleGroupPermissionCacheClear(userId);

      await LogService.logAction({
        logOperation: LOG_OPERATIONS.User.AddGroupToUser.action,
        //ip,
        logData: {
          text: groupe?.name,
        },
        foreignIds: {
          userId,
        },
        models,
        userId: ctx?.me?.id,
      });

      return await user.save();
    } catch (e) {
      console.error(`Erreur ajout groupe ${groupeId} de user ${userId} : ${e}`);
      throw new GraphQLError(`Erreur ajout groupe ${groupeId} de user ${userId}`);
    }
  },
  /*
   * Remove user from a group
   * @param {*} userId
   * @param {*} groupeId
   */
  removeUserFromGroup: async (userId, groupeId, ctx) => {
    try {
      const groupe = await models.Groupe.findByPk(groupeId);

      let user = await models.User.findByPk(userId);
      await user.removeGroupe(groupeId);
      await PermissionService.handleGroupPermissionCacheClear(userId);
      await RedisService.delete(`numberOfUserInGroup-${groupeId}`)
      await LogService.logAction({
        logOperation: LOG_OPERATIONS.User.RemoveGroupFromUser.action,
        //ip,
        logData: {
          text: groupe?.name,
        },
        foreignIds: {
          userId,
        },
        models,
        userId: ctx?.me?.id,
      });

      return await user.save();
    } catch (e) {
      console.error(e);
      throw new GraphQLError(`Erreur suppression groupe ${groupeId} de user ${userId}`);
    }
  },

  handleGroupPermissionCacheClear: async (userId) => {
    await RedisService.delete(`getAvailableUEIdsForUser-${userId}`);
    const ues = await models.UE.findAll({
      attributes: ['id'],
    });
    const ueCategories = await models.UECategory.findAll({
      attributes: ['id'],
    });
    const ueIds = ues.map(u => u.id);
    const ueCategoriesIds = ueCategories.map(u => u.id);
    for (const id of ueIds) {
      await RedisService.delete(`AvailableCategIdsInUE-${id}-forUser-${userId}`);
    }
    for (const id of ueCategoriesIds) {
      await RedisService.delete(`AvailCoursIdsInUECateg-${id}-ForUser-${userId}`);
    }
  },

  addTuteurToUE: async (ueId, userId) => {
    try {
      let ue = await models.UE.findByPk(ueId);
      let tuteur = await models.User.findByPk(userId);
      await ue.addTuteur(tuteur);
      return await ue.save();
    } catch (e) {
      console.error(e);
      throw new GraphQLError(`Erreur ajout tuteur ${userId} à ue ${ueId}`);
    }
  },
  removeTuteurFromUE: async (ueId, userId) => {
    try {
      let ue = await models.UE.findByPk(ueId);
      let tuteur = await models.User.findByPk(userId);
      await ue.removeTuteur(tuteur);
      return await ue.save();
    } catch (e) {
      //console.error(e)
      throw new GraphQLError(`Erreur suppression tuteur ${userId} de ue ${ueId}`);
    }
  },
  updateTuteurFromUe:async(ueIdArray,userId)=>{
    try{
      let tuteur=await models.User.findByPk(userId)

      // test des coursIds
      for (const testUeId of ueIdArray){
        const tempUe=await models.UE.findByPk(testUeId)
        if (parseInt(tempUe.id) !== parseInt(testUeId)){throw new Error(`Dans updateTuteurFromUe l'id ${testUeId} n'est pas reconnu.`)}
      }

      // Update
      await models.UETuteurs.destroy({
       where:{ userId:tuteur.id }
      })

      const newAssociations = ueIdArray.map(ueId => ({
        userId:tuteur.id,
        ueId:ueId
      }))

      await models.UETuteurs.bulkCreate(newAssociations)

      return true

    }catch(e){
      console.error(e)
      throw new GraphQLError(`Erreur update tuteur ${userId} pour les ues suivantes : ${ueIdArray}`)
    }
  },

  async addGroupeToUeCategory({ groupId, ueCategoryId }, { me, ip, req }) {
    try {
      const group = await models.Groupe.findByPk(groupId);
      let uec = await models.UECategory.findByPk(ueCategoryId);
      if (await models.UECategoryGroups.findOne({ where: { groupeId: groupId, uecategory_id: ueCategoryId } })) {
        // Already added
        return true;
      }
      await uec.addGroupe(groupId);
      await uec.save();

      await LogService.logAction({
        logOperation: LOG_OPERATIONS.Groupe.AddGroupPermission.action,
        logData: {
          text: `${await PermissionService.getGroupName(group)} a droit d'accès à ${uec?.name}`,
          groupId,
          ueCategoryId,
        },
        foreignIds: {
          userId: me?.id,
        },
        ip, req, models, userId: me?.id,
      });

      return true;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(`Erreur groupe/categorie: ${e.message}`);
    }
  },
  async removeGroupeFromUeCategory({ groupId, ueCategoryId }, { me, ip, req }) {
    try {
      const group = await models.Groupe.findByPk(groupId);
      if (!await models.UECategoryGroups.findOne({ where: { groupeId: groupId, uecategory_id: ueCategoryId } })) {
        // Already deleted
        return true;
      }
      let uec = await models.UECategory.findByPk(ueCategoryId);
      await uec.removeGroupe(groupId);

      await LogService.logAction({
        logOperation: LOG_OPERATIONS.Groupe.RemoveGroupPermission.action,
        logData: {
          text: `${await PermissionService.getGroupName(group)} n'a plus accès à ${uec?.name}`,
          groupId,
          ueCategoryId,
        },
        foreignIds: {
          userId: me?.id,
        },
        ip, req, models, userId: me?.id,
      });

      return await uec.save();
    } catch (e) {
      //console.error(e)
      throw new GraphQLError(`Erreur: ${e.message}`);
    }
  },

  async addGroupeToCours({ groupId, coursId }, { me, ip, req }) {
    try {
      let cours = await models.Cours.findByPk(coursId);
      const group = await models.Groupe.findByPk(groupId);
      if (await models.CoursGroups.findOne({ where: { groupeId: groupId, coursId } })) {
        // Already added
        return true;
      }
      await LogService.logAction({
        logOperation: LOG_OPERATIONS.Groupe.AddGroupPermission.action,
        logData: {
          text: `${await PermissionService.getGroupName(group)} a accès au cours ${cours?.name}`,
          groupId,
          coursId,
        },
        foreignIds: {
          userId: me?.id,
        },
        ip, req, models, userId: me?.id,
      });

      await cours.addGroupe(groupId);
      return await cours.save();
    } catch (e) {
      //console.error(e)
      throw new GraphQLError(`Erreur: ${e.message}`);
    }
  },
  async removeGroupeFromCours({ groupId, coursId }, { me, ip, req }) {
    try {
      let cours = await models.Cours.findByPk(coursId);
      const group = await models.Groupe.findByPk(groupId);
      if (!await models.CoursGroups.findOne({ where: { groupeId: groupId, coursId } })) {
        // Already deleted
        return true;
      }
      await LogService.logAction({
        logOperation: LOG_OPERATIONS.Groupe.RemoveGroupPermission.action,
        logData: {
          text: `${await PermissionService.getGroupName(group)} n'a plus accès au cours ${cours?.name}`,
          groupId,
          coursId,
        },
        foreignIds: {
          userId: me?.id,
        },
        ip, req, models, userId: me?.id,
      });

      await cours.removeGroupe(groupId);
      return await cours.save();
    } catch (e) {
      //console.error(e)
      throw new GraphQLError(`Erreur: ${e.message}`);
    }
  },

  async addGroupeToTypeQcm({ groupId, typeQcmId }, me) {
    try {
      let typeQcm = await models.TypeQcm.findByPk(typeQcmId);
      await typeQcm.addGroupe(groupId);
      return await typeQcm.save();
    } catch (e) {
      //console.error(e)
      throw new GraphQLError(`Erreur: ${e.message}`);
    }
  },
  async removeGroupeFromTypeQcm({ groupId, typeQcmId }, me) {
    try {
      let typeQcm = await models.TypeQcm.findByPk(typeQcmId);
      await typeQcm.removeGroupe(groupId);
      return await typeQcm.save();
    } catch (e) {
      //console.error(e)
      throw new GraphQLError(`Erreur: ${e.message}`);
    }
  },

  async addGroupeResponsibleForGroup({ responsibleOfGroupId, groupId }, me) {
    try {
      const groupe = await models.Groupe.findByPk(responsibleOfGroupId);
      await groupe.addResponsibleOfGroupe(groupId);
      return await groupe.save();
    } catch (e) {
      console.error(e);
    }
  },
  async removeGroupeResponsibleForGroup({ responsibleOfGroupId, groupId }, me) {
    try {
      const groupe = await models.Groupe.findByPk(responsibleOfGroupId);
      await groupe.removeResponsibleOfGroupe(groupId);
      return await groupe.save();
    } catch (e) {
      console.error(e);
    }
  },

  async groupsForPlanning(me) {
    try {
      return GroupeService.getAllGroupesForUser(me);
    } catch (e) {
      console.error(e);
    }
  },

  async addOrRemoveGroupsToUsersInGroup({ type, groupId, targetGroupsIds }, me) {
    try {
      const userGroups = await models.UserGroups.findAll({
        where: {
          groupeId: groupId,
        },
      });

      const userIds = userGroups.map(ug => ug.userId);
      const uniqueUserIds = [...new Set(userIds)];

      for (const userId of uniqueUserIds) {
        for (const gId of targetGroupsIds) {
          // Check if user is already in group
          const currentUserInCurrentGroup = await models.UserGroups.findOne({
            where: {
              userId,
              groupeId: gId,
            },
          });
          if (type === 'add') {
            if (!currentUserInCurrentGroup) {
              await PermissionService.addUserToGroup(userId, gId);
            }
          } else if (type === 'remove') {
            if (currentUserInCurrentGroup) {
              await PermissionService.removeUserFromGroup(userId, gId);
            }
          }
        }
      }

    } catch (e) {
      console.error(e);
    }
  },

  async addOrRemoveOrReplaceGroupsToGroupsOrUsers({ type, groupsIds,usersIds, targetGroupsIdsString }, ctx) {
    /* Fonction qui permet de add / remplacer / retirer des groups pour un ensemble de ([userIds],[groupsIds])
    *
    * La fonction est un peu compliquée :
    *  1) on va pool tous les usersIds des (groupes + users) dans une liste d'unique Id
    *  2) On va récupérer toutes les associations de ces users, et on va les hasher dans un objet avec la key : `${userId}_${groupId}`
    *  3) Pour chaque user on va :
    *     -> Si type==="replace" : 3.a) Récupérer les associations existantes et itérer dessus pour supprimer les associations qui ne sont pas liés aux groupsTarget
    *
    *     4) itérer sur les targetGroupsIds:
    *       -> Si type==="replace": On ajoute les associations entre (user <-> groupsTarget) Si l'association n'existe pas déjà
    *       -> Si type==="add" : on ajoute les associations entre (user <-> groupsTarget) Si l'association n'exsiste pas déjà
    *       -> Si type==="delete" : on retire les associations entre (user<->groupsTarget) Si l'association existe
    *
    * */
    try {

      /* récupération des users dans les groupsIds renseignés*/
      const userGroups = await models.UserGroups.findAll({
        where: {groupeId: {[Op.in]:groupsIds}},
        attributes:["userId","groupeId"],
        raw:true
      });

      const targetGroupsIds=targetGroupsIdsString.map(value=>parseInt(value,10))

      const allUsersIds = [
        ...new Set([
          ...userGroups.map(node => node.userId),
          ...usersIds.map(node => parseInt(node, 10))
        ])
      ];

      /* Maintenant, on récupère toutes les associations (user <-> groups) existantes pour les users donnés sous une forme d'objet (permet de hash => plus rapide)*/
      const usersGroupsLinksAsObject = await models.UserGroups.findAll({
          where: {userId: {[Op.in]: allUsersIds}}
        }).then(result => result.reduce((obj, node) => {
          obj[`${node.userId}_${node.groupeId}`] = true;
          return obj;
        }, {}));

      /* OK, maintenant on itère sur les users et les target groups pour modifier */
      for (const userId of allUsersIds) {

        /* Si on est en replace, on récupère les associations existantes et on delete les associations qui ne sont pas dans targetGroupsId*/
        if (type==="replace"){

          const singleUserGroupsIds=Object.keys(usersGroupsLinksAsObject).filter(key => key.startsWith(userId + '_')).map(key => parseInt(key.split('_')[1],10));
          for (const previousLinkGroupId of singleUserGroupsIds){
            if (! (targetGroupsIds.includes(previousLinkGroupId)) ){
              await PermissionService.removeUserFromGroup(userId, previousLinkGroupId,ctx);
            }
          }
        }

        /* pour chaque target Groups */
        for (const groupId of targetGroupsIds){

          const key=`${userId}_${groupId}`


          if ((type==="add"||type==="replace") && !(key in usersGroupsLinksAsObject)){  // On add si pas déjà dans l'association
            await PermissionService.addUserToGroup(userId, groupId,ctx);
          } else if (type==="remove" && (key in usersGroupsLinksAsObject)){ // On retire si dans l'association
            await PermissionService.removeUserFromGroup(userId, groupId,ctx);
          } else if (type==="remove" || type==="add" || type==="replace"){ // Permet de filtrer les cas où même si le type est bon, on a rien à faire
          }else{ // Permet d'identifier si le type est mal input
            throw new Error(`type is not recognized :${type}`)
          }
        }
      }
      return true
    } catch (e) {
      console.error(e);
    }
  },
  /**
   * Available answers IDs for user, by linked courses accessible by the user
   * @param user
   * @returns {Promise<*|undefined>} answerIds array
   */
  async getAvailableMcqAnswerIdsForUser(user) {
    // Available questions ids by cours linked
    const coursIds = await PermissionService.getAllCoursIdsAvailableForUser(user);
    // find questions ids linked
    const questionIds = await QuestionsService.getQuestionsIdsFromCoursIds(coursIds);
    return await QCMService.getAnswersIdsFromQuestionIds(questionIds);
  },


  async getAllowedAndForbiddenForumCategoryIdsForUser(user){
    /* Par default, un forumCategory pour lequel les droits ne sont pas set, est accessible à tout le monde.
    Pour avoir accès aux forumCategory authorisé pour un user il faut donc :
      1) Regarder les forumsCategory restricted
      2) Regarder les forumsCategory auquel l'user a accès.
      3) Utilisant 1 et 2 => Identifier les forumCategory où l'accès est restricted pour l'user
      4) Recupérer tous les forumCategory moins les les forumCategory restricted
     */

    // Optimisation REDIS
    const REDIS_KEY_AVAILABLE  =`AvailableForumCategoryIds-ForUser2-${user.id}`;
    const REDIS_KEY_RESTRICTED =`RestrictedForumCategoryIds-ForUser2-${user.id}`;// Définition dynamique d'une key reddis Unique
    let cachedValueAvailable = await RedisService.get(REDIS_KEY_AVAILABLE)
    let cachedValueRestricted = await RedisService.get(REDIS_KEY_RESTRICTED)


    if (cachedValueAvailable && cachedValueRestricted) {return [cachedValueAvailable,cachedValueRestricted]}

    // SUPER ADMIN
    if (user.role === ROLES.ADMIN) {
      // Admins are seeing all elements
      // étape 4 : récupération de tous les forumsId - ceux interdits
      const allowedForumCategory= await models.ForumCategory.findAll({
        attributes:["id"],
        raw:true,
      });
      const allowedForumCategoryIds = allowedForumCategory.map(value => value.id);


      // redis
      await RedisService.set(REDIS_KEY_AVAILABLE,allowedForumCategoryIds);
      await RedisService.set(REDIS_KEY_RESTRICTED,[])

      // return
      return [allowedForumCategoryIds,[]]
    } else {

      // Récupération de l'ID des groupes de l'user
      const groupIds = (await PermissionService.Groupe.getUserGroups(user)).map(g => g.id)

      // étape 1 : Récupération des forumsCategoryId avec des restrictions d'accès :
      const moderatedForumCategory = await models.ForumCategoryGroups.findAll({
        attributes: ['forumCategoryId'],
        raw: true,
        group: ['forumCategoryId']
      })
      const moderatedForumCategoryId = moderatedForumCategory.map(value => value.forumCategoryId)

      // étape 2 : Récupération des forumsCategoryId pour lesquels l'user a accès avec ses groupes actuel :
      const authorizedForumCategory = await models.ForumCategoryGroups.findAll({
        attributes:["forumCategoryId"],
        where :{groupId:{[Op.in] : groupIds}},
        raw: true,
        group: ['forumCategoryId']
      });
      const authorizedForumCategoryId = authorizedForumCategory.map(value => value.forumCategoryId)
          //.then(values => values.map(value => value.forumCategoryId));

      // étape 3 : récupération de la liste des forumCategoryId interdit pour l'user
      const exclusionForumCategoryIds = moderatedForumCategoryId.filter(item => !authorizedForumCategoryId.includes(item))

      // étape 4 : récupération de tous les forumsId - ceux interdits
      const allowedForumCategory= await models.ForumCategory.findAll({
        attributes:["id"],
        where : {id:{[Op.notIn]:exclusionForumCategoryIds}},
        raw:true,
      });
        const allowedForumCategoryIds = allowedForumCategory.map(value => value.id);
        //.then(values => values.map(value => value.id))

      await RedisService.set(REDIS_KEY_AVAILABLE,allowedForumCategoryIds)
      await RedisService.set(REDIS_KEY_RESTRICTED,exclusionForumCategoryIds)

      return [allowedForumCategoryIds,exclusionForumCategoryIds]
    }
  },

  async getAvailableForumIdsForUser(user) {
    /* Rework Martin de getAvailableForumIdsForUser */
    /*
    const [authorizedForumCategoryIds,_] = await PermissionService.getAllowedAndForbiddenForumCategoryIdsForUser(user)
    // maintenant qu'on a les forumsCategoryId interdit pour l'user, on peut filtrer les forum
    const authorizedForum = await models.Forum.findAll({
      attributes: ["id"],
      where: {
        parentId: {
          [Op.in]: authorizedForumCategoryIds
        }
      },
      raw: true
    });
    //.then(value=> value.map(obj => obj.id));
    return authorizedForum.map(value => value.id)
    */
    const groupes = await PermissionService.Groupe.getUserGroups(user);
    const groupIds = groupes.map(g => g.id);
    let forumCategoryGroups = await models.ForumCategoryGroups.findAll({
      where: {
        groupId: groupIds,
      },
      attributes: ['forumCategoryId'],
      raw: true,
    });
    const allowedForumCategoyIds = forumCategoryGroups.map(fcg => fcg.forumCategoryId);
    const forumCategories = await models.ForumCategory.findAll({
      where: {
        id: allowedForumCategoyIds,
      },
        attributes: ['forumId'],
        raw: true,
    });

    return forumCategories.map(fc => fc.forumId);
  },

  /**
   * Get all group ids having access to course
   *
   * @param id
   * @returns {Bluebird<unknown[]>}
   */
  async getGroupIdsHavingAccessForCours(id) {
    const coursGroups = await models.CoursGroups.findAll({
      where: {
        coursId: id,
      },
      raw: true,
    });
    return coursGroups.map(cg => cg.groupeId);
  },

  async getGroupIdsHavingAccessToUECategory(id) {
    const cg = await models.UECategoryGroups.findAll({
      where: {
        uecategory_id: id,
      },
      raw: true,
    });
    return cg.map(cg => cg.groupeId);
  },

  async getGroupIdsHavingAccessToUE(id) {
    const cg = await models.UEGroups.findAll({
      where: {
        ueId: id,
      },
      raw: true,
    });
    return cg.map(cg => cg.groupeId);
  },


  /**
   * Give access recursively to ueCategory and parents
   *
   * @param cours
   * @param ctx context
   * @returns {Promise<void>}
   */
  async giveAccessRecursivelyToCoursAndParents(cours, ctx) {
    try {
      // TODO handle imported course? And check if we need to reset cache

      // Get groups having access to course
      const groupIdsHavingAccessToCours = await PermissionService.getGroupIdsHavingAccessForCours(cours?.id);
      // Handle parent ueCategory
      if (cours?.uecategoryId) {
        const ueCategParent = await models.UECategory.findByPk(cours?.uecategoryId);
        if (ueCategParent) {
          await PermissionService.giveAccessRecursivelyToUECategoryAndParents(ueCategParent, groupIdsHavingAccessToCours, ctx);
        }
      }
      // Handle parent ue
      if (cours?.ueId) {
        const ueParent = await models.UE.findByPk(cours?.ueId);
        if (ueParent) {
          await PermissionService.giveAccessRecursivelyToUEAndParents(ueParent, groupIdsHavingAccessToCours, ctx);
        }
      }
    } catch (e) {
      throw new GraphQLError('Erreur lors de la mise à jour intelligente des permissions du cours');
      console.error(e);
    }
  },


  /**
   *
   * @param ueCateg
   * @param groupIds groups to add access to recursively
   * @param ctx
   * @returns {Promise<void>}
   */
  async giveAccessRecursivelyToUECategoryAndParents(ueCateg, groupIds, ctx) {
    // 1. add access to current ueCateg
    for (const groupId of groupIds) {
      await PermissionService.addGroupeToUeCategory({ groupId, ueCategoryId: ueCateg.id }, ctx);
    }

    // 2. add access to parents recursively
    if (ueCateg?.parentId) {
      // get parent and give access recursively
      const ueCategParent = await models.UECategory.findByPk(ueCateg?.parentId);
      if (ueCategParent) {
        await PermissionService.giveAccessRecursivelyToUECategoryAndParents(ueCategParent, groupIds, ctx);
      }
    }
    if (ueCateg?.ueId) {
      // get parent and give access recursively
      const ue = await models.UE.findByPk(ueCateg?.ueId);
      if (ue) {
        await PermissionService.giveAccessRecursivelyToUEAndParents(ue, groupIds, ctx);
      }
    }
  },

  /**
   *
   * @param ue
   * @param groupIds
   * @param ctx
   * @returns {Promise<void>}
   */
  async giveAccessRecursivelyToUEAndParents(ue, groupIds, ctx) {
    // 1. add access to current ue
    for (const groupId of groupIds) {
      await PermissionService.addGroupeToUe(ue?.id, groupId, ctx);
    }
    // 2. add access to parents recursively
    if (ue?.parentId) {
      // get parent and give access recursively
      const ueParent = await models.UE.findByPk(ue?.parentId);
      if (ueParent) {
        await PermissionService.giveAccessRecursivelyToUEAndParents(ueParent, groupIds, ctx);
      }
    }
  },

  async getChatGptQcmConfigAuthorizedIdsAndStructures(chatGptQcmSettingsId,ctx){
    /* fonction qui retourne les id/structure des groupes/users ayant l'autorisation d'accéder à cette configuration */
    try {
      const models=ctx?.models

      // Verif de si chatGptQcmSettingsId est bien
      const testConfigValue=await models.Config.findByPk(chatGptQcmSettingsId)
      if (!testConfigValue || testConfigValue.get('key')!==CONFIG_KEYS.CHAT_GPT_QCM_SETTINGS){
        throw new Error(`dans getChatGptQcmConfigAuthorizedIdsAndStructures (permission) : la key de CHAT_GPT_QCM_SETTINGS n'existe pas ou alors n'est pas valide. on a : ${testConfigValue?.get('key')} et on devrait avoir : ${CONFIG_KEYS.CHAT_GPT_QCM_SETTINGS}`)
      }

      // récupération des groupesIds et usersIds authorisés
      let authorizedIds=await models.UserGroupChatGpt.findAll({
        where:{chatGptId:chatGptQcmSettingsId},
        attributes:['userId','groupeId']
      })

      // Récup des ids
      const authorizedUserIds=authorizedIds?.map(result => result.userId).filter(userId => userId !== null) || []
      const authorizedGroupesIds=authorizedIds?.map(result => result.groupeId).filter(groupeId => groupeId !== null) || []

      // Récup des users
      const authorizedUsers=await models.User.findAll({where:{id:authorizedUserIds}})
      const authorizedGroupes=await models.Groupe.findAll({where:{id:authorizedGroupesIds}})

      return {authorizedUserIds,authorizedGroupesIds,authorizedUsers,authorizedGroupes}
    }catch(e){
      console.error(e)
    }
  },

  async replaceLinkToOneChatGptForUserIdOrGroupesId({
                                                      ctx,
                                                      args:{
                                                        usersIds,
                                                        groupesIds,
                                                        chatGptId
                                                      }
                                                    }){
    /* fonction qui s'occupe de la gestion des droits entre une config ChatGptQcm et des groupes/users  */

    try{
      //
      const models=ctx?.models

      // Verification de la bonne existance de chatGptId
      const testChatGpt=await models.Config.findByPk(chatGptId)

      // test si c'est le bon type de config
      if (!testChatGpt || testChatGpt.get('key') !== CONFIG_KEYS.CHAT_GPT_QCM_SETTINGS ){
        throw new Error(`dans replaceLinkToOneChatGptForUserIdOrGroupesIds,   le chatGptId est invalide. On devrait tomber sur key === ${CONFIG_KEYS.CHAT_GPT_QCM_SETTINGS} mais on tombe sur ${testChatGpt?.get('key')}`)
      }

      // récupération du dico de config de l'intégrationGPT
      const dico=JSON.parse(testChatGpt.get('value'))
      const gptName=dico["name"]
      const gptId=testChatGpt.get('id')

      // création du bulkcreate
      let bulkCreate=[]


      // On va pour tous les groupes Ids renseignés, on va check si ils existent
      for (const groupeId of groupesIds ){

        const tempGroup=await models.Groupe.findByPk(groupeId)

        if (tempGroup){
          bulkCreate.push({
            chatGptName:gptName,
            groupeId:groupeId,
            chatGptId:gptId,
          })
        }
      }


      // Maintenant pour tous les usersIds renseignés, on va check si ils existent
      for (const tempUserId of usersIds){
        const tempUser=await models.User.findByPk(tempUserId)

        if (tempUser){
          bulkCreate.push({
            chatGptName:gptName,
            userId:tempUser.get('id'),
            chatGptId:chatGptId
          })
        }
      }

      /// Maintenant que l'on a setup chaque groupeId et userId, on va tout delet pour le chatGptId de la table de jointure, et on va tout rajouter
      await models.UserGroupChatGpt.destroy({where:{chatGptId:chatGptId}})

      // bulkCreate
      await models.UserGroupChatGpt.bulkCreate(bulkCreate,{
        updateOnDuplicate: ["chatGptName"], // Champ que l'on souhaite mettre à jour
        conflictTarget: ['userId','groupeId', 'chatGptId'], // Colonnes de de détection des doublons
      })


      return true

    }catch(e){
      console.error(e)
    }
  },


  async getAllGroupsForUser(user,ctx){
    try{
      const models=ctx?.models

      // Récupération des groupes parmis lesquels l'user fait parti
      const userGroupsIds=await PermissionService.Groupe.getUserGroups(user).then(result => result.map(value => value.id));

      // Récupération du groupe individuel de l'user
      const individualGroupsIds=await models.Groupe.findAll({where:{name:user?.id,isIndividual:true},raw:true}).then(result => result.map(value => value.id))

      // return de l'ensemble
      return [...new Set([...userGroupsIds,...individualGroupsIds])]

    }catch(e){
      console.error(e)
    }
  },

  async adminGetGroupsAuthorizedForMathpixIntegration({mathpixIntegrationId,ctx}){
    try{

      const models=ctx?.models
      const user=ctx?.me

      // verif que admin
      if (user.role !== ROLES.ADMIN) {
        throw new Error(`rôle ${user.role} non authorisé dans la fonction admin`)
      }

      const allGroupsId=await models.GroupMathpix.findAll({where:{mathpixIntegrationId:mathpixIntegrationId},attributes:['groupeId']}).then(nodes => nodes.map(value =>value.groupeId));

      const allGroups = await models.Groupe.findAll({where:{id:allGroupsId}})

      return allGroups

    }catch(e){
      console.error(e)
      throw new GraphQLError(e)
    }
  },

  // Update group permissions
  async updateGroupPermissions({ groupId, ueIds=[], ueCategoryIds=[], coursIds=[] }, ctx) {
    try {
      const groupe = await models.Groupe.findByPk(groupId);
      if(!groupe) {
        throw new Error(`Groupe avec l'ID ${groupId} introuvable`);
      }

      // Remove existing permissions
      await Promise.all([
        models.UEGroups.destroy({ where: { groupeId: groupId } }),
        models.UECategoryGroups.destroy({ where: { groupeId: groupId } }),
        models.CoursGroups.destroy({ where: { groupeId: groupId } })
      ]);

      // Add new permissions (bulk create for efficiency)
      const ueGroupPermissions = ueIds.map(ueId => ({ groupeId: groupId, ueId }));
      const ueCategoryGroupPermissions = ueCategoryIds.map(ueCategoryId => ({ groupeId: groupId, uecategory_id: ueCategoryId }));
      const coursGroupPermissions = coursIds.map(coursId => ({ groupeId: groupId, coursId }));

      await Promise.all([
        models.UEGroups.bulkCreate(ueGroupPermissions ),
        models.UECategoryGroups.bulkCreate(ueCategoryGroupPermissions ),
        models.CoursGroups.bulkCreate(coursGroupPermissions),
      ]);

      // Handle cache clearing
      await Promise.all([
        RedisService.deleteByPrefix(`getAvailableUEIdsForUser-`),
        RedisService.deleteByPrefix(`AvailableCategoryIdInCategoryId-`),
        RedisService.deleteByPrefix(`AvailableCategIdsInUE-`),
        RedisService.deleteByPrefix(`AvailableCategoryIdsForUser-`),
        RedisService.deleteByPrefix(`AvailCoursIdsInUECateg-`),
        RedisService.deleteByPrefix(`AvailableCoursIdsInUEForUser-`),
        RedisService.deleteByPrefix(`AvailCoursIdsRecursiveInUECateg-`),
        RedisService.deleteByPrefix(`AvailableCoursIds-ForUser-`),
      ]);
      return true;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(`Erreur lors de la mise à jour des permissions du groupe ${groupId}`);
    }
  },
};
