import { <PERSON>raph<PERSON><PERSON>rror } from 'graphql';
import { Sequelize } from 'sequelize'
import models from '../../../models/index.js';
import { LOG_OPERATIONS } from '../../../models/log/log'
import { McqScaleQuestionType } from '../../../models/qcm/mcq_scale'
import { CoursService } from '../../cours/cours-service.js'
import { QuestionAnswerType } from '../qcm-service.js'

export const FLASHCARD_RESPONSE_TYPE={BINARY:"BINARY",CONTINUOUS:"CONTINUOUS"}
import LogService from '../../log/log-service'
import { QCMService } from '../qcm-service.js'
import { QuestionsService } from '../questions/questions-service'

export const SHOW_SCALE_LOG=false

const DICO_DEFAULT_FLASHCARD_SETTINGS={
  VERY_GOOD:{label:'Flashcard.BaremLabelVeryGood',enabled:true,note:1,backupId:1,type:FLASHCARD_RESPONSE_TYPE.CONTINUOUS},
  GOOD:{label:'Flashcard.BaremLabelGood',enabled:true,note:0.8,backupId:2,type:FLASHCARD_RESPONSE_TYPE.CONTINUOUS},
  AVERAGE:{label:'Flashcard.BaremLabelAverage',enabled:true,note:0.5,backupId:3,type:FLASHCARD_RESPONSE_TYPE.CONTINUOUS},
  POOR:{label:'Flashcard.BaremLabelPoor',enabled:true,note:0.2,backupId:4,type:FLASHCARD_RESPONSE_TYPE.CONTINUOUS},
  VERY_POOR:{label:'Flashcard.BaremLabelVeryPoor',enabled:true,note:0,backupId:5,type:FLASHCARD_RESPONSE_TYPE.CONTINUOUS},
  TRUE:{label:'Flashcard.BaremLabelTrue',enabled:true,note:1,backupId:6,type:FLASHCARD_RESPONSE_TYPE.BINARY},
  FALSE:{label:'Flashcard.BaremLabelFalse',enabled:true,note:0,backupId:7,type:FLASHCARD_RESPONSE_TYPE.BINARY},
  DEFAULT:{label:'Flashcard.BaremLabelDefault',enabled:false,note:0,backupId:8,type:FLASHCARD_RESPONSE_TYPE.CONTINUOUS}
}


export const McqScaleService = {

  // CREATE SCALE CHECK
  createMcqScale: async ({ mcqScale }, userId) => {
    try {
      mcqScale.rules = JSON.stringify(mcqScale.rules);
      mcqScale.authorId=userId
      const mcqScaleCreated = await models.McqScale.create(mcqScale);
      const id = mcqScaleCreated?.id;
      const ueIds = mcqScale?.ueIds || [];


      // Log de création de scale
      await LogService.logAction({
        logOperation:LOG_OPERATIONS.Scale.Create.action,
        logData:{
          scaleId:id,
        },
        foreignIds:{
          scaleId:id,
          userId,
        },
        userId,
        models,
      })


      for (const ueId of ueIds) {
        await McqScaleService.addUeToMcqScale({ ueId, mcqScaleId: id }, userId);
      }

      return mcqScaleCreated;
    } catch (e) {
      console.error({ e });
      throw new GraphQLError(e.message);
    }
  },

  updateMcqScale: async ({ id, mcqScale }, userId) => {
    try {
      mcqScale.rules = JSON.stringify(mcqScale.rules);

      // get de l'old scale pour pouvoir log
      const oldScale=await models.McqScale.findByPk(id);

      let updated = await models.McqScale.update(
        mcqScale, {
          where: { id: id },
        },
      );

      await LogService.logAction({
        logOperation:LOG_OPERATIONS.Scale.Update.action,
        logData:{
          oldScale,
          newScale:updated
        },
        foreignIds:{
          scaleId:id,
          userId,
        },
        userId,
        models
      })

      const ueIds = mcqScale?.ueIds || [];
      try {
        await models.McqScaleUes.destroy({
          where: { mcqScaleId: id },
        });

        await LogService.logAction({
          logOperation:LOG_OPERATIONS.Scale.RemoveAllUeFromScale.action,
          logData:{
            scaleId:id,
          },
          foreignIds:{
            scaleId:id,
            userId,
          },
          userId,
          models
        })
      } catch (e) {
        console.warn(e);
      }
      for (const ueId of ueIds) {
        await McqScaleService.addUeToMcqScale({ ueId, mcqScaleId: id }, userId);
      }

      return updated[0]; // oui
    } catch (e) {
      console.error({ e });
      throw new GraphQLError(e.message);
    }
  },

  deleteMcqScale: async ({ id }, userId) => {
    try {
      let mcqScale = await models.McqScale.findByPk(id);

      if (mcqScale.isDefault) {
        throw new GraphQLError(`Vous ne pouvez pas supprimer la notation par défaut, sélectionner une autre notation par défaut avant`);
      }
      const questions = await models.Question.findAll({
        where: {
          mcqScaleId: id,
        },
      });
      if (questions && questions.length > 0) {
        throw new GraphQLError(`${questions.length} utilisent cette notation ! Elle ne peut être supprimée`);
      } else {
        const resultDeletion= await models.McqScale.destroy({ where: { id } });

        await LogService.logAction({
          logOperation:LOG_OPERATIONS.Scale.Delete.action,
          logData:{
            deletedScale:mcqScale,
            deletedId:mcqScale?.id
          },
          foreignIds:{
            userId,
          },
          userId,
          models
        })

        return resultDeletion
      }
    } catch (e) {
      console.error({ e });
      throw new GraphQLError(e.message);
    }
  },


  async getMcqScaleForQuestionTypeAndUe({questionType,ueIds=[]}){
    // Fonction qui en fonction du questionType et des possible UeIds va retourner la bonne scale
    try{

      // Check de si le questionType est bon
      if (!Object.values(McqScaleQuestionType).includes(questionType)){
        throw new Error(`questionType (${questionType}) not in valid questionType`)
      }

      // Si il y a des ueIds renseignées
      if (ueIds?.length>0){

        ///// Si une ou des ueIds sont renseignées, fetch des scales
        const scaleLinkedToUes=await models.McqScale.findAll({
          where:{
            questionType,
          },
          include:[
            {
              model:models.UE,
              where:{
                id:ueIds
              },
              required:true
            }
          ]
        })

        // Si il y a aucunes scales linked aux Ue, alors on warn et on retourne rien
        if (scaleLinkedToUes.length===0){
          console.warn("getMcqScaleForQuestionTypeAndUe : no scale compatible. Switch to default scale")

          // Si il y a plusieures scales linked aux Ue, alors on warn et on retourne rien
        }else if (scaleLinkedToUes.length>1){
          console.warn(`getMcqScaleForQuestionTypeAndUe : ${scaleLinkedToUes.length} compatible for questionType (${questionType}) and ueIds : ${ueIds}. The first one is choosed `)

          // Si qu'une seule scale linked to UE, alors on return la seule linked
        } else if (scaleLinkedToUes.length===1){
          return scaleLinkedToUes[0]
        }
      }

      // Si y a pas eu de match avec le UE id, ou que pas d'UE ID ont étées reportées, alors on get la défault pour le question Type
      const scale=await models.McqScale.findOne({where:{questionType,isDefault:true}})

      if (!scale){
        throw new Error(`getMcqScalesForQuestionTypeAndUe - when no UE defined, no scale for questionType and isDefault===true`)
      }
      return scale

    }catch(e){
      console.error(e)
      throw new GraphQLError(e)
    }
  },

  async mcqScalesQcmAndQcu(ctx){
    try {
      const {models,me}=ctx
      return models.McqScale.findAll({where:{questionType:[McqScaleQuestionType.MultipleChoice,McqScaleQuestionType.UniqueChoice]}})
    }catch(e){
      console.error(e)
      throw new GraphQLError(e)
    }
  },
  /*
  * questionType: mcq or ucq
  * */
  async getDefaultMcqScaleForUeId(ueId, questionType = null) {
    let mcqScale;
    if (questionType !== null) {
      const result = await models.McqScaleUes.findAll({
        where: {
          ueId,
        },
      });
      const mcqScales = await models.McqScale.findAll({
        where: {
          id: result?.map(r => r?.mcqScaleId),
        },
      });
      mcqScale = mcqScales?.find(m => m?.questionType === questionType);
    } else {
      const result = await models.McqScaleUes.findOne({
        where: {
          ueId,
        },
      });
      mcqScale = await models.McqScale.findByPk(result?.mcqScaleId);
    }
    return mcqScale;
  },

  async mcqScaleDescription({ctx}){
    /* Fonction qui retourne un descriptif de la table scale */
    try {

      const models = ctx?.models

      // Récupération des données de la BDD
      const results = await models.McqScale.findAll({
        attributes: [
          'questionType',
          [Sequelize.fn('COUNT', Sequelize.col('id')), 'count'],
          [Sequelize.fn('MAX', Sequelize.literal('CASE WHEN isDefault = true THEN name ELSE NULL END')), 'defaultName'],
        ],
        group: ['questionType'],
        raw: true,
      });

      // Merge avec les types disponible de ceux qui ne sont pas présent en DB
      const notInDatabaseData=[]
      const arrayOfKeyInResult= results.map((node)=>{return node.questionType});
      Object.values(McqScaleQuestionType).forEach((value)=>{
        if (!arrayOfKeyInResult.includes(value)){
          notInDatabaseData.push({
            questionType:value,
            count:0,
            defaultName:null,
          })
        }
      })

      const newResults=[...results,...notInDatabaseData]

      return newResults
    }catch(e){
      console.error(e)
      throw new GraphQLError(e)
    }
  },

  async getQuestionTypeMcqScales({args,ctx}){
    try {

      const models = ctx?.models
      const questionType=args?.questionType

      // Récupération des données de la BDD
      const results = await models.McqScale.findAll({
        where : {questionType}
      });

      return results

    }catch(e){
      console.error(e)
      throw new GraphQLError(e)
    }

  },


  async addUeToMcqScale({ mcqScaleId, ueId }, id) {
    try {
      const mcqScale = await models.McqScale.findByPk(mcqScaleId);

      const ue = await models.UE.findByPk(ueId);
      const userId=id
      const result = await models.McqScaleUes.findOne({
        where: {
          ueId,
        },
      });
      if (result) {
        const alreadyAssociatedScale = await models.McqScale.findByPk(result?.mcqScaleId);
        if (mcqScale?.questionType === alreadyAssociatedScale?.questionType) {
          throw new GraphQLError(`La matière ${ue?.name} est déjà associée au barême ${alreadyAssociatedScale?.name}`);
        }
      }
      if (ue && mcqScale) {
        await models.McqScaleUes.create({
          ueId,
          mcqScaleId,
        });
      }

      await LogService.logAction({
        logOperation:LOG_OPERATIONS.Scale.LinkUeToScale.action,
        logData:{
          scaleId:mcqScaleId,
          ueId,
        },
        foreignIds:{
          scaleId:mcqScaleId,
          userId,
        },
        userId,
        models,
      })

      return true;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },

  async removeUeFromMcqScale({ mcqScaleId, ueId }, id) {
    try {
      const mcqScale = await models.McqScale.findByPk(mcqScaleId);
      const ue = await models.UE.findByPk(ueId);
      const userId=id

      if (ue && mcqScale) {
        await models.McqScaleUes.destroy({
          where: {
            ueId,
            mcqScaleId,
          },
        });
      }

      await LogService.logAction({
        logOperation:LOG_OPERATIONS.Scale.RemoveUeFromScale.action,
        logData:{
          scaleId:mcqScaleId,
          ueId,
        },
        foreignIds:{
          scaleId:mcqScaleId,
          userId,
        },
        userId,
        models
      })
      return true;
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },


  async massChangesScaleForQuestions({args,ctx,mutation}){
    try{

      // Récupération des arguments
      const massChangesScaleForQuestionsInput=mutation ? args?.mutationMassChangesScaleForQuestionsInput : args?.queryMassChangesScaleForQuestionsInput

      const selectedCoursIds= massChangesScaleForQuestionsInput?.selectedCoursIds ?? []
      const selectedQuestionsIds= massChangesScaleForQuestionsInput?.selectedQuestionIds ?? []
      const dicoMapping=massChangesScaleForQuestionsInput?.mappingScale

      const models=ctx?.models
      const userId=ctx?.me?.id

      //////////// Vérification du mapping

      // non vide
      if (dicoMapping?.length<=0){throw new Error(`In massChangeScale, dicoMapping length is <= 0 :${dicoMapping}`)}


      // vérif de l'unicité des keys
      const isUnique = new Set(dicoMapping.map(item => item.questionType)).size === dicoMapping.length;
      if (!isUnique){throw new Error(`scaleQType key not unique in dicoMapping :${JSON.stringify(dicoMapping)}`)}

      // Transformation de [{mcqScaleType,newId},{}...] en {mcqScaleType:newId}
      const reformedMapping = dicoMapping.reduce((acc, { questionType, scaleId }) => ({ ...acc, [questionType]: parseInt(scaleId) }), {});

      // Check des bonnes key de mapping
      const allKeysProjected = Object.keys(reformedMapping).every(key => Object.values(McqScaleQuestionType).includes(key));
      if (!allKeysProjected){throw new Error(`In massChangeScale, dicoMapping Key not fully projected in McqScaleQType. dicoMapping Keys : ${Object.keys(reformedMapping)}`)}


      // Vérification du mapping key,value
      let errorMappingArray=[]
      for (const [key,value] of Object.entries(reformedMapping)){
        const scaleIdForScaleType = (await models.McqScale.findAll({ where: { questionType: key }, attributes: ["id"] })).map(node => node.id)
        if (!scaleIdForScaleType.includes(value)){
          errorMappingArray.push({McqScale:key,validId:scaleIdForScaleType,wanted:value})
        }
      }

      if (errorMappingArray.length>0){
        throw new Error(`In massChangeScale, scaleId from dicoMapping not valid : ${JSON.stringify(errorMappingArray)}`)
      }

      // si cours de présent, récupération des questions id Associées
      let coursQuestionIds=[]
      if (selectedCoursIds){
        coursQuestionIds = await QuestionsService.getQuestionsIdsFromCoursIds(selectedCoursIds);
      }


      // Merge des questionsIds input + des questions Id des cours Input
      const allQuestionIds = [...new Set([...coursQuestionIds, ...(selectedQuestionsIds.map(id=>parseInt(id)))])];

      // Get des questions depuis la db
      const allQuestions = await models.Question.findAll({
        where: {
          id_question: allQuestionIds,
        }
      });

      ///////////// Modif

      // Update des scales pour les éléments du mapping
      let promiseArray=[]
      let count=0
      for (const q of allQuestions){
        // Pour chaque question, on calcule son mcqScaleType
        const qScaleType = await McqScaleService.determineScaleQuestionTypeFromQuestion({question:q})

        // Check de si le qtype correspond à un élément à modifier
        if (qScaleType in reformedMapping){
          const newScaleId=reformedMapping[qScaleType]
          const oldScaleId=q.mcqScaleId

          // Si on update pour le même scale, alors on exit avant de modifier la question
          if (newScaleId===oldScaleId){
            continue;
          }

          // SI MUTATION === TRUE => push des promises dans l'array
          if (mutation){
            // push du promise dans l'array
            promiseArray.push(
              LogService.logAction({
                logOperation: LOG_OPERATIONS.Question.Update.action,
                logData: { oldScaleId,newScaleId },
                foreignIds: { questionId: q.id_question, userId , scaleId:newScaleId},
                models, userId,
              })
            )

            // Modif et push du promise
            q.mcqScaleId=newScaleId
            promiseArray.push(q.save())
          }

          // numérotation du nombre de modif
          count+=1
        }
      }

      await Promise.all(promiseArray)

      return count

    }catch(e){
      console.error(e)
      throw new GraphQLError(e)
    }
  },


  async getUesForMcqScaleId(id) {
    const mcqScaleUes = await models.McqScaleUes.findAll({
      where: {
        mcqScaleId: id,
      },
    });
    const ueIds = mcqScaleUes?.map(s => s.ueId);
    return models.UE.findAll({
      where: {
        id: ueIds,
      },
    });
  },


  async getNumberOfLinkedExercises(id,ctx){
    try {
      const models = ctx?.models
      const mcqLinkedExercises=await models.Question.count({where:{mcqScaleId:id}})
      return mcqLinkedExercises

    }catch(e){
      console.error(e)
      throw new GraphQLError(e)
    }
  },

  async wrapperFindScaleFromQuestion({question}){
    // Fonction qui va identifier la scale à associer à la question en fonction de son appartenance à un QCM (link UE-ID), type d'exercice.
    try {
      // identification de si il y a un qcm => permet d'avoir la liaison qcm <->UE
      const qcm = await QCMService.getFirstQcmIdHavingQuestion(question?.id_question);

      // Identification du scaleQuestionType de l'exercice
      const scaleQuestionType=await QCMService.subProcessDetermineScaleQuestionType({question})

      // Determination de la scale en fonction de scaleQType et des UE Linked
      const mcqScale=await McqScaleService.getMcqScaleForQuestionTypeAndUe({questionType:scaleQuestionType,ueIds:qcm?.UEId ? [qcm?.UEId] : []})

      return mcqScale
    }catch(e){
      if (SHOW_SCALE_LOG){console.error(`wrapperFindScale -> Error : ${e}`)}
    }
  },


  async wrapperGiveScaleToQuestionFromId({questionId,ctx}){
    /* fonction qui wrap l'attribution de la bonne scale à une question*/
    try {


      const model=ctx?.models
      const q= await model.Question.findByPk(questionId)

      const goodScale=await this.wrapperFindScaleFromQuestion({question:q})


      q.mcqScaleId=goodScale?.id

      await q.save()

      return true

    }catch(e){
      console.error(e)
      throw new GraphQLError(`in wrapperGiveScaleToQuestionFromId, error :`,e)
    }
  },

  async modifyDefaultMcqScaleForQuestionType({args,ctx}){
    // Fonction qui modifie le paramètre 'par default' d'une scale par question type
    try {
      const userId=ctx?.me?.id
      const models=ctx?.models
      const questionType=args?.questionType
      const scaleId=args?.mcqScaleId

      // Check de si le questionType est bon
      if (!Object.values(McqScaleQuestionType).includes(questionType)){
        throw new Error(`questionType (${questionType}) not in valid questionType`)
      }

      // Get des deux scales
      const newScale=await models.McqScale.findByPk(scaleId)
      const oldScale=await models.McqScale.findOne({where:{questionType,isDefault:true}})


      // Check des conditions pour faire ceci :
      // Si newScale et oldScale sont déjà par default, on exit
      if (newScale?.id === oldScale?.id){return true}

      // Remove de l'old par default de la scale si elle existes
      if (oldScale){
        oldScale.isDefault=false
        await oldScale.save()

        // Log
        await LogService.logAction({
          logOperation:LOG_OPERATIONS.Scale.RemoveIsDefaultFromScale.action,
          logData:{
            scaleId:oldScale?.id,
            questionType,
          },
          foreignIds:{
            scaleId:oldScale?.id,
            userId,
          },
          userId,
          models
        })
      }

      // rajout de l'argument isDefault à la nouvelle scale
      if (newScale){

        newScale.isDefault=true
        await newScale.save()

        // Log
        await LogService.logAction({
          logOperation:LOG_OPERATIONS.Scale.AddIsDefaultToScale.action,
          logData:{
            scaleId:newScale?.id,
            questionType,
          },
          foreignIds:{
            scaleId:newScale?.id,
            userId,
          },
          userId,
          models
        })
      }

      return true

    }catch(e){
      console.error(e)
      throw new GraphQLError(e)
    }
  },

  async checkIfAllQuestionTypeExists({transaction}){
    // Fonction de migration qui permet d'uniformiser que tous les types d'exercice existent et qu'il y ai au moins un default barème par type
    // Fonction qui check si pour chaque type d'exercice, il y a bien un bareme (l'argument isDefault===true) sera fixé en second temps

    let scaleToUpdate

    // Il faut faire attention d'un point de vue futurproof car on fait des actions sur des données à un moment T,
    for (const [key, value] of Object.entries(McqScaleQuestionType)) {

      scaleToUpdate=null

      /// Récupération des scales
      const scales=await models.McqScale.findAll({where:{
          questionType:value,
        },
        transaction
      })

      //////////// Si y a des scales
      /// Si il y a des scales, on regarde si il y en a au moins une à default===true
      if (scales.length > 0){
        const hasTrueKey = scales.some(obj => obj.isDefault === true);

        // Si oui, alors on exit la vérification de ce type d'exercice
        if (hasTrueKey){
          continue;
        }

        // Si y a pas de scale par default, alors on regarde si il y qu'une scale, si c'est le cas,on la met automatiquement par default
        if (scales.length===1){
          scales[0].isDefault=true;
          await scales[0].save({transaction})
          continue;
        }

        // Si non, alors on va regarder la plus utilisée, et on va la mettre à par default===true
        let scaleToDefault = null;
        let maxUsageCount = -1;

        // Check pour chaque scale du type défini, laquelle est la plus utilisée
        for (const scale of scales) {
          const usageCount = await models.Question.count({
            where: { mcqScaleId: scale.id },
            transaction
          });

          if (usageCount > maxUsageCount) {
            maxUsageCount = usageCount;
            scaleToDefault = scale;
          }
        }

        scaleToDefault.isDefault=true
        await scaleToDefault.save({transaction})
        continue;
      }

      ///// Si y a pas de scales associées, alors on créé et update à isDefault === 1
      const newScale=await this.createBaseScale(value,{transaction})
      newScale.isDefault=true
      await newScale.save({transaction})
    }
  },


  async scaleReworkMigration({transaction}){
    // Fonction qui va vérifier qu'il y a bien une scale par type d'exercice connu
    // Si non la créé
    // Et vérifie qu'il y a bien une scale par default par type d'exercice
    // Si non, attribue celle avec le plus d'exo linked au par default
    try {
      await this.checkIfAllQuestionTypeExists({transaction})
    }catch(e){
      console.error(e)
      throw new GraphQLError(e)
    }
  },


  async createBaseScale(questionType,{transaction}){
    try{

      if (!Object.values(McqScaleQuestionType).includes(questionType)){
        throw new Error(`questionType (${questionType}) not in valid questionType`)
      }

      const rules={
        minimumGrade:0,
        "numberOfErrors":null,
        "pointsPerQuestion":1,
        "notation":"default",
        "pointsLostPerError":[-0.5,-0.3,-0.2,0,0],
        "pointsLostPerErrorIfTrueAnswerIsCheckedFalse":[-0.5,-0.3,-0.2,0,0],
        "pointsLostPerErrorIfTrueAnswerIsCheckedFalseSettings":"fixed",
        "pointsLostPerErrorIfFalseAnswerIsCheckedTrue":[-0.5,-0.3,-0.2,0,0],
        "pointsLostPerErrorIfFalseAnswerIsCheckedTrueSettings":"fixed",
        "pointsLostPerErrorIfFalseAnswerIsUndefined":[-0.5,-0.3,-0.2,0,0],
        "pointsLostPerErrorIfFalseAnswerIsUndefinedSettings":"fixed",
        "pointsLostPerErrorIfTrueAnswerIsUndefined":[-0.5,-0.3,-0.2,0,0],
        "pointsLostPerErrorIfTrueAnswerIsUndefinedSettings":"fixed",
        "flashcardBareme":DICO_DEFAULT_FLASHCARD_SETTINGS
      }


      const entry={
        type:"manual",
        rules:JSON.stringify(rules),
        authorId:null, // Créé par le systeme, donc pas d'authorId
        questionType:questionType,
        name:"",
        pointsObtainedWhenNothingChecked: 0
      }


      switch (questionType) {
        case McqScaleQuestionType.UniqueChoice:
          entry.name="Barème Défaut QCU"
          break;

        case McqScaleQuestionType.MultipleChoice:
          entry.name="Barème Défaut QCM"
          break;

        case McqScaleQuestionType.FLASHCARD:
          entry.name="Barème Défaut Flashcard"
          break;

        case McqScaleQuestionType.Schema:
          entry.name="Barème Défaut Schema Point & Click"
          entry.type="dynamic"
          break;

        case McqScaleQuestionType.FreeText:
          entry.name="Barème Défaut Texte libre"
          entry.type="dynamic"
          break;

        case McqScaleQuestionType.FillInTheBlanks:
          entry.name="Barème Défaut Texte à trou"
          entry.type="dynamic"
          break;

        case McqScaleQuestionType.SchemaFillInLegends:
          entry.name="Barème Défaut Schéma à compléter"
          entry.type="dynamic"
          break;

        case McqScaleQuestionType.Alphanumerical:
          entry.name="Barème Défaut Alpha-numérique"
          entry.type="dynamic"
          break;

        default:
          throw new Error(`In Create Base Scale, questionType (${questionType}) is not supported.`)
      }


      return await models.McqScale.create(entry,{transaction})

    }catch(e){
      console.error(e)
      throw new GraphQLError(e)
    }
  },

  async createBaseFlashcardScale(){
    try {

      const rules={
        minimumGrade:0,
        "numberOfErrors":null,
        "pointsPerQuestion":1,
        "notation":"default",
        "pointsLostPerError":[-0.5,-0.3,-0.2,0,0],
        "pointsLostPerErrorIfTrueAnswerIsCheckedFalse":[-0.5,-0.3,-0.2,0,0],
        "pointsLostPerErrorIfTrueAnswerIsCheckedFalseSettings":"fixed",
        "pointsLostPerErrorIfFalseAnswerIsCheckedTrue":[-0.5,-0.3,-0.2,0,0],
        "pointsLostPerErrorIfFalseAnswerIsCheckedTrueSettings":"fixed",
        "pointsLostPerErrorIfFalseAnswerIsUndefined":[-0.5,-0.3,-0.2,0,0],
        "pointsLostPerErrorIfFalseAnswerIsUndefinedSettings":"fixed",
        "pointsLostPerErrorIfTrueAnswerIsUndefined":[-0.5,-0.3,-0.2,0,0],
        "pointsLostPerErrorIfTrueAnswerIsUndefinedSettings":"fixed",
        "flashcardBareme":DICO_DEFAULT_FLASHCARD_SETTINGS
      }

      const entry={
        name:"Flashcard",
        type:"manual",
        questionType:QuestionAnswerType.FLASHCARD,
        rules:JSON.stringify(rules)
      }

      await models.McqScale.create(entry)
    }catch(e){
      console.error(e)
      throw new GraphQLError(e)
    }
  },

  async getAllScalesAvailableForQuestion({args,ctx}){
    // Fonction qui va query la questionId, et retourner les scales qui matchent la question
    try {
      const id=args?.questionId
      const models=ctx?.models

      // Recup de la question
      const q=await models.Question.findByPk(id);

      // get du scaleQuestionType
      const scaleQuestionType=await this.determineScaleQuestionTypeFromQuestion({question:q})

      // Get des scales dispo pour cette question
      const allQuestionTypesScales=await models.McqScale.findAll({where:{questionType:scaleQuestionType}})

      return allQuestionTypesScales

    }catch(e){
      console.error(e)
      throw new GraphQLError(e)
    }
  },


  async determineScaleQuestionTypeFromQuestion({question}){
    // Fonction qui mappe une question (champ : {Type, isAnswerFreeText, isCheckbox} ) et qui détermine son scaleQuestionType
    try{
      let scaleQuestionType=null

      // Détermination du scale depuis le type de question si celui ci n'est pas null
      if (question?.type !== null){

        if (question.type===QuestionAnswerType.FLASHCARD){
          scaleQuestionType=McqScaleQuestionType.FLASHCARD;
        } else if (question?.type === QuestionAnswerType.SCHEMA_POINT_AND_CLICK) {
          scaleQuestionType = McqScaleQuestionType.Schema;
        } else if (question?.type === QuestionAnswerType.SCHEMA_FILL_IN_LEGENDS){
          scaleQuestionType = McqScaleQuestionType.SchemaFillInLegends;
        } else if (question?.type === QuestionAnswerType.FILL_IN_THE_BLANKS) {
          scaleQuestionType = McqScaleQuestionType.FillInTheBlanks;
        } else if  (question?.type === QuestionAnswerType.ALPHANUMERICAL_OR_NUMERICAL) {
          scaleQuestionType = McqScaleQuestionType.Alphanumerical;
        } else if  (question?.type === QuestionAnswerType.ALPHANUMERICAL) {
          scaleQuestionType = McqScaleQuestionType.Alphanumerical;
        } else if (question?.type===QuestionAnswerType.NUMERICAL){
          scaleQuestionType = McqScaleQuestionType.Alphanumerical
        } else {
          if (SHOW_SCALE_LOG){
            console.error(`in determine scale qType, qType is not null but still unrecognized : ${question?.type}. We ignore for now and return null`)
          }
        }

        // Je sais qu'on peut simplifier, mais je préfère une syntaxe explicite
      } else if (question?.type===null){
        if (question?.isAnswerFreeText){
          scaleQuestionType=McqScaleQuestionType.FreeText
        } else if (question.isCheckbox){
          scaleQuestionType=McqScaleQuestionType.MultipleChoice
        } else if (!question.isCheckbox){
          scaleQuestionType=McqScaleQuestionType.UniqueChoice
        }else{
          if (SHOW_SCALE_LOG){
            console.error(`in determine scale qType, qType is null, and infered scaleQType is not valid. We ignore for now and return null`)
          }
        }
      }

      return scaleQuestionType

    }catch(e){
      console.error(e)
      throw new GraphQLError(e)
    }
  },

  async getDefaultScaleForExerciseTypeAndCourseId({args,ctx}){
    try {

      // Extration des arguments et modèle
      const models=ctx?.models
      const mcqScaleQuestionType=args?.mcqScaleQuestionType
      const coursIds=args?.coursIds

      // Mise en promise de la récupération de chaque UeId depuis l'array de cours
      const promiseArray = coursIds.map(async (courId) => {
        const cour = await models?.Cours.findByPk(courId);
        return CoursService?.getParentUEForCours(cour);
      });

      // Récupération des ueId de l'array de cours et unification des ueId
      const results = await Promise.all(promiseArray);
      const uniqueResults=[...new Set(results.map((ue)=>ue?.id))]

      // Récupération de la bonne scale en fonction du mcqScaleQuestionType et des id des Ues.
      const scale = await McqScaleService.getMcqScaleForQuestionTypeAndUe({questionType:mcqScaleQuestionType,ueIds:uniqueResults})

      return scale

    }catch(e){
      console.error(e)
      throw new GraphQLError(e)
    }
  }

};