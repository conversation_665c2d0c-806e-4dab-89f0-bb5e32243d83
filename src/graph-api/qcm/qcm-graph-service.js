// Synthesis aggregator for user results by linked courses

import moment from 'moment';
import { Op } from 'sequelize';
import models from '../../models/index.js';
import { PermissionService } from '../permission/permission-service.js';
import { QCMService } from './qcm-service.js';
import { QuestionsService } from './questions/questions-service.js';

const sorterFn = (b, a) => ((a?.goodAnswers / (a?.goodAnswers + a?.badAnswers)) - (b?.goodAnswers / (b?.goodAnswers + b?.badAnswers)));


export default {

  // TODO fonction qui détecte si le user a fait des réponses à des questions mais aggregation non existante en db
  async userNeedsToCreateOrUpdateStatsUserSynthesis(
    {
      userId,
    }) {

    const returnType = {
      needToCreate: 'needToCreate',
      needToUpdate: 'needToUpdate',
      allGood: 'allGood',
    };
    let toReturn = returnType.allGood;

    // Check if user has already stats


    return toReturn;
  },

  async updateGoodAnswersStatsUserSynthesisForUser(userId, debug = false) {

    // For the moment update them all
    // Only user who have answered at least one question
    const userHasAnsweredQuestions = await models.QcmStatsQuestion.findOne({
      where: {
        id_utilisateur: userId,
      },
      raw: true,
      attributes: ['id_statistique_question', 'id_utilisateur'],
    });
    if (!userHasAnsweredQuestions) {
      return
    }

    //TODO voir si peut avoid
    const user = await models.User.findByPk(userId, { raw: true, attributes: ['id', 'role'] });

    const availableUEIdsForUser = await PermissionService.getAvailableUEIdsForUser(user);

    for (const ueId of availableUEIdsForUser) {
      // Set ue as updating
      let statUE = await this.getGoodAnswersStatsSynthesisFor({
        userId,
        ueId: ueId,
      }, { models });
      if (!statUE) {
        // Create
        await models.GoodAnswersStatsUserSynthesis.create({
          ueId: ueId,
          userId,
          isUpdating: true,
        });
      } else {
        // Update
        await models.GoodAnswersStatsUserSynthesis.update({ isUpdating: true }, {
          where: {
            userId,
            ueId: ueId,
          },
        });
      }

      // TODO optimization thinking
      /* Resultats déjà mis à jour */
      /*
      const ueSynthesisStatsForUserInUE = await models.GoodAnswersStatsUserSynthesis.findOne({
        where: {
          userId,
          ueId,
          updatedAt: {
            // Mis à jour il y a moins de 24 h !
            [Op.gte]: moment().subtract(12, 'hours').toDate(),
          }
        }
      });
      if(!ueSynthesisStatsForUserInUE) {
        // Recalcul tout
      } else {
        // Renvoi les stats? ou fait rien, si le calcul se fait en cron (
      }
      */
      //await QCMService.getGraphProgression(userId, ctx);

      const coursIds = await PermissionService.getAvailableCoursIdsInUEForUser(ueId, user); // Cached
      const questionIdsFromCourses = await QuestionsService.getQuestionsIdsFromCoursIds(coursIds); // TODO cache disabled
      const questionsStats = await models.QcmStatsQuestion.findAll({
        where: {
          id_utilisateur: userId,
        },
        raw: true,
      });
      const questionIdsToLookFor = questionsStats.map(s => s.id_question).filter(id => questionIdsFromCourses.includes(id));
      const whereQuestions = {
        id_question: questionIdsToLookFor,
      };

      // Create or update stats to db for user
      await QCMService.getGraphCorrectionDataForQuestions(
        {
          //id_qcms: mcqDoneInUe?.map(q => q.id_qcm),
          whereQuestions,
          firstNodeName: 'Total',
          targettedUserId: userId,
          saveToDB: true,
        });


      /* Set flag to updated */
      await models.GoodAnswersStatsUserSynthesis.update({ isUpdating: false }, {
        where: {
          userId,
          ueId: ueId,
        },
      });
      debug && console.log(`[!] Updated stats for user ${userId} in UE ${ueId}`);

    } // end for ueId

    return true;
  },

  /* Update all */
  async cronUpdateGoodAnswersStatsUserSynthesisForActiveUsers(onlyActiveLastMonth = false) {
    try {

      const debug = false;

      debug && console.log('[!] cronUpdateGoodAnswersStatsUserSynthesisForActiveUsers STARTED! -----');
      // Find active users (not deleted and with activity last 2 months)

      let where = {
        deletedAt: null,
      };
      if (onlyActiveLastMonth) {
        where = {
          ...where,
          updatedAt: {
            [Op.gte]: moment().subtract('1', 'month').toDate(),
          },
        };
      }
      const activeUsersLastMonth = await models.User.findAll({
        where,
        attributes: ['id'],
      });
      const userIds = activeUsersLastMonth?.map(u => u?.id);
      debug && console.log(`${userIds?.length} users to update ${onlyActiveLastMonth ? '(last month active)' : '(all)'}`);

      for (const userId of userIds) {
        await this.updateGoodAnswersStatsUserSynthesisForUser(userId, debug);
      }

      console.log('[!] cronUpdateGoodAnswersStatsUserSynthesisForActiveUsers FINISHED -----');
    } catch (e) {
      console.error(e);
    }
  },

  /**
   * Get user's good answers stats synthesis for given ids
   *
   * @param userId
   * @param ueId
   * @param ueCategoryId
   * @param coursId
   * @param notionId
   * @param ctx
   * @returns {Promise<null>}
   */
  async getGoodAnswersStatsSynthesisFor(
    {
      userId,

      ueId = null,
      ueCategoryId = null,

      coursId = null,
      notionId = null,
    },
    ctx) {
    try {
      const { models } = ctx;

      if (!userId) {
        throw new Error('Missing userId');
      }
      let goodAnswersStatsUserSynthesis = null;

      if (ueId) {
        goodAnswersStatsUserSynthesis = await models.GoodAnswersStatsUserSynthesis.findOne({
          where: {
            userId,
            ueId,
            ueCategoryId: null,
            coursId: null,
            notionId: null,
          },
        });
      } else if (ueCategoryId) {
        goodAnswersStatsUserSynthesis = await models.GoodAnswersStatsUserSynthesis.findOne({
          where: {
            userId,
            ueCategoryId,

            ueId: null,
            coursId: null,
            notionId: null,
          },
        });
      } else if (coursId && !notionId) {
        // COURS seul
        goodAnswersStatsUserSynthesis = await models.GoodAnswersStatsUserSynthesis.findOne({
          where: {
            userId,
            coursId,
            notionId: null,
            ueId: null,
            ueCategoryId: null,
          },
        });
      } else if (notionId && coursId) {
        // Notions de cours
        goodAnswersStatsUserSynthesis = await models.GoodAnswersStatsUserSynthesis.findOne({
          where: {
            userId,
            notionId,
            coursId,
            ueId: null,
            ueCategoryId: null,
          },
        });
      }

      return goodAnswersStatsUserSynthesis;
    } catch (e) {
      console.error('Error in getGoodAnswersStatsSynthesisFor');
      console.error(e);
    }
  },

  /**
   *
   * @param coursId
   * @param userId
   * @param models
   * @returns {Promise<Model[]|null>}
   */
  async getAllNotionsForCours({ coursId, userId }, { models }) {

    if (!userId || !coursId) {
      console.error('Missing coursId or userId');
      return null;
    }

    return await models.GoodAnswersStatsUserSynthesis.findAll({
      where: {
        userId,
        coursId,
        notionId: {
          [Op.ne]: null,
        },
      },
    });

  },


  // Recursive reconstructor for user results


  async buildUETree({ uniqueUes, uniqueCategories, uniqueCours, uniqueNotions }) {

    for (const ue of uniqueUes) {
      // Mettre les catégories dans l'UE
      let ueCategories = uniqueCategories?.filter(c => String(c?.ueId) === String(ue?.id));
      ueCategories = ueCategories?.sort(sorterFn);

      let coursInUE = uniqueCours?.filter(c => String(c?.ueId) === String(ue?.id)) || [];
      let ueChilds = uniqueUes?.filter(c => String(c?.parentId) === String(ue?.id)) || [];

      for (const ueCategory of ueCategories) {
        let categChilds = uniqueCategories?.filter(c => String(c?.parentId) === String(ueCategory?.id)) || [];

        // Mettre les cours dans la catégorie
        let cours = uniqueCours?.filter(c => String(c?.uecategoryId) === String(ueCategory?.id));
        cours = cours?.sort(sorterFn);

        for (const c of cours) {
          let notions = uniqueNotions?.filter(n => n?.coursIds?.includes(String(c?.id)));
          if (notions) {
            notions = notions?.sort(sorterFn);
          }
          c.children = notions;
        }

        ueCategory.children = [...categChilds, ...cours];
      }
      ue.children = [...ueCategories, ...ueChilds, ...coursInUE];
      //////

    }
    uniqueUes = uniqueUes?.sort(sorterFn);
    uniqueUes = uniqueUes?.map(ue => ({
      ...ue,
      id: `ue-${ue.id}`,
      dbId: `${ue.id}`,
      modelType: 'ue',
      // ICI children peuvent être des cours ou des UE (si l'UE a des enfants) ou des UECategories
      children: ue.children?.map(uec => ({
        ...uec,
        id: `ueCategory-${uec.id}`,
        dbId: `${uec.id}`,
        modelType: 'uec',
        collapsed: true,
        // ICI children peuvent être des cours ou des UECategories
        children: uec.children?.map(c => ({
          ...c,
          id: `cours-${c.id}`,
          dbId: `${c.id}`,
          modelType: 'cours',
          collapsed: true,
          children: c.children?.map(n => ({
            ...n,
            id: `notion-${n.id}-${c.id}`,
            dbId: `${n.id}`,
            modelType: 'notion',
            collapsed: true,
          })),
        })),
      })),
    }));

    return uniqueUes;
  },

  // Recursive AGGREGATOR FOR USER RESULTS
  ////////////////////////////////////////
  async getUEDataRecursive(
    {
      ue,
      uniqueUes,
      goodAnswers,
      badAnswers,
      pointsObtained,
      pointsMax,
    }, { models }) {

    if (ue) {
      let ueCurrent = uniqueUes.find(c => String(c.id) === String(ue.id));
      if (!ueCurrent) {
        // Categorie pas dans les categories
        uniqueUes.push({ ...ue, goodAnswers: 0, badAnswers: 0, pointsObtained: 0, pointsMax: 0 });
        ueCurrent = uniqueUes.find(c => String(c.id) === String(ue.id));
      }
      ueCurrent.goodAnswers += goodAnswers;
      ueCurrent.badAnswers += badAnswers;
      ueCurrent.pointsObtained += pointsObtained;
      ueCurrent.pointsMax += pointsMax;
      ueCurrent.id = String(ueCurrent.id);

      if (ue?.parentId) {
        const parentUE = await models.UE.findOne({ where: { id: ue?.parentId }, raw: true });
        let returnedData = await this.getUEDataRecursive({
          ue: parentUE,
          uniqueUes,
          goodAnswers,
          badAnswers,
          pointsObtained,
          pointsMax,
        }, { models });
        uniqueUes = returnedData.uniqueUes;
      }
    }

    return { uniqueUes };
  },

  async handleCourseData(
    {
      cours,
      uniqueCategories,
      uniqueUes,
      goodAnswers,
      badAnswers,
      pointsObtained,
      pointsMax,
    },
    { models },
  ) {
    try {

      // Handle ue category parent
      if (cours?.uecategoryId) {
        // Categ parente du cours
        const ueCategory = await models.UECategory.findOne({ where: { id: cours?.uecategoryId }, raw: true });

        let returnedData = await this.getUECategDataRecursive({
          ueCategory,
          uniqueCategories,
          uniqueUes,
          goodAnswers,
          badAnswers,
          pointsObtained,
          pointsMax,
        }, { models });

        uniqueCategories = returnedData.uniqueCategories;
        uniqueUes = returnedData.uniqueUes;
      }

      if (cours?.ueId) {
        // UE parente du cours (si pas de categ parente)
        const ueParentToCourse = await models.UE.findOne({ where: { id: cours?.ueId }, raw: true });

        let returnedData = await this.getUEDataRecursive({
          ue: ueParentToCourse,
          uniqueUes,
          goodAnswers,
          badAnswers,
          pointsObtained,
          pointsMax,
        }, { models });
        uniqueUes = returnedData.uniqueUes;
      }

      return {
        uniqueCategories,
        uniqueUes,
      };

    } catch (e) {
      console.error(e);
    }
  },

  async getUECategDataRecursive(
    {
      ueCategory,
      uniqueCategories,
      uniqueUes,
      goodAnswers,
      badAnswers,
      pointsObtained,
      pointsMax,
    }, { models }) {

    if (ueCategory) {
      // Cherche categ dans liste unique
      let ucateg = uniqueCategories.find(c => String(c.id) === String(ueCategory.id));
      if (!ucateg) {
        // Categorie pas dans les categories
        uniqueCategories.push({
          ...ueCategory,
          goodAnswers: 0,
          badAnswers: 0,
          pointsObtained: 0,
          pointsMax: 0,
        });
        ucateg = uniqueCategories.find(c => String(c.id) === String(ueCategory.id));
      }
      ucateg.goodAnswers += goodAnswers;
      ucateg.badAnswers += badAnswers;
      ucateg.pointsObtained += pointsObtained;
      ucateg.pointsMax += pointsMax;
      ucateg.id = String(ucateg.id);

      if (ueCategory?.parentId) {
        // Handle UECateg parent to categ if any
        const categParent = await models.UECategory.findOne({ where: { id: ueCategory?.parentId }, raw: true });

        let returnedData = await this.getUECategDataRecursive({
          ueCategory: categParent,
          uniqueCategories,
          uniqueUes,
          goodAnswers,
          badAnswers,
          pointsObtained,
          pointsMax,
        }, { models });
        uniqueCategories = returnedData.uniqueCategories;
        uniqueUes = returnedData.uniqueUes;
      }

      if (ueCategory?.ueId) {
        // Handle UE parent to categ if any
        const ue = await models.UE.findOne({ where: { id: ueCategory?.ueId }, raw: true });
        let returnedData = await this.getUEDataRecursive({
          ue,
          uniqueUes,
          goodAnswers,
          badAnswers,
          pointsObtained,
          pointsMax,
        }, { models });
        uniqueUes = returnedData.uniqueUes;
      }
    }

    return { uniqueCategories, uniqueUes };
  },

};