import fs from 'fs';
import moment from 'moment';
import xlsx from 'node-xlsx';
import { Op } from 'sequelize';
import { generate } from 'shortid';
import { v4 as uuidv4 } from 'uuid';
import { deserialize } from 'v8'
import models from '../../models/index.js';
import { UPLOAD_FOLDER_MAP } from '../../util/utils.js';
import { FileService } from '../file/file-service.js';
import { alphabetArray, QCMService as QcmService } from './qcm-service.js';
import { QCMStatsService } from './qcm-stats-service.js';
import { QuestionsService } from './questions/questions-service.js';
const json2xls = require('json2xls');

export const QcmExportService = {


  exportMcqQuestionsOnly: async (idQcm) => {
    /* QCM */
    const qcm = await models.Qcm.findByPk(idQcm);
    /* GET QUESTIONS */
    let whereQuestions = {};
    if (qcm?.hasExternalQuestions) {
      // get external questions
      const externalQuestions = await models.QuestionsQcm.findAll({
        where: {
          qcmId: idQcm,
        },
      });
      whereQuestions = { id_question: externalQuestions.map(m => m.questionId) };
    } else {
      // Classic QCM
      whereQuestions = { id_qcm: idQcm };
    }
    const questions = await models.Question.findAll({
      where: whereQuestions,
    });

    let dataXls = [];
    dataXls.push([
      'id_qcm', 'CHAPITRE', 'SOUS CHAPITRE', 'ID QUESTION', 'TYPE QUESTION',
      'TEXTE QUESTION', 'A/B/C/D/E..', 'TEXTE PROPOSITION', 'Réponse à donner (0/1)',
      'Explication item', 'Explication globale', 'Difficulté estimée',
    ]);
    for (const question of questions) {
      const currentLineQuestion = [];

      const cours = await QuestionsService.getCoursLinkedForQuestion(question);
      let categoriesString = '';
      if (cours) {
        for (const c of cours) {
          const category = await models.UECategory.findByPk(c.uecategoryId);
          if (category) {
            categoriesString = categoriesString + category.name + ', ';
          }
        }
      }
      const coursStrings = Array.from(
        new Set(cours?.map(cour => `${cour.name} ${cour?.text || ''}`))
      ).join(', ') || '';


      let typeQuestion = question.isCheckbox ? 'QRM' : 'QRU';
      typeQuestion = question.isAnswerFreeText ? 'QTL' : typeQuestion;
      const question_answers = await QcmService.getQuestionAnswers(question?.id_question);
      for (const [key, answer] of Object.entries(question_answers)) {
        dataXls.push([
            qcm.id_qcm,
            categoriesString,
            coursStrings,
            question.id_question,
            typeQuestion,
            question.question,
            alphabetArray[key], // A B C D E
            answer.text,
            answer.isTrue, // rep à donner
            answer.explanation, // explications
            question.explications, // explications globales
            question.definedDifficulty,

          ],
        );
      }
    }

    // Returns a buffer
    return xlsx.build([{
      name: 'MCQ',
      data: dataXls,
    }]);

  },


  inplacePrepareFormationElementToExport:async(elem)=>{

    try {

      // Jsonification de l'élément settings
      if (typeof (elem.settings)==="string"){xlsx
        elem.settings=JSON.parse(elem.settings)
      }

      /////// Serialisation des fichiers :
      // Sérialization des images
      if (elem.image) {
        const link = `${process.cwd()}/${UPLOAD_FOLDER_MAP.files}/${elem.image}`
        const oki = await FileService.toBase64(link)

        const temp = {
          fileName: elem.image,
          data: oki
        }
        elem.image=temp
      }

      // Sérialization des files
      if (elem.type === "file" && elem.text) {
        const link = `${process.cwd()}/${UPLOAD_FOLDER_MAP.files}/${elem.text}`
        const oki = await FileService.toBase64(link)

        const temp = {
          fileName: elem.text,
          data: oki
        }

        elem.text = temp
      }

      // Sérialization des watermark
      if (elem.type === "file" && elem.settings && elem.settings.picture_picture && elem.watermarkPicturePath) {

        const link = `${process.cwd()}/${UPLOAD_FOLDER_MAP.files}/${elem.settings.picture_picture}`
        const oki = await FileService.toBase64(link)

        const temp = {
          fileName: elem.settings.picture_picture,
          data: oki
        }
        elem.settings = { ...elem.settings, picture_picture: temp }
      }


      // Envoi des informations de coursId
      if (!!elem.coursId){
        const cours=await models.Cours.findByPk(elem.coursId)
        elem.coursId={id:cours.id,description:`${cours.name} : ${cours.text}`}
      }

      // envoi des informations de titleId
      if (!!elem.titleId){
        const title=await models.Title.findByPk(elem.titleId)
        const data = JSON.stringify(title.get({plain:true}))
        elem.titleId={id:title.id,data}
      }
      return elem
    }catch(e){
      console.error ("error dans préparation FE : ",e)
    }

  },

  reworkExportFullMcqToJson:async(_,{mcqId},__)=>{
    /* fonction d'export du QCM avec toutes les données associées*/
    try{


      //Get du QCM et données associées
      let fullQcm = await models.Qcm.findByPk(mcqId, {
        attributes: { exclude: ['id_qcm'] },
        include: [

          //// Les données nécessaires à la bonne importation du QCM
          { // Le type du qcm
            attributes:['id','name','contentType'],
            model:models.TypeQcm,
            through:{attributes:[]}
          },
          { // le default type des questions du QCM
            attributes:['id','name','contentType'],
            model:models.TypeQcm,
            as:'defaultTypeQcmForQuestions',
          },
          { // L'UE du QCM
            attributes:['id','name','description'],
            model:models.UE,
            as:'UE',
            required:true
          },
          {
            model: models.FormationElement,
            as: 'headerElements',
          },

          // Les questions et données associées  => note : les Notions ont pas étées importées
          {
            model: models.Question,
            as:'importedQuestion',
            include: [
              {
                model:models.McqScale,
                attributes:['id','name','rules'],
                as:'mcqScale'
              },
              {
                attributes: { exclude: ['questionId', 'id'] },
                model: models.QuestionAnswers,
                required: true,
              },
              {
                model: models.Cours,
                attributes: ['id', 'name','text'],
                through: {attributes: []},
                //as:'questionCours'
              },
              {
                model:models.TypeQcm,
                attributes:['id','name'],
                through:{attributes:[]},
              },
              {
                model: models.FormationElement,
                as: 'headerElements',
              },
              {
                model: models.FormationElement,
                as: 'footerElements',
              },
            ],
          },
        ],
      });

      if (!fullQcm){throw new Error("pas de QCM associé")}



      /// Export des images pour chaque structure de donnée
      if (fullQcm.importedQuestion){

        // Pour chaque question alors on prépare le formationElementToExport
        for (let q of fullQcm.importedQuestion){

          // Pour chaque Question, préparation des formationsElements des headers.
          if (q.headerElements){
            for (let fe of q.headerElements){ await QcmExportService.inplacePrepareFormationElementToExport(fe)}
          }

          // Pour chaque Question, préparation des formations éléments des footers
          if (q.footerElements){
            for (let fe of q.footerElements){ await QcmExportService.inplacePrepareFormationElementToExport(fe)}
          }

          // pour chaque question, préparation des answers et notament des images
          if (q.question_answers){

            // Pour chaque questions answer
            for (let a of q.question_answers){

              // Si l'énoncé a une image , alors on la save en base64  /!\ utilisation du système legacy des QCM. =>
              if (a.url_image){
                const imgLink=(a.url_image).replace('/qcm/uploads/','')
                const link = `${UPLOAD_FOLDER_MAP.qcm}/${imgLink}`

                const temp={
                  fileName:imgLink,
                  data:await FileService.toBase64(link),
                }
                a.url_image=temp
              }

              // Si la correction a une image, alors on la save en base64 /!\ utilisation du système legacy des QCM
              if (a.url_image_explanation){
                const imgLink=(a.url_image_explanation).replace('/qcm/uploads/','')
                const link = `${UPLOAD_FOLDER_MAP.qcm}/${imgLink}`

                const temp2={
                  fileName:imgLink,
                  data:await FileService.toBase64(link),
                }
                a.url_image_explanation=temp2

              }
            }
          }
        }


        // Préparation des formations Elements du QCM
        for (let fe of fullQcm.headerElements){
          await QcmExportService.inplacePrepareFormationElementToExport(fe)
        }
      }

      // On unpublie le QCM afin de permettre à l'user de le corriger si besoin et de le publier lui même
      fullQcm.isPublished= false

      return JSON.stringify({
        qcm: fullQcm,
      });

    }catch(e){
      console.error("error reworkExportFullMcqToJson:",e)
    }
  },

  /*
  exportFullMcqToJson: async (mcqId) => {
    try {
      let fullQcm = await models.Qcm.findByPk(mcqId, {
        attributes: { exclude: ['id_qcm', 'UEId', 'annee'] },
        include: [
          {
            model: models.Question,
            attributes: { exclude: ['id_question', 'id_qcm', 'linkCoursId', 'mcqScaleId', 'authorId'] },
            include: [
              {
                attributes: { exclude: ['questionId', 'id'] },
                model: models.QuestionAnswers,
                required: true,
              },
              {
                attributes: { exclude: ['questionId', 'id', 'coursId', 'titleId', 'eventId'] },
                model: models.FormationElement,
                as: 'headerElements',
              },
              {
                attributes: { exclude: ['questionId', 'id', 'coursId', 'titleId', 'eventId'] },
                model: models.FormationElement,
                as: 'footerElements',
              },
            ],
          },
          {
            attributes: { exclude: ['questionId', 'id', 'coursId', 'titleId', 'eventId'] },
            model: models.FormationElement,
            as: 'headerElements',
          },
          {
            attributes:['id','name','description'],
            model:models.UE,
            as:'UE',
            required:true
          }
        ],
      });
      let imagesQuestions = [];
      let filesElements = [];


      // Ajout éléments annexes QCM
      if (fullQcm?.headerElements?.length > 0) {


        for (const elem of fullQcm?.headerElements) {

          //await this.serialiseFormationElement(elem)


          if (elem.image) {
            let file = await FileService.toBase64(`${UPLOAD_FOLDER_MAP.files}/${elem.image}`);
            filesElements.push({
              filename: elem.image,
              data: file,
            });
          }
          if (elem?.type === 'file' && elem.text) {
            let file = await FileService.toBase64(`${UPLOAD_FOLDER_MAP.files}/${elem.text}`);
            filesElements.push({
              filename: elem.text,
              data: file,
            });
          }
        }
      }


      for (let q of fullQcm.questions) {
        // récupérer les fichiers images
        delete q?.mcqScaleId;
        delete q?.authorId;
        q.elements = [];
        // Formation elements header

        if (q?.headerElements?.length > 0) {
          for (const elem of q?.headerElements) {
            if (elem.image) {
              let file = await FileService.toBase64(`${UPLOAD_FOLDER_MAP.files}/${elem.image}`);
              filesElements.push({
                filename: elem.image,
                data: file,
              });
            }
            if (elem?.type === 'file' && elem.text) {
              let file = await FileService.toBase64(`${UPLOAD_FOLDER_MAP.files}/${elem.text}`);
              filesElements.push({
                filename: elem.text,
                data: file,
              });
            }
          }
        }
        if (q?.footerElements?.length > 0) {
          for (const elem of q?.footerElements) {
            if (elem.image) {
              let file = await FileService.toBase64(`${UPLOAD_FOLDER_MAP.files}/${elem.image}`);
              filesElements.push({
                filename: elem.image,
                data: file,
              });
            }
            if (elem?.type === 'file' && elem.text) {
              let file = await FileService.toBase64(`${UPLOAD_FOLDER_MAP.files}/${elem.text}`);
              filesElements.push({
                filename: elem.text,
                data: file,
              });
            }
          }
        }

        if (!!q.url_image_q) {
          let file = await FileService.toBase64(`${UPLOAD_FOLDER_MAP.qcm}/../..${q.url_image_q}`);
          if (file) {
            imagesQuestions.push({
              filename: q.url_image_q,
              data: file,
            });
          }
        }
        if (!!q.url_image_explication) {
          let file = await FileService.toBase64(`${UPLOAD_FOLDER_MAP.qcm}/../..${q.url_image_explication}`);
          if (file) {
            imagesQuestions.push({
              filename: q.url_image_explication,
              data: file,
            });
          }
        }
      }
      fullQcm.id_lien = uuidv4();
      return JSON.stringify({
        qcm: fullQcm,
        files: imagesQuestions,
        filesElements,
      });
    }catch(e){
      console.log("error :",e)
    }
  },

   */

  async exportTableJSONToXLSDownload(json, name, addUUID, ctx) {
    const xls = json2xls(json);
    let fileName =`${name.replace(/[/\\?%*:|"<>]/g, '-')}`
    if (addUUID) {fileName+=uuidv4()}
    fileName+=".xlsx"
    await fs.writeFileSync(`${UPLOAD_FOLDER_MAP.files}/${fileName}`, xls, 'binary');
    return fileName;
  },

  async exportQcmResultsToXlsFromUserProfile(userId) {
    const notesUser = await models.QcmStats.findAll({
      where: {
        id_utilisateur: userId,
      },
      order: [['note', 'DESC']],
    });

    const ongletsXls = [];
    let dataXls = [];

    const backgroundTabData = [
      ['user id', 'Date inscription', 'username', 'email', 'typeSociete', 'fonction', 'anciennete', 'niveauFormation', 'lienBanqueFinance', 'precisionFormation'],
    ];

    const user = await models.User.findByPk(userId);
    if (user.background) {
      backgroundTabData.push(
        [
          user.id,
          moment(user.createdAt).toDate(),
          user.username,
          user.email,
          user.background.typeSociete,
          user.background.fonction,
          user.background.anciennete,
          user.background.niveauFormation,
          user.background.lienBanqueFinance,
          user.background.precisionFormation,
        ],
      );
    }

    const backgroundTab = {
      name: 'User background',
      data: backgroundTabData,
    };
    ongletsXls.push(backgroundTab);

    dataXls.push([
      'Etat de la session',
      'userId',
      'id_qcm', 'CHAPITRE', 'SOUS CHAPITRE', 'ID QUESTION', 'TYPE QUESTION',
      'TEXTE QUESTION', 'A/B/C/D/E..', 'TEXTE PROPOSITION', 'Réponse à donner (0/1)',
      'Réponse donnée', 'juste ? 0/1', 'Explication item', 'Explication globale', 'Difficulté estimée',
      'Temps de réponse', 'Niveau de confiance', 'Points obtenus', 'Points pondérés obtenus', 'Note finale',
      'Note pondérée finale',
      'Date démarrage', 'Date de fin',
    ]);

    for (const userResult of notesUser) {
      const userId = userResult.id_utilisateur;
      const user = await models.User.findByPk(userId);
      const session = await models.QcmSession.findByPk(userResult.qcmSessionId);

      const idQcm = userResult.id_qcm;
      /* QCM */
      const qcm = await models.Qcm.findByPk(idQcm,
        { attributes: ['hasExternalQuestions'] });
      /* GET QUESTIONS */
      let whereQuestions = {};
      if (qcm?.hasExternalQuestions) {
        // get external questions
        const externalQuestions = await models.QuestionsQcm.findAll({
          where: {
            qcmId: idQcm,
          },
        });
        whereQuestions = { id_question: externalQuestions.map(m => m.questionId) };
      } else {
        // Classic QCM
        whereQuestions = { id_qcm: idQcm };
      }
      const questions = await models.Question.findAll({
        where: whereQuestions,
      });


      for (const question of questions) {
        const currentLineQuestion = [];

        const cours = await QuestionsService.getCoursLinkedForQuestion(question);
        let categoriesString = '';
        if (cours) {
          for (const c of cours) {
            const category = await models.UECategory.findByPk(c.uecategoryId);
            if (category) {
              categoriesString = categoriesString + category.name + ', ';
            }
          }
        }
        const coursStrings = Array.from(
          new Set(cours?.map(cour => `${cour.name} ${cour?.text || ''}`))
        ).join(', ') || '';


        let typeQuestion = question.isCheckbox ? 'QRM' : 'QRU';
        typeQuestion = question.isAnswerFreeText ? 'QTL' : typeQuestion;

        /* Stats by question */
        const currentUserStats = await models.QcmStatsQuestion.findOne({
          where: {
            id_utilisateur: user.id,
            id_question: question.id_question,
            qcmSessionId: userResult.qcmSessionId,
          },
        });

        let currentUserAnswersStats = [];

        if (currentUserStats) {
          currentUserAnswersStats = await models.StatsQuestionAnswers.findAll({
            where: {
              statsQuestionId: currentUserStats?.id_statistique_question,
            },
          });
        }
        const question_answers = await QcmService.getQuestionAnswers(question?.id_question);
        for (const [key, answer] of Object.entries(question_answers)) {
          const actualAnswer = currentUserAnswersStats?.find(c => c.answerId === answer.id);
          dataXls.push([
              `${session?.isFinished ? 'TERMINÉ' : `NON-TERMINÉ (${session?.doneQuestionsCount}Q)`}`,
              userId,
              qcm.id_qcm,
              categoriesString,
              coursStrings,
              question.id_question,
              typeQuestion,
              question.question,
              alphabetArray[key], // A B C D E
              answer.text,
              answer.isTrue, // rep à donner
              actualAnswer?.answerId === answer.id ? '1' : '0', // réponse donnée
              actualAnswer?.isGood ? '1' : '0', // juste ? 0/1 isGood
              answer.explanation, // explications
              question.explications, // explications globales
              question.definedDifficulty,
              currentUserStats?.time,
              currentUserStats?.certainty,

              currentUserStats?.pointsObtained, // Points obtenus question
              currentUserStats?.ponderatedPointsObtained,

              userResult.note, // Note finale
              userResult?.ponderatedGrade, // Note pondé

              moment(session?.createdAt)?.toDate(),
              moment(session?.updatedAt)?.toDate(),
            ],
          );
        }
      }
    }

    ongletsXls.push({
      name: `${user?.username?.slice(0, 10) || ''} ${user?.id}`,
      data: dataXls,
    });

    const options = { '!cols': [{ wch: 6 }, { wch: 7 }, { wch: 10 }, { wch: 20 }] };
    const buffer = xlsx.build(ongletsXls); // Returns a buffer
    return buffer;
  },

  // with notions
  async exportQcmResultsToXLSWithAllInSamePage(idQcm) {
    try {
      console.log('EXPORT: getting all results for QCM');
      const notesUser = await QCMStatsService.getAllUserResultsForQCM(idQcm); // grades only
      /* QCM */
      const qcm = await models.Qcm.findByPk(idQcm, { attributes: ['hasExternalQuestions'] });
      /* GET QUESTIONS */
      let whereQuestions = {};
      if (qcm?.hasExternalQuestions) {
        // get external questions
        const externalQuestions = await models.QuestionsQcm.findAll({
          where: {
            qcmId: idQcm,
          },
        });
        whereQuestions = { id_question: externalQuestions.map(m => m.questionId) };
      } else {
        // Classic QCM
        whereQuestions = { id_qcm: idQcm };
      }
      console.log('EXPORT: getting all Exercises');
      const questions = await models.Question.findAll({
        where: whereQuestions,
      });

      /*
      "{\"typeSociete\":\"Banque Privée\",
        \"fonction\":\"Conseiller pour une clientèle de :  particuliers\",
        \"anciennete\":\"1 an ou moins d'un an\",\
        "niveauFormation\":\"Bac +2\",
        \"lienBanqueFinance\":\"non\",
        \"precisionFormation\":\"Bts nrc\"}"
       */

      // Récupération des IDs utiles
      const allUserIds = notesUser.map(u => u.id_utilisateur);
      const allSessionIds = notesUser.map(u => u.qcmSessionId);
      const allQuestionIds = questions.map(q => q.id_question);

      /* BULK SELECT */
      // 1. Users en bulk
      const users = await models.User.findAll({
        where: { id: allUserIds },
        raw: true,
      });
      const usersMap = Object.fromEntries(users.map(u => [u.id, u]));

// 2. Sessions en bulk
      const sessions = await models.QcmSession.findAll({
        where: { id: allSessionIds },
      });
      const sessionsMap = Object.fromEntries(sessions.map(s => [s.id, s]));

// 3. Stats par question x utilisateur
      const allStats = await models.QcmStatsQuestion.findAll({
        where: {
          id_utilisateur: allUserIds,
          id_question: allQuestionIds,
          //qcmSessionId: allSessionIds,
        },
      });
      const statsMap = new Map(); // key: `${userId}_${questionId}_${sessionId}`
      for (const stat of allStats) {
        statsMap.set(`${stat.id_utilisateur}_${stat.id_question}_${stat.qcmSessionId===null?'null':stat.qcmSessionId}`, stat);
      }

// 4. Answers stats
      const allStatIds = allStats.map(s => s.id_statistique_question);
      const allAnswerStats = await models.StatsQuestionAnswers.findAll({
        where: { statsQuestionId: allStatIds },
      });
      const answersStatsMap = new Map(); // key: statsQuestionId => array
      for (const ans of allAnswerStats) {
        if (!answersStatsMap.has(ans.statsQuestionId)) {
          answersStatsMap.set(ans.statsQuestionId, []);
        }
        answersStatsMap.get(ans.statsQuestionId).push(ans);
      }

// 5. Cours liés à chaque question
      // questionId -> cours[]
      const coursMapByQuestion = await QuestionsService.getCoursLinkedForQuestionsMap(questions);

// 6. UE Category en bulk
      const allCategoryIds = new Set();
      for (const coursList of coursMapByQuestion.values()) {
        for (const cours of coursList) {
          if (cours?.uecategoryId) {
            allCategoryIds.add(cours.uecategoryId);
          }
        }
      }
      const categories = await models.UECategory.findAll({
        where: { id: [...allCategoryIds] },
      });
      const categoryMap = Object.fromEntries(categories.map(cat => [cat.id, cat.name]));
      ///////////////////////////////

      let dataXls = [];
      dataXls.push([
        'Etat de la session',
        'userId', 'Pseudo', 'Prénom', 'Nom',
        'id_qcm', 'CHAPITRE', 'SOUS CHAPITRE', 'ID QUESTION', 'TYPE QUESTION',
        'TEXTE QUESTION', 'A/B/C/D/E..', 'TEXTE PROPOSITION', 'Réponse à donner (0/1)',
        'Réponse donnée', 'juste ? 0/1', 'Explication item', 'Explication globale', 'Difficulté estimée',
        'Temps de réponse', 'Niveau de confiance', 'Points obtenus', 'Points pondérés obtenus', 'Note finale',
        'Note pondérée finale',
        'Date démarrage', 'Date de fin',
      ]);

      console.log('EXPORT: entering main loop...');
      for (const userResult of notesUser) {
        const userId = userResult.id_utilisateur;
        const user = usersMap[userResult.id_utilisateur];
        const session = sessionsMap[userResult.qcmSessionId];

        for (const question of questions) {
          const currentLineQuestion = [];

          const cours = coursMapByQuestion.get(question.id_question) || [];

          const categoriesString = [
            ...new Set(cours.map(c => categoryMap[c.uecategoryId]).filter(Boolean))
          ].join(', ');

          const coursStrings = [
            ...new Set(cours.map(c => `${c.name} ${c.text || ''}`))
          ].join(', ');

          let typeQuestion = question.isCheckbox ? 'QRM' : 'QRU';
          typeQuestion = question.isAnswerFreeText ? 'QTL' : typeQuestion;

          const statsKey = `${userId}_${question.id_question}_${userResult.qcmSessionId===null? 'null':userResult.qcmSessionId}`;
          const currentUserStats = statsMap.get(statsKey);
          const currentUserAnswersStats = currentUserStats
            ? answersStatsMap.get(currentUserStats.id_statistique_question) || []
            : [];

          const question_answers = await QcmService.getQuestionAnswers(question?.id_question); // à garder async

          /* Stats by question */
          for (const [key, answer] of Object.entries(question_answers)) {
            const actualAnswer = currentUserAnswersStats?.find(c => c.answerId === answer.id);
            //const actualAnswer = actualAnswers?.filter(a => a.answerId === answer.id)
            //actualAnswer?.answerId can be undefined (QRM)
            //console.log(actualAnswer?.answerId, answer.id);

            // Disabled notions
            /*
            const notionsQs = await models.NotionQuestionAnswers.findAll({
              where: { answerId: answer.id },
            });
            const notionIds = notionsQs?.map(q => q.notionId);
             */

            const answerToGive = answer.isTrue ? '1' : '0';
            const answerGiven = actualAnswer?.answerId === answer.id ? '1' : '0';
            dataXls.push([
                `${session?.isFinished ? 'TERMINÉ' : `NON-TERMINÉ (${session?.doneQuestionsCount}Q)`}`,
                userId, user.username, user.firstName, user.name,
                qcm.id_qcm,
                categoriesString,
                coursStrings,
                question.id_question,
                typeQuestion,
                question.question,
                alphabetArray[key], // A B C D E
                answer.text,
                answerToGive, // rep à donner
                answerGiven, // réponse donnée
                answerToGive === answerGiven ? '1' : '0', // juste ? 0/1 isGood
                answer.explanation, // explications
                question.explications, // explications globales
                question.definedDifficulty,
                currentUserStats?.time,
                currentUserStats?.certainty,
                currentUserStats?.pointsObtained, // Points obtenus question
                currentUserStats?.ponderatedPointsObtained,
                userResult.note, // Note finale
                userResult?.ponderatedGrade, // Note pondé
                moment(session?.createdAt)?.toDate(),
                moment(session?.updatedAt)?.toDate(),
                //notionIds?.join(','),
              ],
            );
          }
        }
      }
      console.log('EXPORT: main loop done, building XLS...');

      /*
      const getColumnSizes = (data) => {
        const colSizes = data?.[0]?.map((_, colIndex) => {
          const colValues = data?.map(row => row[colIndex]);
          const maxLength = Math.max(...colValues?.map(val => (val ? val?.toString()?.length : 0)));
          return { wch: maxLength >= 10 ? maxLength - 1 : maxLength };
        });
        return colSizes;
      };
      const sheetOptions = { '!cols': getColumnSizes(dataXls) };
      */

      const options = { '!cols': [{ wch: 6 }, { wch: 7 }, { wch: 10 }, { wch: 20 }] };
      const buffer = xlsx.build([{
        name: 'User results',
        data: dataXls,
      }], ); // Returns a buffer
      return buffer;
    } catch (e) {
      console.error(e);
    }
  },


  async exportQcmResultsToXLS(idQcm) {
    const ongletsXls = [];
    const notesUser = await QCMStatsService.getAllUserResultsForQCM(idQcm);
    /* QCM */
    const qcm = await models.Qcm.findByPk(idQcm, { attributes: ['hasExternalQuestions'] });
    /* GET QUESTIONS */
    let whereQuestions = {};
    if (qcm?.hasExternalQuestions) {
      // get external questions
      const externalQuestions = await models.QuestionsQcm.findAll({
        where: {
          qcmId: idQcm,
        },
      });
      whereQuestions = { id_question: externalQuestions.map(m => m.questionId) };
    } else {
      // Classic QCM
      whereQuestions = { id_qcm: idQcm };
    }
    const questions = await models.Question.findAll({
      where: whereQuestions,
    });

    const userIds = notesUser?.map(u => u.id_utilisateur);

    const usersForQcm = await models.User.findAll({
      where: {
        id: {
          [Op.in]: userIds,
        },
      },
    });

    /*
    "{\"typeSociete\":\"Banque Privée\",
      \"fonction\":\"Conseiller pour une clientèle de :  particuliers\",
      \"anciennete\":\"1 an ou moins d'un an\",\
      "niveauFormation\":\"Bac +2\",
      \"lienBanqueFinance\":\"non\",
      \"precisionFormation\":\"Bts nrc\"}"
     */
    const backgroundTabData = [
      ['user id', 'Date inscription', 'username', 'email', 'typeSociete', 'fonction', 'anciennete', 'niveauFormation', 'lienBanqueFinance', 'precisionFormation'],
    ];
    for (const user of usersForQcm) {
      if (user.background) {
        backgroundTabData.push(
          [
            user.id,
            moment(user.createdAt).toDate(),
            user.username,
            user.email,
            user.background.typeSociete,
            user.background.fonction,
            user.background.anciennete,
            user.background.niveauFormation,
            user.background.lienBanqueFinance,
            user.background.precisionFormation,
          ],
        );
      }
    }

    const backgroundTab = {
      name: 'Background',
      data: backgroundTabData,
    };

    ongletsXls.push(backgroundTab);

    for (const userResult of notesUser) {
      let dataXls = [];
      const userId = userResult.id_utilisateur;
      const user = await models.User.findByPk(userId);
      const session = await models.QcmSession.findByPk(userResult.qcmSessionId);

      dataXls.push([
        `QCM ${session?.isFinished ? 'TERMINÉ' : `NON-TERMINÉ (${session?.doneQuestionsCount}Q)`}`,
        'id_qcm', 'CHAPITRE', 'SOUS CHAPITRE', 'ID QUESTION', 'TYPE QUESTION',
        'TEXTE QUESTION', 'A/B/C/D/E..', 'TEXTE PROPOSITION', 'Réponse à donner (0/1)',
        'Réponse donnée', 'juste ? 0/1', 'Explication item', 'Explication globale', 'Difficulté estimée',
        'Temps de réponse', 'Niveau de confiance', 'Points obtenus', 'Points pondérés obtenus', 'Note finale',
        'Note pondérée finale',
        'Date démarrage', 'Date de fin',
      ]);

      for (const question of questions) {
        const currentLineQuestion = [];

        const cours = await QuestionsService.getCoursLinkedForQuestion(question);
        let categoriesString = '';
        if (cours) {
          for (const c of cours) {
            const category = await models.UECategory.findByPk(c.uecategoryId);
            if (category) {
              categoriesString = categoriesString + category.name + ', ';
            }
          }
        }
        const coursStrings = Array.from(
          new Set(cours?.map(cour => `${cour.name} ${cour?.text || ''}`))
        ).join(', ') || '';


        let typeQuestion = question.isCheckbox ? 'QRM' : 'QRU';
        typeQuestion = question.isAnswerFreeText ? 'QTL' : typeQuestion;

        /* Stats by question */
        const currentUserStats = await models.QcmStatsQuestion.findOne({
          where: {
            id_utilisateur: user.id,
            id_question: question.id_question,
            qcmSessionId: userResult.qcmSessionId,
          },
        });

        let currentUserAnswersStats = [];

        if (currentUserStats) {
          currentUserAnswersStats = await models.StatsQuestionAnswers.findAll({
            where: {
              statsQuestionId: currentUserStats?.id_statistique_question,
            },
          });
        }

        const question_answers = await QcmService.getQuestionAnswers(question?.id_question);
        for (const [key, answer] of Object.entries(question_answers)) {
          const actualAnswer = currentUserAnswersStats?.find(c => c.answerId === answer.id);
          //const actualAnswer = actualAnswers?.filter(a => a.answerId === answer.id)
          dataXls.push([
              '',
              qcm.id_qcm,
              categoriesString,
              coursStrings,
              question.id_question,
              typeQuestion,
              question.question,
              alphabetArray[key], // A B C D E
              answer.text,
              answer.isTrue, // rep à donner
              actualAnswer?.answerId === answer.id ? '1' : '0', // réponse donnée
              actualAnswer?.isGood ? '1' : '0', // juste ? 0/1 isGood
              answer.explanation, // explications
              question.explications, // explications globales
              question.definedDifficulty,
              currentUserStats?.time,
              currentUserStats?.certainty,

              currentUserStats?.pointsObtained, // Points obtenus question
              currentUserStats?.ponderatedPointsObtained,

              userResult.note, // Note finale
              userResult?.ponderatedGrade, // Note pondé

              moment(session?.createdAt)?.toDate(),
              moment(session?.updatedAt)?.toDate(),
            ],
          );
          // dataXls.push(currentLineQuestion)
        }
      }

      ongletsXls.push({
        name: `${user?.username?.slice(0, 10) || ''} ${user?.id} (${userResult?.qcmSessionId})`,
        data: dataXls,
      });
    }

    // QCMService.exportFullMcqToJson()
    // const data = [[1, 2, 3], [true, false, null, 'sheetjs'], ['foo', 'bar', new Date('2014-02-19T14:30Z'), '0.3'], ['baz', null, 'qux']]
    const options = { '!cols': [{ wch: 6 }, { wch: 7 }, { wch: 10 }, { wch: 20 }] };
    //const buffer = xlsx.build([{ name: ' ', data: dataXls }]) // Returns a buffer
    const buffer = xlsx.build(ongletsXls); // Returns a buffer
    return buffer;
  },

};