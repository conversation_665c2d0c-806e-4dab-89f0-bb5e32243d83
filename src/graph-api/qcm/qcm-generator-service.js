import { Graph<PERSON>Error } from 'graphql';
import { Op } from 'sequelize';
import { CoursTypesQcmSettings_MODULE_TYPES } from '../../models/cours_types_qcm_settings.js';
import models, { sequelize } from '../../models/index.js';
import { CoursService } from '../cours/cours-service.js';
import { EXOQUALIZE } from '../helpers.js';
import { PermissionService } from '../permission/permission-service.js';
import { AllSchemaQuestionTypesArray, QCMService } from './qcm-service.js';
import { QuestionsService } from './questions/questions-service.js';
import { QcmTypeService } from './type/qcm-type-service.js';

/* Settings:
* - ueIds: mandatory
* - typeQcms: mandatory
* */

export const QCMGeneratorService = {

  getQuestionsIdsFromSettings: async (
    {
      ueIds,
      categoriesIds = null,
      coursIds = null,
      typeQcms, // exercices type IDS
      // isQuestionOrderRandom,
      // isAnswerOrderRandom = false, // Front end only
      nombreQuestions,
      //infiniteQuestionByQuestion = false,
      anneeDebut = null, // toremove
      anneeFin = null, // toremove
      annees = null,
      onlyPublished = true,
    },
    user,
    throwIfNoQuestions = true,
  ) => {
    try {
      let shouldLog = false;
      let idQuestionsFromTypes = await QcmTypeService.getQuestionIdsInTypes(typeQcms);
      if (idQuestionsFromTypes?.length === 0) {
        if (throwIfNoQuestions) {
          throw new GraphQLError('Aucun exercice trouvé avec ce(s) type(s)');
        } else {
          return [];
        }
      }
      shouldLog && console.log('idQuestionsFromTypes', idQuestionsFromTypes);

      let questionIdsAvailable = await PermissionService.getAvailableQuestionsIdsByTypeQcmForUser(user);
      questionIdsAvailable = questionIdsAvailable?.filter(id => idQuestionsFromTypes.includes(id));

      shouldLog && console.log('questionIdsAvailableByTypeForUser', questionIdsAvailable);

      const hasCategoriesWithAllCourses = categoriesIds && !coursIds;
      let coursIdsFromCategories = null;

      if (hasCategoriesWithAllCourses) {
        // Récupère les cours dans cette catégories
        shouldLog && console.log('hasCategoriesWithAllCourses');
        coursIdsFromCategories = await CoursService.getCoursIdsInUeCategoryIds(categoriesIds); // récupère les coursIds de ces catégories
      }

      let coursIdsToFetch = hasCategoriesWithAllCourses ? coursIdsFromCategories : coursIds; // Sinon, juste les cours spécifiés

      /* Pas de cours dans catégories ni de cours spécifique, on prend au niveau de la matière */
      if (!coursIdsToFetch) {
        shouldLog && console.log('!coursIdsToFetch');
        // No specific courses, fetch all courses from the UE
        coursIdsToFetch = await CoursService.getCoursIdsInUe(ueIds); // All in UEs
        shouldLog && console.log(`coursesIdsInUEIds=${ueIds}`, coursIdsToFetch);
        let coursesIdsAvailableForUser = await PermissionService.getAllCoursIdsAvailableForUser(user); // ok
        shouldLog && console.log('coursesIdsAvailableForUser(all)', coursesIdsAvailableForUser);
        coursIdsToFetch = coursIdsToFetch?.filter(id => coursesIdsAvailableForUser.includes(id));
        shouldLog && console.log('coursIdsToFetch(filtered)', coursIdsToFetch);
      }

      // Get questions linked to courses
      const questionIdsWithLinkedCours = await QuestionsService.getQuestionsIdsFromCoursIds(coursIdsToFetch);
      shouldLog && console.log('questionIdsWithLinkedCours', questionIdsWithLinkedCours);

      questionIdsAvailable = questionIdsAvailable?.filter(id => questionIdsWithLinkedCours.includes(id));

      questionIdsAvailable = [...new Set(questionIdsAvailable)];

      shouldLog && console.log('questionIdsAvailable', questionIdsAvailable);

      if (onlyPublished) {
        // Get only published questions
        const publishedQuestions = await models.Question.findAll({
          where: {
            id_question: questionIdsAvailable,
            isPublished: true,
            //type: {
            //  [Op.notIn]: AllSchemaQuestionTypesArray, // Exclude schemas exercices
            //}
          },
          attributes: ['id_question'],
          raw: true,
        });

        // Pour les schémas il faut prendre uniquement ceux qui sont auto-générés
        /*
        const publishedSchemasTraining = await models.Question.findAll({
          where: {
            id_question: questionIdsAvailable,
            isPublished: true,
            type: AllSchemaQuestionTypesArray, // Include schemas exercices
            creationType: 'autoSchemaTraining' // Only auto generated schemas
          },
          attributes: ['id_question'],
          raw: true,
        });
        */
        questionIdsAvailable = publishedQuestions?.map(q => q.id_question);
      }

      if (Array.isArray(annees) && annees.length > 0) {
        // Exécution parallèle des appels asynchrones pour obtenir les QCMs
        const parentQcmsPromisesYears = questionIdsAvailable.map(qId => QCMService.getYearOfFirstQcmIdHavingQuestion(qId));
        const parentYears = await Promise.all(parentQcmsPromisesYears);
        const anneesIntArray = annees.map(a => parseInt(a));
        // Filtrage des questionIdsAvailable basé sur l'année du QCM parent
        questionIdsAvailable = questionIdsAvailable.filter((qId, index) => {
          const parentYear = parentYears[index];
          // Si question na pas de parent serie, il faut la prendre
          return parentYear === undefined || anneesIntArray.includes(parentYear);
        });
      }

      return [...new Set(questionIdsAvailable)];
    } catch (e) {
      console.error('getQuestionsIdsFromSettings');
      console.error(e);
    }
  },


  /**
   * Exercise generation preview
   *
   * @param settings
   * @param userId
   * @returns {Promise<{exercisesCount: number, questionIds: unknown[] | undefined, doneQuestionsIds: *[]}>}
   */
  generateQcmPreview: async (settings, userId) => {
    try {
      const result = await QCMGeneratorService.generateQcmWithParams(settings, userId);
      return {
        exercisesCount: result?.exercisesCount,
        questionIds: result?.questions?.map(q => q.id_question),
        doneQuestionsIds: [], // TODO recup questions faites
      };
    } catch (e) {
      console.error(e);
    }
  },

  getQuestionsFromAdvancesGeneratorPacks: async (settings, userId) => {
    try {
      let questionsGenerated = [];
      let uniqueQuestionIds = new Set();
      let totalQuestionsNeeded = settings.reduce((total, setting) => total + parseInt(setting.nombreQuestions), 0);

      const maxAttempts = 20; // Nombre maximal de tentatives pour trouver des questions supplémentaires

      // Boucle sur les paramètres de configuration pour générer les questions
      for (let i = 0; i < settings.length; i++) {
        const result = await QCMGeneratorService.generateQcmWithParams(settings[i], userId);
        // Ajoute uniquement les questions uniques jusqu'à atteindre le nombre total requis
        result?.questions.forEach(question => {
          if (!uniqueQuestionIds.has(question.id_question) && questionsGenerated.length < totalQuestionsNeeded) {
            uniqueQuestionIds.add(question.id_question);
            questionsGenerated.push(question);
          }
        });
      }

      // Générer des questions supplémentaires si nécessaire, avec une limite de tentatives
      let attempts = 0;
      while (questionsGenerated.length < totalQuestionsNeeded && attempts < maxAttempts) {
        console.log('chevauchement de questions, génération de questions supplémentaires');
        const randomSetting = settings[Math.floor(Math.random() * settings.length)];
        const additionalResult = await QCMGeneratorService.generateQcmWithParams(randomSetting, userId);
        // Ajouter les questions supplémentaires uniques jusqu'à atteindre le nombre total requis
        additionalResult?.questions.forEach(question => {
          if (!uniqueQuestionIds.has(question.id_question) && questionsGenerated.length < totalQuestionsNeeded) {
            uniqueQuestionIds.add(question.id_question);
            questionsGenerated.push(question);
            console.log('question supplémentaire ajoutée');
          }
        });
        attempts++;
      }

      return questionsGenerated;
    } catch (e) {
      console.error(e);
    }
  },
  /**
   * Generate exercise with params packs advanced
   *
   * @param settings
   * @param userId
   * @returns {Promise<void>}
   */
  generateQcmWithParamsPacksAdvanced: async (settings, userId) => {
    try {
      let secondsPerExercise = settings[0]?.secondsPerExercise;
      let isAnswerOrderRandom = settings[0]?.isAnswerOrderRandom;
      let hasTimer = settings[0]?.hasTimer;

      const packs = settings;
      // Générer la session
      const qcmSession = await models.QcmSession.create({
        settings: {
          module: 'packs',
          packs,
          infiniteQuestionByQuestion: false,
        },
        userId,
      });

      const questionsGenerated = await QCMGeneratorService.getQuestionsFromAdvancesGeneratorPacks(settings, userId);

      return {
        questions: questionsGenerated,
        isAnswerOrderRandom: isAnswerOrderRandom,
        qcmSessionId: qcmSession.id, // À voir quand 1 par 1 stabilisé
        secondsPerExercise,
        hasTimer,
      };
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },

  /*
  * Returns questions for MCQ GENERATOR
  *
  * @desc: generate a new QCM by params
  * @param: {Object} qcmParams
  * @param: {number} userId making the request
  * @return: {Object} questionsGenerated
  * */
  generateQcmWithParams: async (settings, userId) => {
    try {
      let {
        ueIds,
        categoriesIds = null,
        coursIds = null,
        typeQcms, // exercices type IDS
        // Years
        annees,
        isQuestionOrderRandom,
        isAnswerOrderRandom = false, // Front end only
        nombreQuestions,
        infiniteQuestionByQuestion = false, // Ask questions until no more
        /* Timer settings */
        secondsPerExercise,
        hasTimer = false,
        goToNextQuestionOnTimerEnd = false,
        hasTimerPerQuestion = true,

        createQcmSession = true,
      } = settings;

      let debug = false;

      if (typeQcms.length === 0) {
        throw new GraphQLError('Vous devez sélectionner au moins un type');
      }
      if (nombreQuestions < 1 || nombreQuestions > 100) {
        throw new GraphQLError('Le nombre de questions désirées doit être compris entre 1 et 100');
      }

      let qcmSession;
      let qcmSessionId = null;
      let questionsGenerated = [];
      let uniqueQuestions = [];
      let exercisesCount = 0;

      const qIdsFromSettings = await QCMGeneratorService.getQuestionsIdsFromSettings(settings, { id: userId });
      settings = { ...settings, countQuestionsAvailable: qIdsFromSettings.length, infiniteQuestionByQuestion }; // Add questions available count
      if (settings?.countQuestionsAvailable === 0) {
        throw new GraphQLError('Aucun exercice trouvé avec ces réglages, veuillez changer les paramètres');
      }

      if (infiniteQuestionByQuestion || createQcmSession) {
        qcmSession = await models.QcmSession.create({
          settings, // save les settings pour recalculer les questions si besoin
          userId,
          remainingQuestionsIds: qIdsFromSettings, // Stocke les id pour pas avoir besoin de recalculer
        });
        qcmSessionId = qcmSession?.id;
      } else {
        // We want to return all questions to client
        uniqueQuestions = await models.Question.findAll({
          where: {
            id_question: qIdsFromSettings,
            isPublished: true,
          },
          order: sequelize.random(),
          limit: parseInt(nombreQuestions),
        });
      }

      exercisesCount = qIdsFromSettings.length;

      return {
        exercisesCount,
        questions: uniqueQuestions, // Dans le cas où on veut tout retourner (pas de création de session)
        isAnswerOrderRandom,
        qcmSessionId,
        secondsPerExercise,
        hasTimer,
      };
    } catch (e) {
      //console.error('qcm generator error');
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },
  corrigerQcmGenerated: async ({ questions, reponses }, userId) => {
    try {
      if (!reponses) {
        let errorMsg = 'reponses introuvables';
        throw errorMsg;
      }
      const questionsIds = questions.map(q => q.id_question);
      const questionsFromDb = await models.Question.findAll({
        where: {
          id_question: questionsIds,
        },
        order: [EXOQUALIZE.ORDER_BY_DEFINED_ORDER],
        include: [models.QuestionAnswers, { model: models.McqScale, as: 'mcqScale' }],
      });

      const {
        reponsesUtilisateur,
        note,
        reponsesJustes,
        totalPonderatedGrade,
        maxPoints,
      } = await QCMService.calculNoteQcm(questionsFromDb, reponses);

      /* Create session so it does not mix results */
      const session = await models.QcmSession.create({
        userId,
        isFinished: true,
        questionsIdsDone: questionsIds,
      });

      // qcmSessionId: sessionId, // Current MCQ session

      /* Create user answers and statsQuestionAnswers */
      for (const rep of reponsesUtilisateur) {
        let jsonAnswers = null; // Schema answer data
        if (rep.jsonAnswers && Array.isArray(rep.jsonAnswers)) {
          jsonAnswers = rep.jsonAnswers;
        }
        await models.QcmStatsQuestion.create({
          qcmSessionId: session.id, // Current MCQ session
          id_question: rep.id_question, // Question id
          id_utilisateur: userId, // User id
          pointsObtained: rep.pointsObtained, // Points obtained
          ponderatedPointsObtained: rep.ponderatedPointsObtained, // Ponderated points obtained
          qcmStatsQuestion: rep.qcmStatsQuestion, // Question stats (new way)
          jsonAnswers,
        }, {
          include: [{
            model: models.StatsQuestionAnswers,
            as: 'qcmStatsQuestion',
          }],
        });
      }

      return { note, maxPoints };
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },

  /******************************************/
  /* NEW MODULES ENTRAINEMENT COURS GENERATEUR */
  /******************************************/

  /* Module entraînement du cours (Annales par exemple). Type à chercher est défini dans settings du cours */
  //TODO delete and replace by qcmSercice.revisionTips after mobile app update
  getExercicesForCourseTraining: async (coursId, me, returnType = 'count', coursModuleType) => {
    try {
      const cours = await models.Cours.findByPk(coursId);
      const {
        questionsIds,
        allQuestionsCount,
        countUndoneExercises,
      } = await QCMGeneratorService.revisionTips({ coursId }, me, returnType, CoursTypesQcmSettings_MODULE_TYPES.training);

      // Keep compatibility with actual mobile app
      return {
        cours,
        questionIds: questionsIds,
        questionsCount: allQuestionsCount,
        countUndoneExercises,
      };
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },

  /**
   * Returns revision tips for Revision module (look for exercices done and undone)
   *
   * @param filter
   * @param me
   * @param returnType
   * @param coursModuleType string
   * @param schemaAllOrCustom
   * @param schemaVariants
   * @returns {Promise<{allQuestionsCount, questionsAnswered, questions: awaited Promise<Model[]> | Bluebird<TInstance[]>}>}
   */
  async revisionTips(filter, { me }, returnType, coursModuleType, schemaAllOrCustom = 'all', schemaVariants = []) {
    try {
      //coursModuleType: {CoursTypesQcmSettings_MODULE_TYPES.revision | CoursTypesQcmSettings_MODULE_TYPES.training | CoursTypesQcmSettings_MODULE_TYPES.schemas}
      /*
       filter:
        coursId: ID   - id cours
        notionId: ID  -
      */

      const coursId = filter?.coursId;
      const notionId = filter?.notionId;
      // get questions faites par user
      //TODO cache
      const statsq = await models.QcmStatsQuestion.findAll({
        where: {
          id_utilisateur: me.id,
        },
        attributes: ['id_question'],
        raw: true,
        nest: true,
      });

      // Questions ids done by user
      const qIdsDoneByUser = statsq?.map(s => s.id_question);

      let debug = false;

      if (filter?.coursId) {
        // Questions not done by user
        // récupération Cours-based type permission
        const typeQcmIds = await PermissionService.CoursTypeQcmSettings.getTypeqcmIdsForCoursModule(
          { coursId, coursModuleType: coursModuleType },
        );
        debug && console.log({ typeQcmIdsallowed: typeQcmIds });
        // Voir c'est quoi les types que l'utilisateur a le droit de voir de base
        const userTypesAllowed = await PermissionService.getAvailableTypeQcmsForUser(me);
        debug && console.log({ userTypesAllowed: userTypesAllowed.map(t => t.name) });
        const typeQcmIdsAuthorized = userTypesAllowed.map(q => q.id);
        debug && console.log({ typeQcmIdsAuthorized });
        // Prend que les types qui sont dans les types autorisés parmi ceux proposé dans les settings du module du cours
        const typeQcmAllowed = typeQcmIds.filter(q => typeQcmIdsAuthorized.includes(q));
        debug && console.log({ typeQcmAllowed });
        const questionsWithTypeQcmId = await models.QuestionTypeQcm.findAll({
          where: {
            typeQcmId: typeQcmAllowed,
          },
          attributes: ['questionId'],
        });
        // Questions autorisées d'après les settings du module du cours ET les types qcm autorisés par l'utilisateur
        const questionIdsWithTypeQcmAllowed = questionsWithTypeQcmId.map(q => q.questionId);
        debug && console.log({ questionIdsWithTypeQcmAllowed });
        const questionIdsHavingCours = await QuestionsService.getQuestionsIdsFromCoursIds(coursId);
        debug && console.log({ questionIdsHavingCours });
        const questionIdsMatchingFilters = questionIdsHavingCours.filter(q => questionIdsWithTypeQcmAllowed?.includes(q));
        debug && console.log({ questionIdsMatchingFilters });
        let allQuestions = [], allQuestionsCount = 0;

        let publishedQuestionIdsMatchingFilters = [];

        let schemaImagesPreview = [];

        let additionnalWhere = {};
        if (coursModuleType === CoursTypesQcmSettings_MODULE_TYPES.schemas) {
          // Prendre seulement schémas auto générés
          additionnalWhere = { creationType: 'autoSchemaTraining' };
          if(schemaAllOrCustom === 'custom') {
            // Mode custom, regarder les schemaVariants
            additionnalWhere.type = schemaVariants; // [fill in the blanks, point and click]
          }
        }

        // DEFINITIONS DE TOUTES QUESTIONS, Y COMPRIS NON-FAITES
        if (returnType === 'count') { // preview
          allQuestionsCount = await models.Question.count({
            where: {
              id_question: questionIdsMatchingFilters,
              isPublished: true,
              ...additionnalWhere,
            },
          });

          // Pour schéma, cherche petites images pour le front
          if (coursModuleType === CoursTypesQcmSettings_MODULE_TYPES.schemas) {
            const someExercises = await models.Question.findAll({
              where: {
                id_question: questionIdsMatchingFilters,
                ...additionnalWhere,
                isPublished: true,
              },
              limit: 60, // 60 premiers
              raw: true,
              attributes: ['id_question', 'schemaLibraryId'],
            });
            const schemaLibraryIds = someExercises.map(q => q.schemaLibraryId);
            const schemaLibraries = await models.SchemaLibrary.findAll({
              where: {
                id: schemaLibraryIds,
              },
              raw: true,
              attributes: ['id', 'image'],
            });
            schemaImagesPreview = schemaLibraries?.map(s => s.image);
          }
          /////////////////////////////////////////////////////////////////////
        } else if (returnType === 'questionIds') {
          publishedQuestionIdsMatchingFilters = await models.Question.findAll({
            where: {
              id_question: questionIdsMatchingFilters,
              isPublished: true,
              ...additionnalWhere,
            },
            raw: true,
            attributes: ['id_question'],
          });
          publishedQuestionIdsMatchingFilters = publishedQuestionIdsMatchingFilters.map(q => q.id_question);
          allQuestionsCount = publishedQuestionIdsMatchingFilters.length;
        } else {
          allQuestions = await models.Question.findAll({
            where: {
              id_question: questionIdsMatchingFilters,
              isPublished: true,
              ...additionnalWhere,
            },
            required: true,
            include: [
              {
                model: models.QuestionAnswers,
                separate: true,
                required: true,
              },
            ],
          });
        }

        // Toutes les questions en enlevant celles faites par l'utilisateur
        const questionIdsUndone = questionIdsMatchingFilters.filter(q => !qIdsDoneByUser?.includes(q));
        // Questions pas encore faites

        // prend seulement celles qui sont allowed
        const questionIdsToLookFor = questionIdsUndone.filter(q => questionIdsWithTypeQcmAllowed.includes(q));

        // garder celles qui ne sont pas faites et qui ont le(s) cours autorisés
        const questionsIdsUndoneWithFilters = questionIdsToLookFor.filter(q => questionIdsHavingCours.includes(q));

        let questions = [], countQuestionsIdsUndoneWithFilters = 0;

        let questionsIds = [];


        // DEFINITION DES QUESTIONS NON-FAITES
        if (returnType === 'count') {
          // COUNT ONLY
          countQuestionsIdsUndoneWithFilters = await models.Question.count({
            where: {
              id_question: questionsIdsUndoneWithFilters,
              isPublished: true,
              ...additionnalWhere,
            },
          });
        } else if (returnType === 'questionIds') {
          // QUESTION IDS : non faites et publiées
          const publishedUndoneQuestionIdsMatchingFilters = await models.Question.findAll({
            where: {
              id_question: questionsIdsUndoneWithFilters,
              isPublished: true,
              ...additionnalWhere,
            },
            raw: true,
            attributes: ['id_question'],
          });
          questionsIds = publishedUndoneQuestionIdsMatchingFilters.map(q => q.id_question);
          countQuestionsIdsUndoneWithFilters = questionsIds.length;
        } else {
          // FULL QUESTIONS
          questions = await models.Question.findAll({
            where: {
              id_question: questionsIdsUndoneWithFilters,
              isPublished: true,
              ...additionnalWhere,
            },
            required: true,
            include: [
              {
                model: models.QuestionAnswers,
                separate: true,
                required: true,
              },
            ],
          });
        }

        return {
          questionsAnswered: statsq.length,
          questions: questions, // questions not done yet
          allQuestions: allQuestions, // all questions

          allQuestionsCount, // total questions count
          countUndoneExercises: countQuestionsIdsUndoneWithFilters, // undone questions count

          questionsIds, // Question ids undone
          questionIdsMatchingFilters: publishedQuestionIdsMatchingFilters, // all questions ids matching filters
          schemaImagesPreview,
        };
      }

      //TODO faire pareil que pour cours pour notion

      ///////////////////////////////////
      // FOR NOTION
      ///////////////////////////////////
      if (filter?.notionId) {
        // TODO voir si permission par type qcm est suffisante
        const availableQuestionsIds = await PermissionService.getAvailableQuestionsIdsByTypeQcmForUser({ id: me?.id });

        // Récupérer les answers ids
        const notionsQuestionAnswers = await models.NotionQuestionAnswers.findAll({
          where: {
            notionId: filter?.notionId,
          },
          attributes: ['answerId'],
        });
        const answersIdsOfThisNotion = notionsQuestionAnswers.map(nqa => nqa.answerId);

        // Récupérer les questions issus des answers
        const questionsFromAnswers = await models.QuestionAnswers.findAll({
          where: {
            id: answersIdsOfThisNotion,
          },
          attributes: ['questionId'],
        });
        const questionsIdsFromAnswers = questionsFromAnswers.map(q => q.questionId);

        // Récupérer les question ids
        const notionsQuestion = await models.NotionQuestion.findAll({
          where: {
            notionId: filter?.notionId,
          },
          attributes: ['questionId'],
        });
        const questionIdsOfThisNotion = notionsQuestion.map(nqa => nqa.questionId);

        const allQuestionsIds = [...questionsIdsFromAnswers, ...questionIdsOfThisNotion];
        // Remove allQuestionsIds duplicates
        const allQuestionsIdsUnique = [...new Set(allQuestionsIds)];

        // Récupérer les questions avec les bons droits
        const questionIdsWithNotion = allQuestionsIdsUnique.filter(q => availableQuestionsIds.includes(q));

        const allQuestions = await models.Question.findAll({
          where: {
            id_question: questionIdsWithNotion,
          },
          required: true,
          include: [
            {
              model: models.QuestionAnswers,
              separate: true,
              required: true,
            },
          ],
        });

        const questionsNotDone = await models.Question.findAll({
          where: {
            id_question: {
              [Op.notIn]: qIdsDoneByUser,
            },
          },
          raw: 'true',
          attributes: ['id_question'],
        });
        const questionIdsNotDone = questionsNotDone.map(q => q.id_question);
        // prend seulemenet celles qui sont allowed
        const questionIdsToLookFor = questionIdsNotDone.filter(q => availableQuestionsIds.includes(q));

        // Questions pas encore faites avec les bons droits ET qui ont la notion concernée
        const questionIdsWithNotionAndNotDone = questionIdsToLookFor.filter(q => questionIdsWithNotion.includes(q));

        // Questions restantes de révision
        const questions = await models.Question.findAll({
          where: {
            id_question: questionIdsWithNotionAndNotDone,
          },
          required: true,
          include: [
            {
              model: models.QuestionAnswers,
              separate: true,
              required: true,
            },
          ],
        });

        return {
          questionsAnswered: statsq?.length,
          allQuestionsCount: allQuestions?.length, // total questions
          questions: questions, // questions not done yet
          allQuestions,
        };

      }

      // Module accès rapide (générateur)
      if (filter?.moduleQuickAccessId) {
        const quickAccessModule = await models.ModulesQuickAccess.findByPk(filter?.moduleQuickAccessId);
        const settings = quickAccessModule?.settings;
        // Questions ids à prendre en compte seulement pour le mode liste
        let questionIds = await QCMGeneratorService.getQuestionsIdsFromSettings(settings, { id: me.id });
        if (!settings?.infiniteQuestionByQuestion) {
          const limit = parseInt(settings?.nombreQuestions);
          const questions = await models.Question.findAll({
            where: {
              id_question: questionIds,
            },
            // Order by rand
            order: sequelize.random(),
            limit,
          });
          // limit questionIds
          questionIds = questions.map(q => q.id_question);
        }
        // Parmi ces questionsIds regarder celles qui sont faites
        const questionsDone = await models.QcmStatsQuestion.findAll({
          where: {
            id_question: questionIds,
            id_utilisateur: me.id,
          },
          attributes: ['id_question'],
        });
        const uniqueDoneQIds = [...new Set(questionsDone?.map(q => q.id_question))];
        const countQuestionsDone = uniqueDoneQIds?.length;
        let allQuestionsCount = 0, countUndoneExercises = 0;
        allQuestionsCount = questionIds?.length;
        countUndoneExercises = allQuestionsCount - countQuestionsDone;
        return {
          questionsAnswered: statsq.length, // inutile
          allQuestionsCount, // total questions count
          countUndoneExercises, // undone questions count
        };
      }
    } catch (e) {
      console.error(e);
    }
  },


  // LEGACY TO REMOVE AFTER MOBILE APP UPDATE
  getQuestionsAnnaleForCours: async (coursId, userId) => {
    try {
      const shouldLog = false;

      // récupération Cours-based type permission
      const typeQcmIds = await PermissionService.CoursTypeQcmSettings.getTypeqcmIdsForCoursModule(
        { coursId, coursModuleType: CoursTypesQcmSettings_MODULE_TYPES.training },
      );
      //const qcmIds = await QcmTypeService.getQcmIdsInType(typeQcmIds);
      const cours = await models.Cours.findByPk(coursId);
      const user = await models.User.findByPk(userId);
      //const idsMcqAvailable = await PermissionService.getAvailableMcqIdsForUser(user);
      // Prendre que ceux qui sont dans les idsMcqAvailable
      //const idsToLookFor = qcmIds.filter(id => idsMcqAvailable.includes(id));
      // Voir c'est quoi les types que l'utilisateur a le droit de voir de base
      const userTypesAllowed = await PermissionService.getAvailableTypeQcmsForUser(user);
      const typeQcmIdsAuthorized = userTypesAllowed.map(q => q.id);
      // Prend que les types qui sont dans les types autorisés parmi ceux proposé dans les settings du module du cours
      const typeQcmAllowed = typeQcmIds.filter(q => typeQcmIdsAuthorized.includes(q));
      const questionsWithTypeQcmId = await models.QuestionTypeQcm.findAll({
        where: {
          typeQcmId: typeQcmAllowed,
        },
        attributes: ['questionId'],
      });
      const questionIdsWithTypeQcmAllowed = questionsWithTypeQcmId.map(q => q.questionId);
      const questionIdsForCours = await QuestionsService.getQuestionsIdsFromCoursIds(coursId);
      const questionIdsToLookFor = questionIdsForCours.filter(q => questionIdsWithTypeQcmAllowed?.includes(q));

      let questions = await models.Question.findAll({
        where: {
          id_question: questionIdsToLookFor,
          isPublished: true,
        },
      });

      shouldLog && console.log('questionsAnnales forCoursId=', { coursId }, ` questionIdsToLookFor.length=${questionIdsToLookFor?.length}`, `
      (published questions found).length=${questions?.length}`);
      for (const question of questions) {
        question.qcm = await QCMService.getFirstQcmIdHavingQuestion(question.id_question);
        question.annee = question?.qcm?.annee || null;
        question.id_qcm = question?.qcm?.id_qcm;
      }

      return {
        cours,
        questions,
      };
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },


  /**
   * Mutation generate questions annales for cours
   *
   * mode: oneByOne | allAtOnce
   * module: training | revision
   * @param coursId
   * @param notionId
   * @param includeAlreadyDone
   * @param module
   * @param mode
   * @param numberOfExercises
   * @param schemaAllOrCustom
   * @param schemaVariants
   * @param ctx
   * @returns {Promise<Awaited<undefined>>}
   */
  async generateExerciseSessionForCourseModule(
    {
      coursId,
      notionId,
      includeAlreadyDone,
      module = CoursTypesQcmSettings_MODULE_TYPES.training,
      mode = 'oneByOne',
      numberOfExercises,
      schemaAllOrCustom, // string
      schemaVariants //[String]
    }, ctx) {
    try {
      const {
        allQuestionsCount,
        countUndoneExercises,

        questionsIds, // undone exercises
        questionIdsMatchingFilters, // all exercises matching filters
      } = await QCMGeneratorService.revisionTips({ coursId, notionId }, ctx, 'questionIds', module);

      let countQuestionsAvailable = allQuestionsCount;
      let remainingQuestionsIds = includeAlreadyDone ? questionIdsMatchingFilters : questionsIds;
      if (!includeAlreadyDone) {
        countQuestionsAvailable = countUndoneExercises;
      }

      const infiniteQuestionByQuestion = mode === 'oneByOne';
      const additional = !infiniteQuestionByQuestion ? {
        nombreQuestions: numberOfExercises, // pour mode all at once
        schemaAllOrCustom,
        schemaVariants
      } : {};
      // Generate session
      const session = await models.QcmSession.create({
        settings: {
          coursId: coursId,
          countQuestionsAvailable: countQuestionsAvailable,
          infiniteQuestionByQuestion,
          module,
          includeAlreadyDone,
          ...additional,
        },
        remainingQuestionsIds, // Stocke les id pour pas avoir besoin de recalculer
        userId: ctx.me.id,
      });

      return {
        qcmSessionId: session.id,
      };

    } catch (e) {
      console.error(e);
      throw new GraphQLError(e.message);
    }
  },

  async generateSessionForDiapoSynthese(formationElementId, ctx) {
    try {
      const element = await models.FormationElement.findByPk(formationElementId);
      const mcqId = element?.mcqId; // Serie diapo synthese
      const questionsIds = await QuestionsService.getQuestionsIdsForMcq(mcqId);
      // Generate session
      const session = await models.QcmSession.create({
        formationElementId,
        settings: {
          countQuestionsAvailable: questionsIds?.length,
          infiniteQuestionByQuestion: true, // generator compatible even if we have only 1 question
          nombreQuestions: questionsIds?.length,
          singleQuestionMode: true,
        },
        remainingQuestionsIds: questionsIds,
        userId: ctx.me.id,
      });
      return {
        qcmSessionId: session.id,
      };
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },

  async generateSessionFromExerciseElement(formationElementId, ctx) {
    try {
      const element = await models.FormationElement.findByPk(formationElementId);
      const questionId = element?.doQuestionId;

      //TODO voir possibles settings à recup sur element: hasTimer, isAnswerOrderRandom, secondsPerExercise

      // Generate session
      const session = await models.QcmSession.create({
        formationElementId,
        settings: {
          countQuestionsAvailable: 1,
          infiniteQuestionByQuestion: true, // generator compatible even if we have only 1 question
          nombreQuestions: 1,
          singleQuestionMode: true,
        },
        remainingQuestionsIds: [questionId],
        userId: ctx.me.id,
      });

      return {
        qcmSessionId: session.id,
      };
    } catch (e) {
      console.error(e);
      throw new GraphQLError(e);
    }
  },
};