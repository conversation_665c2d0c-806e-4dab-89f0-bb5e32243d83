import { Op } from 'sequelize';
import uuidv4 from 'uuid/v4.js';
import models from '../../models';
import { CONFIG_KEYS } from '../../models/config';
import { RegisterFieldsNames } from '../../models/formation/formation_element.js';
import { QuestionAnswerType } from '../qcm/qcm-service';
import { SchemaLibraryService } from '../schema-library/schema-library-service';


const DEFAULT_DYNAMIC_RULES = {
  minimumGrade: null,
  numberOfErrors: null,
  pointsPerQuestion: 1,
  notation: 'default',
  pointsLostPerError: [-0.5, -0.3, -0.20, 0],
  pointsLostPerErrorIfTrueAnswerIsCheckedFalse: [-0.5, -0.3, -0.20, 0],
  pointsLostPerErrorIfFalseAnswerIsCheckedTrue: [-0.5, -0.3, -0.20, 0],
  pointsLostPerErrorIfFalseAnswerIsUndefined: [-0.5, -0.3, -0.20, 0],
  pointsLostPerErrorIfTrueAnswerIsUndefined: [-0.5, -0.3, -0.20, 0],
  pointsLostPerErrorIfTrueAnswerIsCheckedFalseSettings: 'fixed',
  pointsLostPerErrorIfFalseAnswerIsCheckedTrueSettings: 'fixed',
  pointsLostPerErrorIfFalseAnswerIsUndefinedSettings: 'fixed',
  pointsLostPerErrorIfTrueAnswerIsUndefinedSettings: 'fixed',
};

export const ConfigUpgrades = {

  async addDefaultScaleForFillTheBlanks() {
    const scale = {
      name: 'Texte à trous',
      type: 'dynamic',
      isDefault: false,
      questionType: 'fillintheblanks',
      pointsObtainedWhenNothingChecked: 0,
      rules: DEFAULT_DYNAMIC_RULES,
    };
    scale.rules = JSON.stringify(scale.rules);
    await models.McqScale.create(scale);
  },
  async addDefaultScaleForSchema() {
    const scale = {
      name: 'Schemas par défaut',
      type: 'dynamic',
      isDefault: false,
      questionType: 'schema',
      pointsObtainedWhenNothingChecked: 0,
      rules: DEFAULT_DYNAMIC_RULES,
    };
    scale.rules = JSON.stringify(scale.rules);
    await models.McqScale.create(scale);
  },

  async addDefaultScaleForSchemaFillInTheBlanks() {
    const scale = {
      name: 'Schema à compléter',
      type: 'dynamic',
      isDefault: false,
      questionType: 'schemaFillInLegends',
      pointsObtainedWhenNothingChecked: 0,
      rules: DEFAULT_DYNAMIC_RULES,
    };
    scale.rules = JSON.stringify(scale.rules);
    await models.McqScale.create(scale);
  },

  async addMissingFormUserCompletions() {
    // Crée les lignes manquantes dans FormsUserCompletion pour les users qui ont déjà rempli des formulaires, avant l'ajout de steps
    const userResults = await models.UserPropertiesData.findAll();
    let addedFormIds = [];
    let addedUserIds = [];
    for (const result of userResults) {
      const formId = result.formId;
      const userId = result.userId;
      if (formId && userId) {
        if (!addedFormIds.includes(formId) || !addedUserIds.includes(userId)) {
          addedFormIds.push(formId);
          addedUserIds.push(userId);
          await models.FormsUserCompletion.create({
            formId,
            userId,
            complete: true,
          });
        }
      }
    }
  },

  async createDefaultUUIDForms() {
    const allForms = await models.Forms.findAll();
    for (const form of allForms) {
      form.uuid = uuidv4();
      await form.save();
    }
  },

  /* Before multi AI user */
  async createDefaultNewChatGPTIntegrationIfAny() {
    const existingChatGPTConfig = await models.Config.findAll({
      where: {
        key: CONFIG_KEYS.CHAT_GPT_API_KEY,
      },
    });
    let integrationId = null;
    if (existingChatGPTConfig?.length > 0) {
      for (const config of existingChatGPTConfig) {
        const value = config?.value;
        // Create only existing api keys
        if (value) {
          // Create new
          let domainName = config?.domain;
          if (domainName === null) {
            domainName = 'par défaut';
          }

          const integ = await models.Config.create({
            key: CONFIG_KEYS.CHAT_GPT_INTEGRATION,
            value: JSON.stringify({
              name: 'ChatGPT - ' + domainName,
              accessToken: value,
            }),
            domain: config?.domain,
          });
          if (config?.domain === null) {
            integrationId = integ?.id;
          }
        }
      }
    }
    const existingChatGPTSettings = await models.Config.findAll({ where: { key: CONFIG_KEYS.CHAT_GPT_SETTINGS } });
    if (existingChatGPTSettings?.length > 0) {
      for (const c of existingChatGPTSettings) {
        const value = c?.value;
        if (value) {
          let parsedValue = JSON.parse(value);
          const domain = c?.domain;
          const user = await models.User.findOne({
            where: {
              bot: true,
            },
          });
          // Add correct user ID and user name to existing c
          if (user) {
            parsedValue = {
              ...parsedValue,
              aiUserId: user.id,
              integrationId,
            };
          }
          c.value = JSON.stringify(parsedValue);
          await c.save();
        }
      }
    }
  },

  async addNewConfigForDefaultForfaitsConfig() {
    // Recup les domaines
    const allConfigs = await models.Config.findAll();
    const allDomains = allConfigs.map(c => c.domain);
    const uniqueDomains = [...new Set(allDomains)];
    for (const domain of uniqueDomains) {
      const newConf = await models.Config.create({
        key: CONFIG_KEYS.REGISTER_PAGE_SELECTED_FORFAITS,
        value: JSON.stringify([]), // Par défaut, rien
        domain,
      });
    }
    return true;
  },

  async addNewAvatarListGlobalConfig() {
    try {
      const allConfigs = await models.Config.findAll();
      const allDomains = allConfigs.map(c => c.domain);
      const uniqueDomains = [...new Set(allDomains)];
      for (const domain of uniqueDomains) {
        const newConf = await models.Config.create({
          key: CONFIG_KEYS.DEFAULT_AVATARS,
          value: JSON.stringify([]), // Par défaut, rien
          domain,
        });
      }
      return true;
    } catch (e) {
      console.error(e);
    }
  },

  async createRegisterFieldsElementsForfaits() {
    const forfaits = await models.Forfait.findAll();
    // Enable => si pas présent non ajouté
    // mandatory => element settings: "{\"isMandatory\":true}"

    const createLink = async (forfaitId, elementId) => {
      // Create link between forfait and element
      return await models.FormationElementForfaits.create({
        forfaitId,
        elementId,
      });
    };

    const createElement = async (settings, order) => {
      return await models.FormationElement.create({
        type: 'registerField',
        settings,
        order,
      });
    };

    for (const forfait of forfaits) {
      const registerFields = forfait?.paymentSettings?.registerFields;
      // Default fields mandatory not deletable
      let usernameElem = await createElement({
        isMandatory: true,
        type: RegisterFieldsNames.USERNAME,
      }, 1);
      await createLink(forfait.id, usernameElem.id);
      let emailElem = await createElement({
        isMandatory: true,
        type: RegisterFieldsNames.EMAIL,
      }, 2);
      await createLink(forfait.id, emailElem.id);
      let passwordElem = await createElement({
        isMandatory: true,
        type: RegisterFieldsNames.PASSWORD,
      }, 3);
      await createLink(forfait.id, passwordElem.id);

      if (registerFields) {
        // Others optionnal
        if (registerFields?.name?.enable) {
          const newElement = await createElement({
            isMandatory: registerFields?.name?.mandatory,
            type: RegisterFieldsNames.NAME,
          }, 4);
          await createLink(forfait.id, newElement.id);
        }
        if (registerFields?.firstName?.enable) {
          const newElement = await createElement({
            isMandatory: registerFields?.firstName?.mandatory,
            type: RegisterFieldsNames.FIRST_NAME,
          }, 5);
          await createLink(forfait.id, newElement.id);
        }
        if (registerFields?.addressline1?.enable) {
          const newElement = await createElement({
            isMandatory: registerFields?.addressline1?.mandatory,
            type: RegisterFieldsNames.ADDRESS,
          }, 6);
          await createLink(forfait.id, newElement.id);
        }
        if (registerFields?.postcode?.enable) {
          const newElement = await createElement({
            isMandatory: registerFields?.postcode?.mandatory,
            type: RegisterFieldsNames.POSTCODE,
          }, 7);
          await createLink(forfait.id, newElement.id);
        }
        if (registerFields?.city?.enable) {
          const newElement = await createElement({
            isMandatory: registerFields?.city?.mandatory,
            type: RegisterFieldsNames.CITY,
          }, 8);
          await createLink(forfait.id, newElement.id);
        }
        if (registerFields?.phone?.enable) {
          const newElement = await createElement({
            isMandatory: registerFields?.phone?.mandatory,
            type: RegisterFieldsNames.PHONE,
          }, 9);
          await createLink(forfait.id, newElement.id);
        }

      }
    }
  },

  async migrateForfaitsElementsToProxyForfaitElements() {
    // Créer élément proxy pour chaque élément forfait lié en dur
    // Supprimer ou écraser ancien lien forfait - élément vers le nouvel element
    let forfaitsElements = await models.FormationElementForfaits.findAll(); // elementId, forfaitId
    for (let forfaitElement of forfaitsElements) {
      const targetElement = await models.FormationElement.findByPk(forfaitElement.elementId);
      const newElementProxy = await models.FormationElement.create({
        type: 'importedInput',
        objectId: forfaitElement.elementId,
        settings: {},
        order: targetElement.order, // Keep order
      });

      // Update link
      await models.FormationElementForfaits.update({
        elementId: newElementProxy.id,
      }, {
        where: { elementId: forfaitElement.elementId, forfaitId: forfaitElement.forfaitId },
      });

    }

    /*
        "input": {
        "objectId": "10324",
        "mcqId": null,
        "questionId": null,
        "footerQuestionId": null,
        "headerMcqId": null,
        "forfaitId": null,
        "userPropertyFolderId": null,
        "settings": {},
        "coursSupportId": null,
        "eventId": null,
        "challengeId": null,
        "titleId": null,
        "globalAnnounceType": null,
        "type": "importedInput",
        "coursId": null,
        "formId": null
      }
    */

    return true;
  },

  async createDefaultMobileAppConfig() {
    try {
      // Recup les domaines
      const allConfigs = await models.Config.findAll();
      const allDomains = allConfigs.map(c => c.domain);
      const uniqueDomains = [...new Set(allDomains)];
      for (const domain of uniqueDomains) {
        const newConf = await models.Config.create({
          key: CONFIG_KEYS.CUSTOM_MOBILE_CONFIG,
          value: JSON.stringify({
            type: 'classic',
          }),
          domain,
        });
      }
      return true;
    } catch (e) {
      console.error(e);
    }
  },

  // 2 options: soit on créé tout et on update quand on update schemaLib
  // Soit créé à la volée si existe pas
  async createDefaultSchemaExercicesPointAndClick() {
    try {
      console.log('creating default schema point and click exercises training...');
      const allSchemaLibrary = await models.SchemaLibrary.findAll();
      for (const schema of allSchemaLibrary) {
        await SchemaLibraryService.createAutoSchemaExercise({ schema }, { models, me: null });
      }
      console.log('done creating default schema point and click exercises training !');
      return true;
    } catch (e) {
      console.error(e);
    }
  },


  async populateDefaultSchemaLibraryTypeQcm() {
    try {
      const schemaTypeDefault = await models.TypeQcm.create({
        name: 'Schéma',
        description: 'Type par défaut pour entrainement sur les schémas',
        contentType: 'EXERCISE',
      });
      // Ajouter tous les groupes par défaut
      const typeId = schemaTypeDefault.id;
      const allGroupIds = await models.Groupe.findAll({
        where: {
          isIndividual: false,
        },
        attributes: ['id'],
        raw: true,
      });
      const groupIds = allGroupIds.map(g => g.id);
      for (const groupId of groupIds) {
        await models.TypeQcmGroups.create({
          typeQcmId: typeId,
          groupeId: groupId,
        });
      }
      // Associer schema lib existants à ce nouveau type
      const allSchemaLib = await models.SchemaLibrary.findAll();
      for (const schemaLib of allSchemaLib) {
        await models.SchemaLibraryTypeQcm.create({
          schemaLibraryId: schemaLib.id,
          typeQcmId: typeId,
        });
      }
      return true;
    } catch (e) {
      console.error(e);
    }
  },

  async migrateConfigToGPT_4o() {
    try {
      const configs = await models.Config.findAll({
        where: {
          key: CONFIG_KEYS.CHAT_GPT_SETTINGS,
        },
      });

      for (const config of configs) {
        const value = config?.value;
        if (value) {
          let parsedValue = JSON.parse(value);
          parsedValue.model = 'gpt-4o';
          config.value = JSON.stringify(parsedValue);
          await config.save();
        }
      }
      return true;
    } catch (e) {
      console.error(e);
    }
  },

  async migrateForfaitsChildsToNewSystem() {
    try {
      const forfaitsWithParentIds = await models.Forfait.findAll({
        where: {
          parentId: {
            [Op.ne]: null,
          },
        },
      });
      for (const forfait of forfaitsWithParentIds) {
        const existing = await models.ForfaitsChilds.findOne({
          where: {
            forfaitId: forfait.parentId, // parent
            childId: forfait.id, // current
          },
        });
        if (!existing) {
          await models.ForfaitsChilds.create({
            forfaitId: forfait.parentId,
            childId: forfait.id,
          });
        }
      }
      console.log('done migrating forfaits');
      return true;
    } catch (e) {
      console.error(e);
    }
  },
  async transformForfaitChildToUnique() {
    const legacyChildsFOrfaits = await models.Forfait.findAll({
      where: {
        parentId: {
          [Op.ne]: null,
        },
      },
    });
    for (const forfait of legacyChildsFOrfaits) {
      forfait.type = 'CHILD';
      await forfait.save();
    }
  },

  async alphanumerialOrNumericalMigrateToAlphanumByDefault() {
    const questions = await models.Question.findAll({
      where: {
        type: QuestionAnswerType.ALPHANUMERICAL_OR_NUMERICAL,
      },
    });

    for (const question of questions) {
      question.type = QuestionAnswerType.ALPHANUMERICAL;
      await question.save();
    }

    return true;
  },

  async migrateIcalDataToCreateIcalSummary() {
    const datesDiffusionsWithIcal = await models.DateDiffusion.findAll({
      where: {
        ical_data: {
          [Op.ne]: null,
        },
      },
    });
    for(const d of datesDiffusionsWithIcal) {
      if(d?.ical_data?.summary) {
        d.ical_summary = d.ical_data.summary;
        await d.save();
      }
    }
  },
};