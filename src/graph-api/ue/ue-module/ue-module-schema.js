import gql from 'graphql-tag'

/*
* UE Modules - Modules de formations (si UE de type formation)
*
* */

export default gql`
    extend type Query {
        "Get UE modules by UE id (formation modules)"
        ueModules(ueId: ID!): [UEModule]
        "Count UE modules in a formation"
        countUEModules(ueId: ID!): Int
        "User progress for an ueId (formation)"
        ueModuleProgressInUE(ueId: ID!, userId: ID): [UEModuleProgress]
        
        "Single ue module progress"
        ueModuleProgress(ueModuleId: ID!, userId: ID): UEModuleProgress
    }

    extend type Mutation {
        #UeModule
        createUEModule(input: UEModuleInput!): UEModule
        updateUEModule(id: ID!, input: UEModuleInput!): Boolean
        deleteUEModule(id: ID!): Boolean
        
        "Save time spent on module, global and for specific progress log"
        saveTimeSpentOnModule(ueModuleId: ID!, seconds: Int, ueModuleProgressLogId: ID, currentBlockId: ID): Boolean
        "Handle user opens, start or resume ue module, retourne id de ue_module_progress_log"
        handleUserDoUEModule(ueModuleId: ID!, ueId: ID!, currentBlockId: ID): UserModuleProgressLogResponse
        
        "When user clicks on next module in a formation, this will update the progress"
        handleNextUEModule(ueModuleId: ID!, ueId: ID!, currentBlockId: ID, ueModuleProgressLogId: ID): Boolean
    }
    
    "Return type for handleUserDoUEModule"
    type UserModuleProgressLogResponse {
        ueModuleProgressLogId: ID
    }

    "Formation module"
    type UEModule {
        id: ID
        "parent formation"
        ueId: ID
        "cours id optional"
        coursId: ID
        "elementId optional"
        elementId: ID
        cours: Cours
        element: FormationElement
        "module validation settings"
        validationSettings: JSON
        "module display settings"
        displaySettings: JSON
        "order in parent formation"
        order: Int
        "Number of steps in the module (only for courses with steps)"
        countSteps: Int
        createdAt: Date
        updatedAt: Date
    }
    input UEModuleInput {
        ueId: ID
        coursId: ID
        elementId: ID
        validationSettings: JSON
        displaySettings: JSON
        order: Int
    }

    "General progress for a module in a formation"
    type UEModuleProgress {
        id: ID
        "parent formation"
        ueId: ID
        "module id"
        ueModuleId: ID
        "user id"
        userId: ID
        "seconds spent on module"
        seconds: Int
        "true if module is completed"
        completed: Boolean
        "number of steps completed (only for courses with steps)"
        stepsCompleted: Int
        "total number of steps (only for courses with steps)"
        totalSteps: Int
        createdAt: Date
        updatedAt: Date
    }
    
    "Detailed progress logs for a module or element in a formation"
    type UEModuleProgressLogs { 
        id: ID
        "parent formation"
        ueId: ID
        "module id"
        ueModuleId: ID
        "user id"
        userId: ID
        "Type of log operation"
        logOperation: String
        "seconds spent on module"
        seconds: Int
        "block id if applicable"
        blockId: Int
        completed: Boolean
        createdAt: Date
        updatedAt: Date
    }
    
`
