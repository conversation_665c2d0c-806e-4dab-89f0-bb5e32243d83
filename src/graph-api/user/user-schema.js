import gql from 'graphql-tag';

export default gql`
    extend type Query {
        "Connected user"
        me: User
        "All users"
        users: [User!]
        "Get user by ID"
        user(id: ID!): User
        "Get users by idArray"
        getUsersArray(idArray:[ID]!):[User]!
        "Get all users with TUTEUR role"
        tuteurs: [User!]
        "Search among all users with UserSearchFilter"
        searchUsers(filter: UserSearchFilter): UserSearchResult
        "Search by username, for autocomplete"
        searchUsersByUsername(username: String!, role: String, domain: String): [User]
        "User profile, used for profile cards"
        userProfile(id: ID, username: String): User

        checkIfUserShouldSetupUsername(inputToken: String): Boolean
        checkUsernameAvailability(username: String): <PERSON><PERSON><PERSON>
        
        "Get parent's childs"
        myChilds: [User]
        
        "My responsables"
        myResponsables: [User]

        "User notification preferences"
        userNotificationSettings: NotificationSettings

        reportedContent: [Log]

        getTeamsMembersForMe: [User]

        "Users I have blocked"
        myBlockedUsers: [User]

        "Find all users asking for deletion"
        usersAskingForDeletion: [User]

        "find all user active by date and belonging to one or multiples groups and with a filter on the username"
        #findUsersActiveForDates(activeUsersFilter:ActiveUsersFilter,startDate:Date!,endDate:Date!): ActiveUsersResult
        #todo voir pour dataloader de stats
        # Nouvelle façon de récupérer les stats => fonction plus polyvalante et complete de récupération de stats
        findUsersActiveForDates(activeUsersFilter:ActiveUsersFilter,startDate:Date!,endDate:Date!): ActiveUsersResult
      
        "Fonction de récupération de stats au cours du temps de façon à créer un graph "
        activeUsersGraph(activeUsersFilter: ActiveUsersFilter, graphFilter: ActiveUsersGraphFilter): GraphTimeDistributedData

        "user reCAPTCHA post"
        verifyRecaptcha(inputToken:String!) : RecaptchaResult

        userPropertiesFolders: [UserPropertyFolder]

        debugStatsForUserWithinDates(userId: ID!, startDate: Date!, endDate: Date!): JSON
        
        "Get user ids matching search filter"
        userIdsMatchingFilter(filter: UserSearchFilter): [ID]
        
        "Export users by filter and get link"
        exportUserIdsMatchingFilter(filter: UserSearchFilter): String
    }
    
    extend type Mutation {
        "Sign in with login and password and get Token"
        signIn(login: String!, password: String!): Token!
        "Sign in with login, password, and recaptcha discret v2 and get User infos and token"
        signInWithUser(recaptchaToken:String, login: String!, password: String!): TokenWithUser!
        "Impersonate user"
        impersonateUser(login: String!): TokenWithUser!

        "Block or unblock a user"
        blockOrUnBlockUser(userId: ID!, action: String!): Boolean

        "Activate with words"
        activateAssessmentWithBook(page: String, answer: String): Boolean
        "Send forgot password email"
        forgotPassword(email: String!): String!

        "First time setup password"
        resetPasswordWithToken(token: String!, password: String!): Boolean!
        "User create first password from link with token"
        createFirstPasswordToken(token: String!, password: String!, username: String): Boolean!

        "Increment user stat, called when a user visit a course"
        incrementUserStat(operation: String!, objectId: ID): Boolean
        "User's Background infos"
        setUserBackground(input: JSON!): Boolean
        "Delete user reported content"
        deleteReportedContent(id: ID): Boolean

        "Add one credit after In-App-Purchase"
        addOneCreditIAP(key: String): Boolean

        "user asks for deletion"
        askForAccountDeletion: Boolean

        #TODO change updateMyProfile with input
        updateMyProfile(username: String, name: String, email:String, addressline1:String, addressline2:String,postcode:String,city: String,phone: String, bio: String, appearsInTeam: Boolean, appearsInSubjects: Boolean, isReachableByPrivateMessage: Boolean, lang: String): User!
        updatePassword(oldPassword: String!, newPassword: String): User!
        "Update user avatar"
        updateMyAvatar(file: Upload!): DataPath!
        "Update user avatar from list of avatars (setup in admin appearance)"
        updateMyAvatarFromList(filename: String!): Boolean
        "Update user notification preferences"
        updateUserNotification(privateMessageEmailNotification: Boolean, coursUpdateEmailNotification: Boolean, coursUpdateDeviceNotification: Boolean, qcmUpdateDeviceNotification: Boolean): NotificationSettings

        "Create a new user - for admins"
        createUser(user: UserInput!): User!
        "Update user"
        updateUser(id: ID!, user: UserInput): Boolean
        updateUserPassword(newPassword: String!, userId: ID!): User!
        updateUserAvatar(userId: ID!, file: Upload!): DataPath!
        deleteUser(id: ID!): Boolean!
        restoreDeletedUser(id: ID!): Boolean!

        updateUserGroup(userId: ID!, groupId: ID!): User!

        "Add tutor as responsible for subject"
        addTuteurToUe(ueId: ID!, userId: ID!): UE
        "Remove tutor as responsible for subject"
        removeTuteurFromUe(ueId: ID!, userId: ID!): UE
        "Update (remove all previous associations, and add thoses in argument) tutor as responsible for subjet"
        updateTuteurFromUe(ueIdArray:[ID]!,userId:ID!):Boolean

        "Import Users from XLS file"
        importUsersFromXls(file: Upload, doImport: Boolean, users: JSON, filename: String, email: EmailUserImportSettings, domain: String): JSON
        "Mutation to accept current CGU, return true  if transaction was good, else false"
        acceptCgu:Boolean
        "Mutation to reject current CGU, return false if transaction was good, else false"
        rejectCgu:Boolean

        createUserPropertyFolder(input: UserPropertyFolderInput): Boolean
        updateUserPropertyFolder(id: ID!, input: UserPropertyFolderInput): Boolean
        deleteUserPropertyFolder(id: ID!): Boolean

        "Create if not exist, update if exist. Deleted is like null values because all users have the same properties. For global user properties only"
        createOrUpdateUserPropertyData(input: UserPropertyDataInput): Boolean

        "Update UserPropertyData by id"
        updateUserPropertyDataById(id: ID!, input: UserPropertyDataInput): Boolean

        sendInputElementsForm(input: [UserPropertyDataInput]): Boolean

        addParentChild(childId: ID!, parentId: ID!): Boolean
        removeParentChild(childId: ID!, parentId: ID!): Boolean

        "Mass update selected users with selected groups (add or remove). if hasSelectedAll is true, all users within filter will be updated"
        massUpdateUserGroups(operation: String, userIds: [ID], groupIds:[ID], hasSelectedAll: Boolean, filter: UserSearchFilter): Boolean
        "Mass update user preferences"
        massUpdateUserPreferences(userIds: [ID], preferences:PreferenceInput, hasSelectedAll: Boolean, filter: UserSearchFilter): Boolean
        
        disconnectUserFromAllDevices(userId: ID!): Boolean
        
        "Delete all users in groups ids"
        deleteAllUsersInGroups(groupIds: [ID]): Boolean
    }
    
    input PreferenceInput {
        appearsInSubjects: Boolean
        appearsInTeam:Boolean

        limitNumber:Int
        isActive:Boolean
        timeWindow:Int
        #days
        #hours
        #isActiveDiscussionLimit
        #limitNumber
        #minutes
        #seconds
    }
    
    # Active users
    input ActiveUsersFilter {
        startDate:Date!
        endDate:Date!
        groupIdArray:[ID]!=[]
        pseudoFilter:String=""
        roleFilter:[String]=[]
        companyFilter:[ID]!=[]
        coursIdsFilter:[ID]!=[]
    }
    
    "Input pour le activeUserGraphFilter => notament ici, comment discrétiser le temps"
    input ActiveUsersGraphFilter {
        "Indication on how to discretize the time"
        timeDiscretization:String
    }
    
    "Ligher type that return already pre-processed stats"
    type StatsReport{
      # session stats
      sessionCount:Int
      sessionMinutes:Int
      averageSessionCount:Float
      averageSessionMinutes:Float
      
      # overall stats
      "Le nombre total de classes vu par le batch et sur la période => pas d'unicité"
      seenClasses:Int
      "Le nombre total de mcqDone (série d'exercice ?) par le batch et sur la période => pas d'unicité"
      mcqDone:Int
      "Nombre moyen de séries d'exercice réalisés par le batch et sur la période"
      averageMcqDone:Float
      "Nombre de fichier téléchargés par le batch et sur la période"
      downloadedFiles:Int
      "Nombre d'userActifs dans le batch"
      activeUsers:Int
      "Nombre de questions réalisées par le batch"
      questionsDone:Int
      "Nombre de posts réalisés par le batch"
      postsSent:Int
      
      # Stats techniques
      "Nombre de classes unique vu par le batch et sur la période"
      batchUniqueSeenClassesPeriode:Int
      
      "Nombre median de file téléchargé par le batch"
      medianDownloadedFiles:Float
      
      # Progression
      "La moyenne des progressions de chaque user sans borne temporelle du batch pour donner une progression moyenne "
      allTimeBatchProgress:Float
      "la médiane des progressions de chaque user sans borne temporelle du batch)"
      allTimeProgressMedian:Float
      
      
      "La moyenne des progression de chaque user Sur la période temporelle donnée"
      periodeBatchProgress:Float
      "la médiane des progressions de chaque user sur la période temporelle donnée"
      periodeProgressMedian:Float
      
      
      "All time - Nombre de cours Unique visible par le batch au total"
      allTimeBatchSeeableClassesIds:Int
      
    }
    
    "Return data of graph filtration => les données sont très formatées pour être universelles"
    type GraphTimeDistributedData{
      "for each dataType returned, we have timeStamp"
      uniqueSeenClasses:[ActiveUserGraphNodeData]
      totalSeeableClasses:Int
      exercisesDone:[ActiveUserGraphNodeData]
      activeUsers:[ActiveUserGraphNodeData]
      downloadedFiles:[ActiveUserGraphNodeData]
      allSeenClasses:[ActiveUserGraphNodeData]
      postsSent:[ActiveUserGraphNodeData]
    }
    
    
    "TimeStamp for a data type for graph point"
    type ActiveUserGraphNodeData {
        "label : description de la métrique"
        label: String
        "La date reconstituée correspondant à l'agrégat de donnée"
        reconstructedDate: Date
        "la valeur de la metrique"
        count: Int
    }
    #/Active users

    type RecaptchaResult{
        success:Boolean
        score:Float
        action:String
    }

    type UserSearchResult {
        count: Int
        users: [User]
    }
    
    
    # Active users result and global stats (aggregated average)
    #type ActiveUsersResult {
    #    stats: ActiveUsersGlobalStats
    #    users: [User]
    #}

    type ActiveUsersResult {
      stats: StatsReport
      users: [User]
    }
    
    type ActiveUsersGlobalStats {
        progressAverage: Float
        timeSpentAverage: Float
        numberOfSessionsAverage: Float
        askedQuestions: Int
        exercisesDone: Int
        "total seen classes"
        seenClasses: Int
        "unique seen classes"
        uniqueSeenClasses: Int
        "total seeable classes"
        totalSeeableClasses: Int
        exerciseSeriesCompleted: Int
        privateMessagesSend: Int
        likes: Int
    }
    
    type NotificationSettings {
        privateMessageEmailNotification: Boolean
        coursUpdateEmailNotification: Boolean
        forumUpdateEmailNotification: Boolean
        coursUpdateDeviceNotification: Boolean
        forumUpdateDeviceNotification: Boolean
        qcmUpdateDeviceNotification: Boolean
    }

    input EmailUserImportSettings {
        sendEmail: Boolean
        sendUsernameAndPassword: Boolean
        subject: String
        body: String
    }

    type DataPath {
        path: String
    }
    type Filepath {
        path: String!
    }

    type Token {
        token: String!
    }

    type TokenWithUser {
        token: String!
        me: User
    }

    enum UserType{
        USER
        TUTEUR
        ADMIN
    }

    input UserSearchFilter {
        offset: Int
        limit: Int

        isActive: Boolean
        deleted: Boolean

        username: String
        firstName: String
        name: String
        email: String
        role: [String]
        
        everywhere: String

        banned: Boolean

        groupId: ID
        
        # New fields
        sortField: String
        sortOrder: String
        "IDs de groupes"
        groupIds: [ID]
        "type de relation (or, and, not in)"
        groupRelationType: String
    }

    type User {
        id: ID!
        "Unique username used to login"
        username: String!
        "Additional unique identifier, can be used to import user exercises results"
        userCodeName: String

        hasValidAvatar: Boolean
        "Whether user can receive PM from others. If disabled, user will not appears on search"
        isReachableByPrivateMessage: Boolean

        firstName: String
        title: String
        name: String
        bio: String
        email: String # can be null for user querying other users (privacy protection)
        role: String
        avatar: String

        hasSetupUsername: Boolean
        hasSetupPassword: Boolean
        "True if user is Exoteach staff (cannot be deleted)"
        exostaff: Boolean

        addressline1: String
        addressline2: String
        postcode: String

        birthdate: Date
        gender: String
        nationality: String
        country: String
        phoneCountryCode: String

        ip: String
        lang: String
        credits: Int
        warningReceived: Int
        banned: Boolean

        "User asking for deletion"
        asksForDeletion: Boolean

        "For Admin/Teachers only"
        appearsInTeam: Boolean
        appearsInSubjects: Boolean

        responsibleForUeIds: [ID]

        "If user has accepted last CGU version"
        hasAcceptedCGU: Boolean
        resetPasswordToken: String
        "User's phone number"
        phone: String
        city: String

        messagesSent: Int # unused?
        likesReceived: Int # unused?

        stats(startDate: Date, endDate: Date): UserStats

        messages: [Message]

        groups: [Groupe]
        bills: [Bill]
        notifications: [Notification]

        isActive: Boolean

        createdAt: Date
        updatedAt: Date
        deletedAt: Date

        "User's mobile devices, for notifications"
        user_devices: [Device]

        externalId: String

        fromUE: [UE]

        "User's background data"
        background: JSON

        parentsEmail: String
        parentsPhone: String
        parentsProfession: String

        bot: Boolean
        bot_personality: String

        isBlocked: Boolean

        "Auth token version"
        tokenVersion: Int

        individualGroup: Groupe

        myGroupsResponsibility: [Groupe]

        statsByDay(startTime: Date, endTime: Date): [UserTimeSpentByDay]
        totalTimeSpentExercising(filter: UserTimeSpentFilter): [UserTimeSpentExercising]

        "User can have childs (if it's a parent)"
        childs: [User]
        "User can have parents (if it's a child)"
        parents: [User]
      
        "companies linked to the user"
        companiesDescriptions(userId:ID):[CompanyDescription]
      
        "If the user can have extra time (tier-temps)"
        isExtraTime:Boolean
    }
    input UserTimeSpentFilter {
        ueId: ID
        coursId: ID
        ueCategoryId: ID
    }

    type UserTimeSpentByDay {
        id: ID!
        userId: ID!
        "seconds"
        time: Int
        "Date of the day"
        date: Date
    }
    type UserTimeSpentExercising {
        id: ID!
        userId: ID!
        ueId: ID
        ueCategoryId: ID
        coursId: ID
        "seconds"
        time: Int
        createdAt: Date
        updatedAt: Date
    }

    input UserInput {
        username: String
        userCodeName: String
        password: String
        title: String
        confirm: String # password confirm
        bio: String
        firstName: String
        name: String
        email: String
        role: String
        avatar: String
        addressline1: String
        addressline2: String
        postcode: String
        birthdate: Date
        gender: String
        nationality: String
        country: String
        phoneCountryCode: String
        ip: String
        credits: Int
        warningReceived: Int
        banned: Boolean
        resetPasswordToken: String
        phone: String
        city: String
        likesReceived: Int
        asksForDeletion: Boolean
        parentsEmail: String
        parentsPhone: String
        parentsProfession: String
        "If user has accepted CGU"
        hasAcceptedCGU: Boolean
        lang: String
        "If user is reachable by private message"

        isReachableByPrivateMessage: Boolean
        deletedAt: Date

        "For Admin/Teachers only"
        appearsInTeam: Boolean
        appearsInSubjects: Boolean

        bot: Boolean
        bot_personality: String
      
        "company linked to the user. It's becoming mandatory to inform"
        companyId:[ID]
        isExtraTime:Boolean
    }

    type Bill {
        id: ID!
        name: String
        file: String!
        userId: ID
        paymentId: ID
        createdAt: Date
        updatedAt: Date
    }

    type UserStats {
        uniqueSeenClasses: Int
        totalUniqueSeenClasses: Int
        totalSeeableClasses: Int
        userId: ID
        seenClasses: Int
        seenAnnales: Int
        seenMcq: Int
        mcqDone: Int
        postsSent: Int
        accountConnections: Int
        likesReceived: Int
        likesGiven: Int
        downloadedFiles: Int
        privateMessagesSent: Int
        privateMessagesReceived: Int
        questionsDone: Int

        sessionCount: Int
        sessionMinutes: Int
    }

    type Device {
        "Used for push notifications"
        uuid: String
        deviceName:String
        platform: String
        osVersion: String
        model: String
        manufacturer: String
        isVirtual: Boolean
        createdAt: Date
        updatedAt: Date
    }

    "User property folder, used to store user's custom data"
    type UserPropertyFolder {
        id: ID!
        name: String
        name_en: String
        name_es: String
        name_de: String
        name_it: String
        "Input elements in folder"
        elements: [FormationElement]

        createdAt: Date
        updatedAt: Date
    }
    input UserPropertyFolderInput {
        name: String
        name_en: String
        name_es: String
        name_de: String
        name_it: String
    }

    type UserPropertyData {
        id: ID!
        value: String
        values: [String]
        "Element ID associé"
        elementId: ID
        element: FormationElement
        "Si les champs remplis sont dans un formulaire, id du formulaire"
        formId: ID
        userId: ID
        #user: User
        createdAt: Date
        updatedAt: Date
    }
    input UserPropertyDataInput {
        value: String
        values: [String]
        file: Upload
        elementId: ID
        userId: ID
        "Si les champs remplis sont dans un formulaire, id du formulaire"
        formId: ID
    }


`;
