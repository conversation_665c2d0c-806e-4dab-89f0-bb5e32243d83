import { DeleteObjectCommand } from '@aws-sdk/client-s3'
import { GraphQLError } from 'graphql'
const { S3Client, ListBucketsCommand,  CreateMultipartUploadCommand,
  UploadPartCommand,
  CompleteMultipartUploadCommand, <PERSON><PERSON><PERSON>Command,GetObjectCommand,
  CreateBucketCommand,
  HeadBucketCommand,
  PutBucketCorsCommand,
} = require('@aws-sdk/client-s3');
import { isDev } from '../../index.js';

import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { LOG_OPERATIONS } from '../../models/log/log'
import { UploadService } from '../file/upload-service'
import LogService from '../log/log-service'
///////// Init du client

const s3 = new S3Client({
  region: process.env.S3_BUCKET_REGION, // adapte à ta région
  credentials: {
    accessKeyId: process.env.S3_DEV_ACCESS_KEY_ID,
    secretAccessKey: process.env.S3_DEV_SECRET_KEY,
  },
});

export const S3Service = {

  initS3Module:{
    createBuckIfNotAlreadyHere:async ()=>{
      try{

        // Creation du bucket
        try {
          await s3.send(new HeadBucketCommand({ Bucket: process.env.S3_BUCKET_NAME }));
          console.log(`[S3] Bucket ${process.env.S3_BUCKET_NAME} existe déjà !`)
        }catch(err) {
          if (err?.name==='NotFound'||err.$metadata?.httpStatusCode===404){
            await s3.send(new CreateBucketCommand({
              Bucket:process.env.S3_BUCKET_NAME,
              CreateBucketConfiguration:{LocationConstraint:process.env.S3_BUCKET_REGION}
            }))
            console.log(`Bucket : ${process.env.S3_BUCKET_NAME} créé !`)
          }
        }

        // Reset du CORS
        const corsRules={
          CORSRules:[{
            AllowedHeaders:[
              "Content-Type",
              "Authorization",
            ],
            AllowedMethods: [
              'GET',
              'PUT',
              'POST',
              'HEAD',
            ],
            AllowedOrigins:[
              //`${process.env.FRONT_URL.replace(/\/$/, '')}`
              isDev ? `http://localhost:8001` : `https://${process.env.DOMAIN}`
            ],
            ExposeHeaders:[
              "ETag"
            ],
            MaxAgeSeconds:[
              3000
            ]
          }]
        };

        try {
          await s3.send(new PutBucketCorsCommand({Bucket:process.env.S3_BUCKET_NAME,CORSConfiguration:corsRules}))
          console.log(`[S3] CORS mis à jour pour ${process.env.S3_BUCKET_NAME}`);
        }catch(e){
          console.error(`init S3 cors rules error, e:`,e)
          throw new Error(e)
        }
      }catch(e){
       console.error(`Error during bucket S3 init : ${e}`)
      }
    }
  },

  initiateMultipartUpload:async({args,ctx})=>{
    // Fonction d'initialisation du multipart upload => permet de générer pour chaque chunk, plusieurs url pré-validées au front
    try {

      const {filename,contentType,partsCount}=args

      const sanitizedFilename=UploadService.sanitizeFilename({filename:filename})

      // 1. Créer l'uploadId
      const createCmd = new CreateMultipartUploadCommand({
        Bucket:process.env.S3_BUCKET_NAME,
        Key: sanitizedFilename,
        ContentType: contentType,
      });
      const {UploadId}=await s3.send(createCmd)


      // 2. Générer les presigned URLs pour chaque part
      const presignedUrls=await Promise.all(
        Array.from({length:partsCount},async(_,i)=>{
          const partNumber=i+1;

          const uploadPartCmd = new UploadPartCommand({
            Bucket:process.env.S3_BUCKET_NAME,
            Key:sanitizedFilename,
            UploadId,
            PartNumber:partNumber
          })

          const url=await getSignedUrl(s3,uploadPartCmd,{expiresIn:500})

          return {partNumber,url}
        })
      )

      // On log la création du lien présigné d'upload
      await LogService.logAction({
        logOperation:LOG_OPERATIONS.S3.CreateS3FileUploadLinkMultipart.action,
        logData:{
          fileName:sanitizedFilename,
          uploadId:UploadId,
        },
        models:ctx?.models,
        userId:ctx?.me.id,
      })

      return {
        uploadId:UploadId,
        presignedUrls,
        sanitizedFilename,
      }
    }catch(e){
      console.error("InitiateMultipartUpload Error e :",e)
      throw new GraphQLError(e)
    }
  },

  completeMultipartUpload:async({args,ctx})=>{
    // Fonction de finalisation de l'upload multipart.
    try {
      const {filename,uploadId,parts}=args

      const completeCmd=new CompleteMultipartUploadCommand({
        Bucket:process.env.S3_BUCKET_NAME,
        Key:filename,
        UploadId:uploadId,
        MultipartUpload:{
          Parts: parts.map(p => ({
            PartNumber:p.partNumber,
            ETag:p.eTag,
          }))
        },
      });

      // Normalement, si validation pas bonne, s3 lève automatiquement error,
      const a= await s3.send(completeCmd);


      // On log la création du lien présigné d'upload
      await LogService.logAction({
        logOperation:LOG_OPERATIONS.S3.ConfirmS3UploadMultipart.action,
        logData:{
          fileName:filename,
          uploadId,
        },
        models:ctx?.models,
        userId:ctx?.me.id,
      })

      return true;
    }catch(e){
      console.error("complete MultipartUpload error, e :",e)
      throw new GraphQLError(e)
    }
  },

  initiateSinglePartUpload:async ({args,ctx})=>{
    try {

      const {filename,contentType}=args

      const sanitizedFilename=UploadService.sanitizeFilename({filename:filename})

      const putCmd=new PutObjectCommand({
        Bucket:process.env.S3_BUCKET_NAME,
        Key:sanitizedFilename,
        ContentType:contentType,
      })

      const url = await getSignedUrl(s3,putCmd,{expiresIn:300})

      // On log la création du lien présigné d'upload
      await LogService.logAction({
        logOperation:LOG_OPERATIONS.S3.CreateS3FileUploadLinkMonopart.action,
        logData:{
          fileName:sanitizedFilename,
        },
        models:ctx?.models,
        userId:ctx?.me.id,
      })

      return {url,sanitizedFilename}
    }catch(e){
      console.error(" initiateSinglePartUpload error, e:",e)
      throw new GraphQLError(e)
    }
  },

  getSignedUrlForFile:async({args,simplifiedCtx:ctx})=>{
    try {
      const {filename,expirationTime}=args

      const command= new GetObjectCommand({
        Bucket:process.env.S3_BUCKET_NAME,
        Key:filename,
        ResponseContentDisposition: `inline; filename="${filename}"`,
      })

      const url = await getSignedUrl(s3,command,{expiresIn:expirationTime || 300})

      return url
    }catch(e){
      console.error("error in getSignedUrlForFile :",e)
      throw new GraphQLError(e)
    }
  },
  getFileForDownload:async({args,simplifiedCtx:ctx})=>{
    try {
      const {filename,expirationTime}=args

      const command = new GetObjectCommand({
        Bucket:process.env.S3_BUCKET_NAME,
        Key: filename,
        ResponseContentDisposition: `attachment; filename="${filename}"`,
      });

      // Génération du lien présigné
      const url = await getSignedUrl(s3,command,{expiresIn:expirationTime || 300})

      return url
    }catch(e){
      console.error("error in getSignedUrlForFile :",e)
      throw new GraphQLError(e)
    }
  },

  deleteFileFromBucket:async({args,ctx})=>{
    // Fonction qui supprime un file (avec fileName) du bucket S3 et log
    try {
      const {fileName}=args

      const command=new DeleteObjectCommand({
        Bucket:process.env.S3_BUCKET_NAME,
        Key:fileName,
      })

      try {
        const response=await s3.send(command);

        await LogService.logAction({
          logOperation:LOG_OPERATIONS.S3.DeleteFile.action,
          logData:{fileName},
          models:ctx?.models,
          userId:ctx?.me.id,
        })

      } catch(e){
        console.error(e)
        throw new Error(e)
      }
    }catch(e){
      console.error(`Dans DeleteFileFromBucket => Essai de delete un fichier. filanem : ${fileName}  , e : ${e}`)
      throw new GraphQLError(e)
    }
  }
}