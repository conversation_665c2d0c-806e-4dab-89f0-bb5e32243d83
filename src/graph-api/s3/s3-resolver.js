import { GraphQLError } from 'graphql';
import { combineResolvers } from 'graphql-resolvers'
import { isAdmin, isAuthenticated, isSuperAdmin, isTuteurOrAdmin, ROLES } from '../authorization'
import { S3Service } from './s3-service'


'use strict'

export default {
  Query: {
  },

  Mutation:{
    initiateMultipartUpload:combineResolvers(
      isTuteurOrAdmin,
      async(parent,args,ctx,info)=>{
        return await S3Service.initiateMultipartUpload({args,ctx})
      }
    ),
    completeMultipartUpload:combineResolvers(
      isTuteurOrAdmin,
      async(parent,args,ctx,info)=>{
        return await S3Service.completeMultipartUpload({args,ctx})
      }
    ),
    initiateSinglePartUpload:combineResolvers(
      isTuteurOrAdmin,
      async(parent,args,ctx,info)=>{
        return await S3Service.initiateSinglePartUpload({args,ctx})
      }
    )
  }
}