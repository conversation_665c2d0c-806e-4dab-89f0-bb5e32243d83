'use strict'

import { combineResolvers } from 'graphql-resolvers'
import { isAdmin, isAuthenticated } from '../authorization.js'
import { UserCompanyService } from './user-company-information-service.js'

export default {
  Mutation : {
    createOrAddUsersToCompaniesAssociation: combineResolvers(
      isAdmin,
      async (parent, arg, ctx) => {
        return await UserCompanyService.createOrAddUsersToCompaniesAssociation(parent, arg, ctx)
      }
    ),

    deleteUsersToCompaniesAssociationFromAssociationIds: combineResolvers(
      isAdmin,
      async (parent, arg, ctx) => {
        return await UserCompanyService.deleteUsersToCompaniesAssociationFromAssociationIds(parent, arg, ctx)
      }
    ),

    deleteUsersToCompaniesAssociationFromUsersAndCompaniesId:combineResolvers(
      isAdmin,
      async (parent, arg, ctx) => {
        return await UserCompanyService.deleteUsersToCompaniesAssociationFromUsersAndCompaniesId(parent, arg, ctx)
      }
    ),

    replaceUsersToCompaniesAssociationFromUsersAndCompaniesId:combineResolvers(
     isAdmin,
     async(parent,arg,ctx)=>{
       return await UserCompanyService.replaceUsersToCompaniesAssociationFromUsersAndCompaniesId(parent,arg,ctx)
     }
    ),

    addOrDeleteOrReplaceUsersAndGroupsToCompaniesAssociations:combineResolvers(
     isAdmin,
     async(parent,arg,ctx)=>{
       return await UserCompanyService.addOrDeleteOrReplaceUsersAndGroupsToCompaniesAssociations(parent,arg,ctx)
     }
    )
  }
}

