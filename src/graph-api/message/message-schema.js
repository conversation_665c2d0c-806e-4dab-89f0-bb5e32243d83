import gql from 'graphql-tag'

export default gql`
    extend type Query {
        message(id: ID!): Message!
        "Get a discussion by ID"
        discussion(id: ID!): Discussion

        "My discussions"
        mesDiscussions: [Discussion]

        "Discussion's messages"
        messagesInDiscussion(discussionId: ID!, cursor: String, limit: Int): MessageConnection

        "User's discussions (admin)"
        userDiscussions(userId: ID!): [Discussion]
    }

    extend type Mutation {
        "Create a private discussion"
        createDiscussionWith(userId: ID!): Discussion
        
        createDiscussion(input: DiscussionInput): Discussion
        
        updateDiscussion(id: ID!, input: DiscussionInput): Discussion
        
        addUserToDiscussion(discussionId: ID!, userId: ID!): Discussion
        removeUserFromDiscussion(discussionId: ID!, userId: ID!): Discussion
        # Messages (in discussion)
        sendMessage(message: MessageInput!): Message
        
        deleteMessage(id: ID!): Boolean!
        deleteConversation(id: ID!): <PERSON><PERSON><PERSON>
    }

    extend type Subscription {
        messageCreated(discussionId: ID!): Message!
    }
    
    type MessageConnection {
        edges: [Message]
        pageInfo: PageInfo!
    }
    type PageInfo {
        hasNextPage: Boolean
        endCursor: String
    }

    type Message {
        id: ID!
        text: String!
        tag: String
        read: Boolean
        isDeleted: Boolean
        likes: Int
        createdAt: Date!
        updatedAt: Date
        discussionId: ID
        user: User!
        file: File
        fileImage: File
        "Multi fichiers"
        fileList: [File]
        userId: ID
    }

    input MessageInput {
        discussionId: ID!
        text: String
        tag: String
        file: Upload
        isDeleted: Boolean
        fileImage: Upload
        "multi fichiers"
        fileList: [Upload]
    }

    input DiscussionInput {
        authorId: ID
        destinataireId: ID
        isGroup: Boolean
        participants: [ID] # user IDs
        image: Upload
        name: String
        
        createdAt: Date
        updatedAt: Date
    }
    
    type Discussion {
        id: ID!
        author: User
        destinataire: User
        name: String
        isGroup: Boolean
        image: String
        unreadCount: Int #todo
        messages: [Message] # only last message
        participants: [User]
        createdAt: Date
        updatedAt: Date
    }

`
