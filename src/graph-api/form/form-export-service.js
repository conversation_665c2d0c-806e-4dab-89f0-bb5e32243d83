import xlsx from 'node-xlsx';
import { ELEMENTS_TYPE } from '../../models/formation/formation_element';
import models from '../../models/index';
const moment = require('moment');

export const FormExportService = {
  exportFormToExcel: async (formId, ctx) => {
    const form = await models.Forms.findByPk(formId);

    const resultsLinkedToForm = await models.UserPropertiesData.findAll({
      where: { formId: form.id },
      include: [models.FormationElement],
      order: [['createdAt', 'DESC']],
    });

    const formationElementsForms = await models.FormationElementsForms.findAll({
      where: { formId: formId },
      raw: true,
    });

    const formationElementsIds = formationElementsForms.map(f => f.elementId);
    const elements = await models.FormationElement.findAll({
      where: { id: formationElementsIds },
      attributes: ['id', 'objectId'],
    });

    const elementsIdsWithObjectId = elements.filter(e => e.objectId).map(e => e.objectId);

    const resultsFromGlobalProperties = await models.UserPropertiesData.findAll({
      where: { elementId: elementsIdsWithObjectId },
      include: [models.FormationElement],
    });

    const allUserProperties = [...resultsLinkedToForm, ...resultsFromGlobalProperties];

    const allUsers = await models.User.findAll({
      where: {
        id: [...new Set(allUserProperties.map(up => up.userId))],
      },
      raw: true,
    });

    const uniqueItems = [
      ...new Set(
        allUserProperties.map(p => p?.formation_module_element?.name).filter(Boolean)
      )
    ];

    const usersMap = {};

    for (const userProp of allUserProperties) {
      const user = allUsers.find(u => u.id === userProp.userId);
      const itemName = userProp?.formation_module_element?.name;
      const elementType = userProp?.formation_module_element?.type;
      if (!user || !itemName) continue;

      let valueString = '';
      if (userProp.value) {
        valueString = userProp.value;
      } else if (userProp.values) {
        valueString = userProp.values.join(', ');
      }

      if ([ELEMENTS_TYPE.DATE_PICKER, ELEMENTS_TYPE.DATE_AND_TIME_PICKER].includes(elementType)) {
        if (elementType === ELEMENTS_TYPE.DATE_PICKER) {
          valueString = moment(userProp.value).format('YYYY-MM-DD');
        } else {
          valueString = moment(userProp.value).format('YYYY-MM-DD HH:mm');
        }
      }

      if (elementType === ELEMENTS_TYPE.FILE_IMPORT) {
        valueString = '<Fichier>';
      }

      if (!usersMap[user.id]) {
        usersMap[user.id] = {
          baseData: {
            Formulaire: form.name,
            Pseudo: user.username,
            Nom: user.name,
            Prénom: user.firstName,
            Email: user.email,
            'Date de complétion': moment(userProp.createdAt).format('YYYY-MM-DD HH:mm'),
          },
          responses: {},
          lastDate: userProp.createdAt,
        };
      }

      // Mise à jour de la date de complétion si plus récente
      if (moment(userProp.createdAt).isAfter(usersMap[user.id].lastDate)) {
        usersMap[user.id].baseData['Date de complétion'] = moment(userProp.createdAt).format('YYYY-MM-DD HH:mm');
        usersMap[user.id].lastDate = userProp.createdAt;
      }

      usersMap[user.id].responses[itemName] = valueString;
    }

    const headers = [
      'Formulaire',
      'Pseudo',
      'Nom',
      'Prénom',
      'Email',
      'Date de complétion',
      ...uniqueItems
    ];

    const dataXls = [headers];

    for (const userId in usersMap) {
      const base = usersMap[userId].baseData;
      const row = [
        base.Formulaire,
        base.Pseudo,
        base.Nom,
        base.Prénom,
        base.Email,
        base['Date de complétion'],
        ...uniqueItems.map(item => usersMap[userId].responses[item] || '')
      ];
      dataXls.push(row);
    }

    const getColumnSizes = (data) => {
      const colSizes = data?.[0]?.map((_, colIndex) => {
        const colValues = data?.map(row => row[colIndex]);
        const maxLength = Math.max(...colValues?.map(val => (val ? val?.toString()?.length : 0)));
        return { wch: maxLength >= 10 ? maxLength - 1 : maxLength };
      });
      return colSizes;
    };

    const sheetOptions = { '!cols': getColumnSizes(dataXls) };

    return xlsx.build([
      {
        name: 'Export Formulaire',
        data: dataXls,
      }
    ], { sheetOptions });
  }
};
