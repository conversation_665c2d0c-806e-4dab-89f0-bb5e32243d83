import gql from 'graphql-tag'

export default gql`
    extend type Query {
        forumCategory(id: ID!): ForumCategory
        forumCategories(parentId: ID): ForumResponse

        #forumCategoriesInUE(parentId: ID, type: String): ForumResponse
        #forumsInCategory(categoryId: ID!): [Forum]
    }

    extend type Mutation {
        # CATEGORY
        createForumCategory(forumCategory: ForumCategoryInput!): ForumCategory!
        updateForumCategory(id: ID!, forumCategory: ForumCategoryInput!): Boolean
        deleteForumCategory(id: ID!): Boolean!
        
        # FORUM
        createForum(forum: ForumInput!): Forum!
        updateForum(id: ID!, forum: ForumInput!): Boolean
        deleteForum(id: ID!): Boolean!
    }

    type ForumResponse {
        parents: [Forum]
        categories: [ForumCategory]
    }
    
    input ForumInput {
        "The name"
        name: String
        name_en: String
        name_it: String
        name_de: String
        name_es: String
        "Description text"
        description: String
        description_en: String
        description_it: String
        description_de: String
        description_es: String
        tag: String
        views: Int
        isPinned: Boolean
        image: Upload
        parentId: ID
    }

    type Forum {
        id: ID
        "The name"
        name: String
        name_en: String
        name_it: String
        name_de: String
        name_es: String
        "Description text"
        description: String
        description_en: String
        description_it: String
        description_de: String
        description_es: String
        tag: String
        views: Int
        isPinned: Boolean
        image: String
        parentId: ID
        
        lastPost: Post
        postsNumber: Int
        
        posts: [Post]
        
        createdAt: Date
        updatedAt: Date
    }
    
    type ForumCategory {
        id: ID
        "The name"
        name: String
        name_en: String
        name_it: String
        name_de: String
        name_es: String
        "Description text"
        description: String
        description_en: String
        description_it: String
        description_de: String
        description_es: String
        tag: String
        views: Int
        isPinned: Boolean
        image: String
        forums: [Forum]

        parent: Forum 

        color: String
        color2: String
        
        groupes: [Groupe]
    }

    input ForumCategoryInput {
        "The name"
        name: String
        name_en: String
        name_it: String
        name_de: String
        name_es: String
        "Description text"
        description: String
        description_en: String
        description_it: String
        description_de: String
        description_es: String
        tag: String
        views: Int
        isPinned: Boolean
        forumId: ID
        color: String
        color2: String
    }
`
