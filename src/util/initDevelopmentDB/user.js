import models from '../../models'
import { CONFIG_KEYS } from '../../models/config'

export const initUsersLambdas = async () => {

  const sylvain = await models.User.create(
    {
      username: 'sylv',
      name: '<PERSON><PERSON><PERSON><PERSON>',
      email: '<EMAIL>',
      password: 'sylvepass',
      role: 'ADMIN'
    }
  )
  const admin = await models.User.create(
    {
      username: 'admin',
      name: 'admin',
      email: '<EMAIL>',
      password: 'SuperSmashBros',
      addressline1: 'Adresse',
      postcode: 'H2W1P4',
      city: 'Marseille',
      role: 'ADMIN'
    }
  )
  const tuteur = await models.User.create(
    {
      username: 'tuteur',
      name: 'Gentil',
      email: '<EMAIL>',
      password: 'tuteur',
      role: 'TUTEUR',
      addressline1: 'Adresse',
      postcode: '13000',
      city: 'Marseille',
    }
  )
  const tuteur2 = await models.User.create(
    {
      username: 'tuteur2',
      name: '<PERSON><PERSON>',
      email: '<EMAIL>',
      password: 'tuteur2',
      role: 'TUTE<PERSON>',
      addressline1: 'Adresse',
      postcode: '13000',
      city: 'Marseille',
    }
  )

  const etudiant1 = await models.User.create(
    {
      username: 'etudiant1',
      password: 'OAZEHObbdioabzdbadzbuaz',
      name: 'Etudiant 1',
      email: '<EMAIL>',
      role: 'USER',
      addressline1: 'Adresse',
      postcode: '13000',
      city: 'Marseille',
    }
  )

  const etudiant2 = await models.User.create(
    {
      username: 'etudiant2',
      password: 'OAZEHObbddaioabzdbadzbuaz',
      name: 'Etudiant 2',
      email: '<EMAIL>',
      role: 'USER',
      addressline1: 'Adresse',
      postcode: '13000',
      city: 'Marseille',

    }
  )

  const user = await models.User.create(
    {
      username: 'user',
      name: 'Étudiant Lambda',
      email: '<EMAIL>',
      password: 'user',
      role: 'USER'
    }
  )

  for (let step = 0; step < 30; step++) {
    const fakeUser = await models.User.create(
      {
        username: 'fakeUser'+step,
        name: 'Étudiant Lambda',
        email: 'test+'+step+'@user.com',
        password: 'fakeUser+'+step,
        role: 'USER',
        addressline1: 'addressline adresse user',
        addressline2: 'addressline ',
        postcode: '34500',
        phone: '0987654321',
        city: 'Ville lambda',
        ip: '***************',
        isActive: 'true'
      }
    )
  }

  return { sylvain, admin, tuteur, tuteur2, etudiant1, etudiant2, user }
}
