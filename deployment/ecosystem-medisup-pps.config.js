const MEDIBACK_GIT = '*****************:mediboxteam/mediback.git'
const apps = require('./apps.js')
const helper = require('./helpers.js');
const HOST = '*************'
const USER = 'debian'
const CURRENT_ECOSYSTEM_PATH = 'deployment/ecosystem-medisup-pps.config.js'

// NON PROD env
const USER_STAGING = ' '
const HOST_STAGING = ''

const DOMAIN = 'connect.groupe-medisup.com';
const UPLOAD_PATH_QCM = '/var/www/html/qcm/uploads'
const ENV_PROD = {

  NODE_ENV: 'production',
  CURRENT_ENV: 'production',
  DOMAIN: DOMAIN,
  FRONT_URL: `https://${DOMAIN}/elearning/`, // For sending emails
  DATABASE_HOST: '127.0.0.1',
  DATABASE: 'exoteach',
  DATABASE_USER: 'root',
  DATABASE_PASSWORD: 'S4rXFbetdQPF',
  SEQUELIZE_DIALECT: 'mysql',
  SECRET: 'GduOzA263.KeBaB.GhNvnW.MzO23',
  ENGINE_API_KEY: '',
  SEQUELIZE_CHARSET: 'utf8mb4',
  WIPE_BDD: 'FALSE', // DO NOT TOUCH
  UPLOAD_PATH_QCM: UPLOAD_PATH_QCM,

  GOOGLE_APPLICATION_CREDENTIALS: 'medisup-firebase.json', // Medisup app
  GOOGLE_APPLICATION_URL: 'https://medisup-e06b9-default-rtdb.europe-west1.firebasedatabase.app', // récupérer l'addresse dans le google-services du dossier /android/app,
  S3_BUCKET_NAME: 'connect.groupe-medisup.com-63e9a0a1',
  S3_DEV_ACCESS_KEY_ID: '********************',
  S3_DEV_SECRET_KEY: 'wUzfLUpg2KLun7bYlf7FUAVBboENzJrNbbWZ1WCU',
  S3_BUCKET_REGION: 'eu-west-3',
}

// UNUSED TO DELETE
const ENV_STAGING = {
  NODE_ENV: 'development',
  CURRENT_ENV: 'staging',
  DOMAIN: ' .fr',
  DATABASE_HOST: 'localhost',
  DATABASE: ' ',
  DATABASE_USER: 'root',
  DATABASE_PASSWORD: ' ',
  SEQUELIZE_DIALECT: 'mysql',
  SECRET: 'GduOzA263.KeBaB.GhNvnW.MpO23',
  SEQUELIZE_CHARSET: 'utf8mb4',
  WIPE_BDD: 'FALSE', // DO NOT TOUCH
  UPLOAD_PATH_QCM: UPLOAD_PATH_QCM
}

const NODE_HOME_STAGING = `/home/<USER>
const NODE_HOME = `/home/<USER>
module.exports = {
  apps: [
    apps.mediback(ENV_STAGING, ENV_PROD),
    apps.scheduler(ENV_STAGING, ENV_PROD)
  ],

  deploy: {

    // UNUSED
    staging: {
      user: USER_STAGING,
      host: HOST_STAGING,
      ref: 'origin/master',
      repo: MEDIBACK_GIT,
      path: NODE_HOME_STAGING,
      'pre-deploy': 'git reset --hard', // prévient les erreurs de pull
      'ssh_options': ['StrictHostKeyChecking=no'],
      'post-deploy': `npm i && npm run build && npx sequelize-cli db:migrate && pm2 reload ${CURRENT_ECOSYSTEM_PATH} --env dev --update-env`,
      env: {
        ...ENV_STAGING,
      },
    },

    production: {
      user: USER,
      host: HOST,
      ref: 'origin/master',
      repo: MEDIBACK_GIT,
      path: NODE_HOME,
      'pre-setup': 'ssh-keyscan bitbucket.org >> ~/.ssh/known_hosts',
      'pre-deploy': helper.preDeploy(ENV_PROD.DATABASE_PASSWORD), // prévient les erreurs de pull
      'ssh_options': ['StrictHostKeyChecking=no'],
      'post-deploy': `npm i && npm run build && npx sequelize-cli db:migrate && pm2 reload ${CURRENT_ECOSYSTEM_PATH} --env production --update-env`,
      env: {
        ...ENV_PROD,
      },
    },
  },
}
