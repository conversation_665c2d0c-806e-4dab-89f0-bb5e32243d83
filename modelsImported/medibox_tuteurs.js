/* jshint indent: 2 */

module.exports = function(sequelize, DataTypes) {
  return sequelize.define('medibox_tuteurs', {
    id_tuteur: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      primaryKey: true,
      autoIncrement: true
    },
    login: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    firstname: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    lastname: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    email: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    password: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    date_inscription: {
      type: DataTypes.DATE,
      allowNull: false
    },
    classement: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    promo_actuelle: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    phone: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    disponibilite: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    url_image: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    last_payment: {
      type: DataTypes.DATE,
      allowNull: false
    },
    valid_until: {
      type: DataTypes.DATE,
      allowNull: false
    },
    banned: {
      type: DataTypes.INTEGER(1),
      allowNull: false,
      defaultValue: '0'
    },
    validated: {
      type: DataTypes.INTEGER(4),
      allowNull: true,
      defaultValue: '0'
    }
  }, {
    tableName: 'medibox_tuteurs'
  });
};
