'use strict';
const tableName = 'challenge_badges';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.createTable(tableName,
        {
          id: { allowNull: false, autoIncrement: true, primaryKey: true, type: Sequelize.INTEGER },
          name: { type: Sequelize.STRING },
          image: { type: Sequelize.STRING },
          type: { type: Sequelize.STRING, defaultValue: null },
          description: { type: Sequelize.TEXT, defaultValue: null },
          file: { type: Sequelize.TEXT, defaultValue: null },
          challengeId: {
            type: Sequelize.INTEGER,
            references: {
              model: 'challenges', // name of Target model
              key: 'id', // key in Target model that we're referencing
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE',
          },
          createdAt: { type: Sequelize.DATE, defaultValue: Sequelize.NOW },
          updatedAt: { type: Sequelize.DATE, defaultValue: Sequelize.NOW },
        },
        { transaction },
      );
      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.dropTable(tableName, { transaction });
      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
};

