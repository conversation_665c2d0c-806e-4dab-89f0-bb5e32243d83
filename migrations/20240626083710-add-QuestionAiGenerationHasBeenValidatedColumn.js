'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.addColumn('questions', 'aiGenerationHasBeenValidated', {
      type: Sequelize.BOOLEAN,
      allowNull: true,
      defaultValue: null,
    });



  },

  async down (queryInterface, Sequelize) {
    await queryInterface.removeColumn('questions', 'aiGenerationHasBeenValidated');
  }
};
