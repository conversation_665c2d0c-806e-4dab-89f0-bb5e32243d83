'use strict';

const { ConfigUpgrades } = require('../src/graph-api/config/config-upgrades.js');
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    return await ConfigUpgrades.createRegisterFieldsElementsForfaits();
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
  }
};
