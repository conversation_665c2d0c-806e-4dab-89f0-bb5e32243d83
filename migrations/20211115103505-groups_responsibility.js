'use strict'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction()
    try {
      // many to many
      await queryInterface.createTable(
        'groups_responsibility',
        {
          id: { allowNull: false, autoIncrement: true, primaryKey: true, type: Sequelize.INTEGER },
          groupeId: {
            type: Sequelize.INTEGER,
            references: {
              model: 'groupes', // name of Target model
              key: 'id', // key in Target model that we're referencing
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE',
          },
          responsibleOfGroupeId: {
            type: Sequelize.INTEGER,
            references: {
              model: 'groupes', // name of Target model
              key: 'id', // key in Target model that we're referencing
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE',
          },
          createdAt: { type: Sequelize.DATE, defaultValue: Sequelize.NOW },
          updatedAt: { type: Sequelize.DATE, defaultValue: Sequelize.NOW },
        },
        { transaction },
      );
      await transaction.commit()
    } catch (err) {
      await transaction.rollback()
      throw err
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction()
    try {
      await queryInterface.dropTable('groups_responsibility', { transaction })
      await transaction.commit()
    } catch (err) {
      await transaction.rollback()
      throw err
    }
  },
}
