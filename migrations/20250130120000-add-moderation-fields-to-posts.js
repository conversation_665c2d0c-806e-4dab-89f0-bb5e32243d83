'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      // Check if columns already exist to avoid errors
      const tableDescription = await queryInterface.describeTable('posts');
      
      // Add is_published column if it doesn't exist
      if (!tableDescription.is_published) {
        await queryInterface.addColumn(
          'posts',
          'is_published',
          {
            type: Sequelize.BOOLEAN,
            defaultValue: true,
            allowNull: false
          },
          { transaction }
        );
      }

      // Add moderation_status column if it doesn't exist
      if (!tableDescription.moderation_status) {
        await queryInterface.addColumn(
          'posts',
          'moderation_status',
          {
            type: Sequelize.ENUM('pending', 'approved', 'rejected'),
            defaultValue: 'approved',
            allowNull: false
          },
          { transaction }
        );
      }

      // Add ai_generated_title column if it doesn't exist
      if (!tableDescription.ai_generated_title) {
        await queryInterface.addColumn(
          'posts',
          'ai_generated_title',
          {
            type: Sequelize.STRING(255),
            allowNull: true
          },
          { transaction }
        );
      }

      // Add moderated_by column if it doesn't exist
      if (!tableDescription.moderated_by) {
        await queryInterface.addColumn(
          'posts',
          'moderated_by',
          {
            type: Sequelize.INTEGER,
            allowNull: true,
            references: {
              model: 'users',
              key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'SET NULL'
          },
          { transaction }
        );
      }

      // Add moderated_at column if it doesn't exist
      if (!tableDescription.moderated_at) {
        await queryInterface.addColumn(
          'posts',
          'moderated_at',
          {
            type: Sequelize.DATE,
            allowNull: true
          },
          { transaction }
        );
      }

      await transaction.commit();

      // Add indexes for performance (outside transaction to avoid conflicts)
      try {
        await queryInterface.addIndex('posts', ['is_published']);
      } catch (e) {
        console.log('Index is_published already exists or failed to create');
      }

      try {
        await queryInterface.addIndex('posts', ['moderation_status']);
      } catch (e) {
        console.log('Index moderation_status already exists or failed to create');
      }

      try {
        await queryInterface.addIndex('posts', ['moderated_by']);
      } catch (e) {
        console.log('Index moderated_by already exists or failed to create');
      }

      try {
        await queryInterface.addIndex('posts', ['moderated_at']);
      } catch (e) {
        console.log('Index moderated_at already exists or failed to create');
      }

    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      // Remove indexes first
      try {
        await queryInterface.removeIndex('posts', ['is_published']);
      } catch (e) {
        console.log('Index is_published does not exist');
      }

      try {
        await queryInterface.removeIndex('posts', ['moderation_status']);
      } catch (e) {
        console.log('Index moderation_status does not exist');
      }

      try {
        await queryInterface.removeIndex('posts', ['moderated_by']);
      } catch (e) {
        console.log('Index moderated_by does not exist');
      }

      try {
        await queryInterface.removeIndex('posts', ['moderated_at']);
      } catch (e) {
        console.log('Index moderated_at does not exist');
      }

      // Remove columns
      await queryInterface.removeColumn('posts', 'is_published', { transaction });
      await queryInterface.removeColumn('posts', 'moderation_status', { transaction });
      await queryInterface.removeColumn('posts', 'ai_generated_title', { transaction });
      await queryInterface.removeColumn('posts', 'moderated_by', { transaction });
      await queryInterface.removeColumn('posts', 'moderated_at', { transaction });

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
};
