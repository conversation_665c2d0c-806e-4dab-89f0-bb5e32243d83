'use strict'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction()
    try {
      await queryInterface.createTable(
        'forms_user_completion',
        {
          id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: Sequelize.INTEGER
          },
          formId: {
            type: Sequelize.INTEGER,
            references: {
              model: 'forms',
              key: 'id',
            },
          },
          userId: {
            type: Sequelize.INTEGER,
            references: {
              model: 'users',
              key: 'id',
            },
          },
          complete: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
          },
          createdAt: { type: Sequelize.DATE, defaultValue: Sequelize.NOW },
          updatedAt: { type: Sequelize.DATE, defaultValue: Sequelize.NOW },
        },
        { transaction },
      )
      await transaction.commit()
    } catch (err) {
      await transaction.rollback()
      throw err
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction()
    try {
      await queryInterface.dropTable('forms_user_completion', { transaction })
      await transaction.commit()
    } catch (err) {
      await transaction.rollback()
      throw err
    }
  },
}
