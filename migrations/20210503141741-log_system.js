'use strict'

module.exports = {
  up: (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.createTable('logs', {
        id: {
          type: Sequelize.INTEGER,
          primaryKey: true,
          autoIncrement: true,
        },
        logOperation: {
          type: Sequelize.TEXT,
          allowNull: false,
        },
        logData: {
          type: Sequelize.JSON,
          defaultValue: null,
        },


        userId: {
          type: Sequelize.INTEGER,
          references: {
            model: 'users',
            key: 'id',
          },
        },
        reporteeUserId: {
          type: Sequelize.INTEGER,
          references: {
            model: 'users',
            key: 'id',
          },
        },

        qcmId: {
          type: Sequelize.INTEGER,
          references: {
            model: 'qcm',
            key: 'id_qcm',
          },
        },
        questionId: {
          type: Sequelize.INTEGER,
          references: {
            model: 'questions',
            key: 'id_question',
          },
        },

        answerId: {
          type: Sequelize.INTEGER,
          references: {
            model: 'question_answers',
            key: 'id',
          },
        },

        forfaitId: {
          type: Sequelize.INTEGER,
          references: {
            model: 'forfaits',
            key: 'id',
          },
        },
        postId: {
          type: Sequelize.INTEGER,
          references: {
            model: 'posts',
            key: 'id',
          },
        },



        createdAt: {
          allowNull: false,
          type: Sequelize.DATE,
          defaultValue: Sequelize.NOW,
        },
        updatedAt: {
          allowNull: false,
          type: Sequelize.DATE,
          defaultValue: Sequelize.NOW,
        },
      }),
    ])
  },

  down: (queryInterface, Sequelize) => {
    return Promise.all([
        queryInterface.dropTable('logs'),
      ],
    )
  },
}
