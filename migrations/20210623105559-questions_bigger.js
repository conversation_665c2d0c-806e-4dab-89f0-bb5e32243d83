'use strict';

module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.sequelize.transaction(t => {
      return Promise.all([
        queryInterface.changeColumn('questions', 'question', {
          type: Sequelize.TEXT,
          allowNull: true,
        }, { transaction: t }),

        queryInterface.changeColumn('questions', 'explications', {
          type: Sequelize.TEXT,
          allowNull: true,
        }, { transaction: t }),

      ]);
    });
  },
  down: (queryInterface, Sequelize) => {
    return queryInterface.sequelize.transaction(t => {
      return Promise.all([

      ]);
    });
  }
};