'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.addColumn('api_keys', 'userId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      },
    });
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.removeColumn('api_keys', 'userId');
  }
};
