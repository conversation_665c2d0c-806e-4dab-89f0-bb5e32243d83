'use strict';

module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.sequelize.transaction(t => {
      return Promise.all([
        queryInterface.addColumn('statistiques_questions', 'pointsObtained', {
          type: Sequelize.FLOAT,
          defaultValue: null,
        }, { transaction: t }),
      ]);
    });
  },
  down: (queryInterface, Sequelize) => {
    return queryInterface.sequelize.transaction(t => {
      return Promise.all([
        queryInterface.removeColumn('statistiques_questions', 'pointsObtained', { transaction: t }),
      ]);
    });
  }
};