'use strict';

const TABLENAME = 'formation_element'
module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable(TABLENAME, {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      name: {
        type: Sequelize.TEXT('long')
      },
      description: {
        type: Sequelize.TEXT('long')
      },
      image: {
        type: Sequelize.STRING, defaultValue: null
      },

      // ELEMENT TYPE (Cours enrichi, liens, fichier, cours exoteaach, quizz (entité qcm) )
      type: {
        type: Sequelize.STRING,
        defaultValue: null
      },
      // Target type if any
      objectId: {
        type: Sequelize.INTEGER,
        defaultValue: null,
      },

      text: {
        // quill text or title whatever
        type: Sequelize.TEXT('long'),
        defaultValue: null
      },
      order: {
        type: Sequelize.INTEGER,
        defaultValue: null,
      },

      // parent STEP ID
      formationStepId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'formation_step',
          key: 'id',
        },
        defaultValue: null,
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },


      coursId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'cours',
          key: 'id',
        },
        defaultValue: null,
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      mcqId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'qcm',
          key: 'id_qcm',
        },
        defaultValue: null,
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },



      // CreatorId
      authorId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      createdAt: { type: Sequelize.DATE, defaultValue: Sequelize.NOW },
      updatedAt: { type: Sequelize.DATE, defaultValue: Sequelize.NOW },
    });
  },
  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable(TABLENAME);
  }
};